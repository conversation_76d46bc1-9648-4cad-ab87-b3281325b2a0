import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import cat1 from "@/dummyImages/category-image/accessories-cat.jpg";
import cat2 from "@/dummyImages/category-image/body-cat.jpg";
import {
  default as cat3,
  default as cat6,
} from "@/dummyImages/category-image/concealer.jpg";
import cat4 from "@/dummyImages/category-image/eye-cream.jpg";
import cat5 from "@/dummyImages/category-image/sunscreen.jpg";
import { ROUTES } from "@/Routes";
import Image from "next/image";
import Link from "next/link";
import { useContext } from "react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

const category = [
  {
    id: 1,
    image: cat1,
    catName: "Accessories",
  },
  {
    id: 2,
    image: cat2,
    catName: "Body",
  },
  {
    id: 3,
    image: cat3,
    catName: "Concealer",
  },
  {
    id: 4,
    image: cat4,
    catName: "Eye Cream",
  },
  {
    id: 5,
    image: cat5,
    catName: "Body Balm",
  },
  {
    id: 6,
    image: cat6,
    catName: "Body Butter",
  },
  {
    id: 7,
    image: cat2,
    catName: "Body",
  },
  {
    id: 8,
    image: cat3,
    catName: "Concealer",
  },
  {
    id: 9,
    image: cat4,
    catName: "Eye Cream",
  },
  {
    id: 10,
    image: cat5,
    catName: "Body Balm",
  },
  {
    id: 11,
    image: cat6,
    catName: "Body Butter",
  },
];

const FeaturedCategoriesModern = () => {
  const { categories, isCategoriesLoading }: ContextDataType =
    useContext(LocalDataContext);
  return (
    <Swiper
      effect="coverflow"
      grabCursor={true}
      centeredSlides={false}
      slidesPerView={7}
      spaceBetween={10}
      coverflowEffect={{
        rotate: 0,
        stretch: 10,
        depth: 100,
        modifier: 1,
        slideShadows: true,
      }}
      pagination={{
        clickable: true,
        el: ".swiper-pagination",
        type: "bullets",
      }}
      autoplay={{
        delay: 3000,
        disableOnInteraction: false,
      }}
      loop={true}
      breakpoints={{
        1920: {
          slidesPerView: 7,
        },
        1024: {
          slidesPerView: 6,
        },
        768: {
          slidesPerView: 4,
        },
        640: {
          slidesPerView: 4,
        },
        320: {
          slidesPerView: 4,
        },
      }}
      modules={[Autoplay]}
      className="mySwiper"
    >
      {!isCategoriesLoading && categories?.length
        ? categories?.map((cat) => (
            <SwiperSlide key={cat.id}>
              <div className="group rounded-[265px_265px_10px_10px] border border-gray-800 transition-all duration-500 ease-in-out hover:rounded-none">
                <div className="rounded-[265px_265px_10px_10px] p-1 transition-all duration-500 ease-in-out group-hover:rounded-none sm:p-2">
                  <Link href={ROUTES.PRODUCTS.CATEGORY(cat.slug)}>
                    <Image
                      height={100}
                      width={100}
                      // src={cat.image}
                      // alt={cat.catName}
                      src={
                        generateProductImage(cat?.imageUrl) ??
                        "https://t4.ftcdn.net/jpg/04/73/25/49/360_F_473254957_bxG9yf4ly7OBO5I0O5KABlN930GwaMQz.jpg"
                      }
                      alt={cat.name}
                      className="h-auto w-full rounded-[265px_265px_10px_10px] transition-all duration-500 ease-in-out group-hover:rounded-none"
                    />
                  </Link>
                </div>
                <div className="duration-750 relative z-[1] overflow-hidden border-t border-gray-800 transition-all after:absolute after:bottom-0 after:left-[-25%] after:right-0 after:top-[-4%] after:z-[-1] after:block after:h-[150%] after:w-[150%] after:origin-right after:skew-x-[-45deg] after:scale-x-0 after:bg-gray-800 after:transition-transform after:duration-[750ms] after:content-[''] group-hover:after:skew-x-[-45deg] group-hover:after:scale-x-100">
                  <Link
                    href={ROUTES.PRODUCTS.CATEGORY(cat.slug)}
                    className="mb-0 block py-2 text-center text-xs font-semibold capitalize leading-none tracking-[0.6px] text-gray-800 transition-all duration-500 group-hover:text-white sm:py-3 sm:text-sm"
                  >
                    {cat.name}
                  </Link>
                </div>
              </div>
            </SwiperSlide>
          ))
        : ""}
    </Swiper>
  );
};

export default FeaturedCategoriesModern;
