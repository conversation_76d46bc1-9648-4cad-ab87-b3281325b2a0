//banners

import { BASE_URL } from "@/environment/environment";
import axios from "axios";

//brands
export interface GetBrandsResponse {
  message: string;
  results: SingleBrand[];
  status: boolean;
}

export interface GetSingleBrandDetailsResponse {
  message: string;
  result: SingleBrand;
  status: boolean;
}

export interface SingleBrand {
  _id: string;
  name: string;
  slug: string;
  id: number;
  imageUrl: string;
  createdAt: string;
  updatedAt: string;
  description: string;
  metaTitle: string;
  brief: string;
  metaDescription: string;
  __v: number;
}

export const getBrandsListApi = () => {
  return {
    api() {
      return axios
        .get<GetBrandsResponse>(`${BASE_URL}/brands/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return [`getAdminBrandsListApi`];
    },
  };
};

//categories
export interface GetCategoriesResponse {
  message: string;
  results: SingleBrand[];
  status: boolean;
}

export interface GetSingleCategoryDetailsResponse {
  message: string;
  result: SingleCategory;
  status: boolean;
}

export interface SingleCategory {
  _id: string;
  name: string;
  slug: string;
  id: number;
  imageUrl: string;
  createdAt: string;
  updatedAt: string;
  subCategories: SingleSubCategory[];
  productType: string;
  productTypeId: number;
  description: string;
  metaTitle: string;
  brief: string;
  metaDescription: string;
  __v: number;
}

export interface SingleSubCategory {
  _id: string;
  name: string;
  slug: string;
  id: number;
  imageUrl: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export const getCategoryListApi = () => {
  return {
    api() {
      return axios
        .get<GetCategoriesResponse>(`${BASE_URL}/categories/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return [`getAdminCategoryListApi`];
    },
  };
};

export const getSkinTypeListApi = () => {
  return {
    api() {
      return axios
        .get<GetCategoriesResponse>(`${BASE_URL}/skin-types/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return [`getSkinTypeListApi`];
    },
  };
};

export const getSkinConcernListApi = () => {
  return {
    api() {
      return axios
        .get<GetCategoriesResponse>(`${BASE_URL}/skin-concerns/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return [`getSkinConcernListApi`];
    },
  };
};
