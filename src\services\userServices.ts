import axios from "axios";
import { readCookie } from "./apiResource";
import { BASE_URL } from "@/environment/environment";
import { UserDetailsResponse, UserHeaderResponse } from "@/Types/userTypes";
import { OrderSummeryResponseType } from "@/Types/dashboardTypes";

export const getUserDetailsApi = () => {
  return {
    api() {
      return axios
        .get<UserDetailsResponse>(`${BASE_URL}/users/get-details`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getUserDetailsApi"];
    },
  };
};

export const getUserDashboardDetailsApi = () => {
  return {
    api() {
      return axios
        .get<OrderSummeryResponseType>(`${BASE_URL}/users/dashboard`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getUserDashboardDetailsApi"];
    },
  };
};

export const getUserHeaderDetailsApi = () => {
  const accessToken = readCookie();

  const api = axios.create({
    headers: {
      Authorization: `Bearer ${accessToken}`, // Set the Bearer token in the Authorization header
    },
  });

  return {
    api() {
      return api
        .get<UserHeaderResponse>(`${BASE_URL}/users/get-header`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getUserHeaderDetailsApi"];
    },
  };
};

export const updateUserDetailsApi = () => {
  return {
    api(data: any) {
      return axios
        .patch(`${BASE_URL}/users/update-details`, data)
        .then(({ data }) => data);
    },
    getKey() {
      return ["updateUserDetailsApi"];
    },
  };
};
