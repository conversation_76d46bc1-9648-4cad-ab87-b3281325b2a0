"use client";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleBrand } from "@/services/bannerBrandCategoryServices";
import Image from "next/image";
import Link from "next/link";
import { useContext } from "react";
import { generateProductImage } from "../utils/GenerateProductImage";

const BrandsPageOverview = () => {
  const { brands, isBrandsLoading }: ContextDataType =
    useContext(LocalDataContext);
  return (
    <div className="mt-2">
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-6">
        {!isBrandsLoading &&
          brands.map((singleBrand: SingleBrand) => (
            <>
              {singleBrand?.name !== "All" ? (
                <Link
                  href={ROUTES.BRANDS.VIEW(singleBrand.slug)}
                  key={singleBrand.id}
                >
                  <div className="overflow-hidden rounded-lg bg-white px-1 py-2 shadow hover:shadow-lg">
                    {singleBrand.imageUrl ? (
                      <Image
                        height={100}
                        width={100}
                        src={
                          singleBrand.imageUrl
                            ? generateProductImage(singleBrand?.imageUrl)
                            : "https://cdn-icons-png.flaticon.com/512/5309/5309779.png"
                        }
                        alt={singleBrand.name}
                        className="h-20 w-full rounded-lg  transition-transform hover:scale-105"
                      />
                    ) : (
                      <span className="h-20 w-full rounded-lg  text-center transition-transform hover:scale-105">
                        {singleBrand.name}
                      </span>
                    )}
                  </div>
                </Link>
              ) : (
                ""
              )}
            </>
          ))}
      </div>
    </div>
  );
};

export default BrandsPageOverview;
