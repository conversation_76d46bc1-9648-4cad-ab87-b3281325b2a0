import { OrderSummeryType } from "../../Types/dashboardTypes";

interface Props {
  summery?: OrderSummeryType;
}

const SalesSummery = ({ summery }: Props) => {

  const getStatusStyles = (status: string) => {
    switch (status) {
      case "Products":
        return "bg-green-200 text-green-700 shadow-lg";
      case "Total":
        return "bg-blue-200 text-blue-700 shadow-lg";
      case "Pending":
        return "bg-yellow-200 text-yellow-600 shadow-lg";
      case "Confirmed":
        return "bg-orange-200 text-orange-600 shadow-lg";
      case "Delivered":
        return "bg-green-200 text-green-700 shadow-lg";
      case "Cancelled":
        return "bg-red-200 text-red-600 shadow-lg";
      case "Returned":
        return "bg-purple-200 text-purple-700 shadow-lg";
      default:
        return "bg-white text-black shadow-md";
    }
  };

  return (
    <div>
      <div className="grid grid-cols-2 gap-2 md:grid-cols-4 lg:grid-cols-6">
        <div className={`rounded-md border-2 p-2 ${getStatusStyles("Total")}`}>
          <h6 className="text-md mb-4 text-center font-semibold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            Total
          </h6>
          <p className="text-center text-lg font-bold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            {summery?.totalOrderCount}(৳ {summery?.totalOrderAmount})
          </p>
        </div>

        <div
          className={`rounded-md border-2 p-2 ${getStatusStyles("Pending")}`}
        >
          <h6 className="text-md mb-4 text-center font-semibold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            Pending
          </h6>
          <p className="text-center text-lg font-bold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            {summery?.pendingOrderCount}(৳ {summery?.pendingOrderAmount})
          </p>
        </div>

        <div
          className={`rounded-md border-2 p-2 ${getStatusStyles("Confirmed")}`}
        >
          <h6 className="text-md mb-4 text-center font-semibold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            Confirmed
          </h6>
          <p className="text-center text-lg font-bold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            {summery?.inProgressOrderCount}(৳ {summery?.inProgressOrderAmount})
          </p>
        </div>

        <div
          className={`rounded-md border-2 p-2 ${getStatusStyles("Delivered")}`}
        >
          <h6 className="text-md mb-4 text-center font-semibold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            Delivered
          </h6>
          <p className="text-center text-lg font-bold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            {summery?.deliveredOrderCount}(৳ {summery?.deliveredOrderAmount})
          </p>
        </div>

        <div
          className={`rounded-md border-2 p-2 ${getStatusStyles("Cancelled")}`}
        >
          <h6 className="text-md mb-4 text-center font-semibold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            Cancelled
          </h6>
          <p className="text-center text-lg font-bold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            {summery?.cancelledOrderCount}(৳ {summery?.cancelledOrderAmount})
          </p>
        </div>

        <div
          className={`rounded-md border-2 p-2 ${getStatusStyles("Returned")}`}
        >
          <h6 className="text-md mb-4 text-center font-semibold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            Returned
          </h6>
          <p className="text-center text-lg font-bold md:text-xs lg:text-xs xl:text-sm 2xl:text-sm">
            {summery?.refundedOrderCount}(৳ {summery?.refundedOrderAmount})
          </p>
        </div>
      </div>
    </div>
  );
};

export default SalesSummery;
