"use client";
import ImageViewerWithHoverZoom from "@/Components/ReusableComponents/ImageViewerWithHoverZoom/ImageViewerWithHoverZoom";
import Modal from "@/Components/ReusableComponents/Modal/Modal";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { discountPercentage } from "@/Components/utils/PriceCalculator";
import { SingleProductDetailsType } from "@/Types/productTypes";
import Image from "next/image";
import { useEffect, useState } from "react";
import { BiX } from "react-icons/bi";
import { GoScreenFull } from "react-icons/go";

interface Props {
  productDetails: SingleProductDetailsType;
}

const SingleProductImageViewer = ({ productDetails }: Props) => {
  const [isImageFullScreenModalOpen, setIsImageFullScreenModalOpen] =
    useState<boolean>(false);
  const [selectedImage, setSelectedImage] = useState<string>("");
  const { price, discountPrice, mainImageUrl, images, video } = productDetails;

  useEffect(() => {
    setSelectedImage(mainImageUrl);
  }, [mainImageUrl]);

  return (
    <div className="grid w-full grid-cols-12 gap-2 md:gap-0">
      <div
        className="relative col-span-10 flex  w-full  items-center justify-center rounded-xl border-2 border-[#F48F4B]
        bg-[#F4F4F4]
        
        sm:col-span-12 
        md:col-span-12 

        md:h-[300px] 

        lg:h-[330px] 

        xl:h-[400px] 

        2xl:h-[500px] 
        "
      >
        <ImageViewerWithHoverZoom
          imageUrl={
            selectedImage ||
            "https://i.ibb.co/2Y8FbjS/Screenshot-6-removebg-preview.png"
          }
        />
        <div className="absolute right-2 top-2">
          {discountPercentage(price, discountPrice) > 0 ? (
            <p className="rounded-bl-lg rounded-tr-lg bg-primary px-2 py-2  text-base font-semibold text-white">
              {discountPercentage(price, discountPrice)}
              %
              OFF
            </p>
          ) : (
            ""
          )}
        </div>
        <div className="absolute bottom-1 right-2">
          <button
            className="bg-white p-1"
            onClick={() => setIsImageFullScreenModalOpen(true)}
          >
            <GoScreenFull className="bg-white text-2xl" />
          </button>
        </div>
      </div>
      <div className="col-span-2 flex flex-col gap-2 overflow-x-auto  sm:col-span-12 md:col-span-12 md:mt-4 md:flex-row md:items-center md:gap-4">
        {[mainImageUrl, ...images].map((image: string) => (
          <Image
            height={100}
            width={100}
            key={image}
            onClick={() => setSelectedImage(image)}
            src={generateProductImage(image)}
            className="h-[60px] w-[60px] rounded-lg md:h-[60px] md:w-[60px] 2xl:h-[80px] 2xl:w-[80px]"
            alt="Product"
            style={{
              border: selectedImage === image ? "2px solid #F48F4B" : "",
            }}
          />
        ))}
      </div>
      {/* {video ? (
        <div className="hidden md:block">
          <iframe
            width="300"
            height="200"
            src={video?.replace("/watch", "/embed")}
            title="YouTube video player"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          ></iframe>
        </div>
      ) : (
        ""
      )} */}
      <Modal
        showModal={isImageFullScreenModalOpen}
        setShowModal={setIsImageFullScreenModalOpen}
      >
        <div className="z-10 flex h-full w-full items-center justify-center bg-slate-100">
          <div className="h-[60vh] w-[90vw] md:h-[80vh] md:w-auto">
            <div className="flex items-center justify-end">
              <button onClick={() => setIsImageFullScreenModalOpen(false)}>
                <BiX className="text-4xl" />
              </button>
            </div>
            <div className="flex h-full w-full items-center justify-center">
              <Image
                height={100}
                width={100}
                src={
                  generateProductImage(selectedImage) ??
                  "https://i.ibb.co/2Y8FbjS/Screenshot-6-removebg-preview.png"
                }
                className="object-fit h-full w-full"
                alt="Product"
              />
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default SingleProductImageViewer;
