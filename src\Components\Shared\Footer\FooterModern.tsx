import {
  help<PERSON><PERSON><PERSON><PERSON><PERSON>,
  shopAddress,
} from "@/Components/utils/filterOptionData";
import logo from "@/dummyImages/logo-white.png";
import { ROUTES } from "@/Routes";
import Image from "next/image";
import Link from "next/link";
import { BiPhone } from "react-icons/bi";
import { BsInstagram, BsYoutube } from "react-icons/bs";
import { FaFacebook } from "react-icons/fa";
import { IoLocation } from "react-icons/io5";
import { LuDot } from "react-icons/lu";
import { MdEmail } from "react-icons/md";
import { TbMinusVertical } from "react-icons/tb";

const FooterModern = () => {
  return (
    <div>
      <div>
        <div className="bg-primary">
          <div className="px-4 md:px-6 lg:px-20 xl:px-28">
            <div className="grid grid-cols-1 gap-5 border-b border-b-gray-300 py-10 sm:grid-cols-2 lg:grid-cols-4">
              <div className="f1">
                <Link href={ROUTES.HOME}>
                  <Image
                    src={logo}
                    alt="INNI"
                    height={100}
                    width={100}
                    className="h-[55px] w-[200px]"
                  />
                </Link>
                <p className="mt-4 w-[95%] text-white xl:w-[80%]">
                  Real beauty starts truly with your skin and skin Improves
                  Confidence.
                </p>
                <div className="mt-4 flex gap-2">
                  <a
                    href="https://www.facebook.com/koreanshopsBangladesh"
                    target="_blank"
                    className=""
                  >
                    <span className="inline-block rounded bg-white p-2 text-base shadow-sm">
                      <FaFacebook className="text-base text-gray-700" />
                    </span>
                  </a>
                  <a
                    href="https://www.youtube.com/@koreanshopbangladesh"
                    target="_blank"
                    className=""
                  >
                    <span className="inline-block rounded bg-white p-2 text-base shadow-sm">
                      <BsYoutube className="text-base text-gray-700" />
                    </span>
                  </a>
                  <a
                    href="https://www.instagram.com/koreanshopbd/?hl=en"
                    target="_blank"
                    className=""
                  >
                    <span className="inline-block rounded bg-white p-2 text-base shadow-sm">
                      <BsInstagram className="text-base text-gray-700" />
                    </span>
                  </a>
                </div>
              </div>
              <div className="f2">
                <h3 className="text-base font-semibold uppercase text-white">Popular Categories</h3>
                <div className="mt-3 space-y-2">
                  <Link
                    href={ROUTES.HOME}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    Home
                  </Link>
                  <Link
                    href={ROUTES.PRODUCTS.HOME}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    Products
                  </Link>
                  <Link
                    href={ROUTES.BLOGS.HOME}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    Blogs
                  </Link>
                  <Link
                    href={ROUTES.SITEMAP.HOME}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    Sitemap
                  </Link>
                  <Link
                    href={ROUTES.FAQ}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    FAQ
                  </Link>
                </div>
              </div>
              <div className="f3">
                <h3 className="text-base font-semibold uppercase text-white">Customer Services</h3>
                <div className="mt-3 space-y-2">
                  <Link
                    href={ROUTES.ABOUT_US}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    About Us
                  </Link>
                  <Link
                    href={ROUTES.CONTACT_US}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    Contact Us
                  </Link>
                  <Link
                    href={ROUTES.RETURN_AND_REFUND}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    Return And Refund
                  </Link>
                  <Link
                    href={ROUTES.SHIPPING_AND_DELIVERY}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    Shipping And Delivery
                  </Link>
                  <Link
                    href={ROUTES.STORE_LOCATION}
                    className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                  >
                    <LuDot />
                    Store Location
                  </Link>
                </div>
              </div>
              <div className="f4">
                <h3 className="text-base font-semibold uppercase text-white">Contact Us</h3>
                <div className="mt-3 space-y-2">
                  <div className="flex items-center gap-2">
                    <BiPhone className="text-lg text-secondary" />
                    <a
                      href="tel:+8801303779646"
                      target="_blank"
                      className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                    >
                      {helpLineNumber}
                    </a>
                  </div>
                  <div className="flex items-center gap-2">
                    <MdEmail className="text-lg text-secondary" />
                    <a
                      href="mailto:<EMAIL>"
                      target="_blank"
                      className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary"
                    >
                      koreantrendymall.com
                    </a>
                  </div>
                  <div className="flex items-start gap-2">
                    <div>
                      <IoLocation className="text-lg text-secondary" size={20} />
                    </div>
                    <span className="flex transform cursor-pointer items-center gap-1 text-base text-white transition-all duration-700 ease-in-out hover:scale-105 hover:text-secondary">
                      {shopAddress}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mb-[3rem] md:mb-0 flex flex-col md:flex-row items-center justify-between py-3 sm:py-4 px-4 md:px-6 lg:px-20 xl:px-28 space-y-3 md:space-y-0">
          <div>
            <p className="text-sm text-gray-700">
              © {new Date().getFullYear()} All rights reserved.{" "}
              <span className="text-secondary">INNI.</span>
            </p>
          </div>

          <div>
            <span>Developed By</span>
            <Link
              href="https://softs.ai/"
              target="_blank"
              className="ml-2 hover:text-blue-600 hover:underline"
            >
              SOFTS.AI
            </Link>
          </div>

          <div className="flex flex-wrap items-center gap-x-2 gap-y-1 text-sm text-gray-700">
            <Link
              href={ROUTES.TERMS_AND_CONDITIONS}
              className="hover:scale-105 hover:text-secondary transition-all duration-700 ease-in-out"
            >
              Terms And Conditions
            </Link>
            <TbMinusVertical className="text-gray-700" />
            <Link
              href={ROUTES.PRIVACY_POLICY}
              className="hover:scale-105 hover:text-secondary transition-all duration-700 ease-in-out"
            >
              Privacy Policy
            </Link>
          </div>
        </div>

      </div>
    </div>
  );
};

export default FooterModern;
