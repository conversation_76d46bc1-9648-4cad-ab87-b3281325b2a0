"use client";
import ReactHotToast from "@/Components/ReusableComponents/Toast/ReactHotToast";
import { defaultQueryClient } from "@/services/util";
import { QueryClientProvider } from "@tanstack/react-query";
import { SessionProvider } from "next-auth/react";
import React, { ReactNode } from "react";

interface Props {
  children: ReactNode;
}

const Providers = (props: Props) => {
  const [queryClient] = React.useState(() => defaultQueryClient);
  return (
    <SessionProvider>
      <QueryClientProvider client={queryClient}>
        <ReactHotToast />
        {props.children}
      </QueryClientProvider>
    </SessionProvider>
  );
};

export default Providers;
