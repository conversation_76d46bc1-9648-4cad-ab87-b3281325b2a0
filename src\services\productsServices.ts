import {
  ProductListType,
  RelatedProductListType,
  SingleProductResponse,
} from "@/Types/productTypes";
import { BASE_URL } from "@/environment/environment";
import axios from "axios";

interface filters {
  size?: number;
  page?: number;
  productCategory?: string;
  subCategory?: string;
  productType?: string;
  skinType?: string;
  brand?: string;
  skinConcern?: string;
  isTopSelling?: string;
  isFeatured?: string;
  isCombo?: string;
  isOnSale?: string;
  isAvailable?: string;
}
export const getProductListApi = ({
  productCategory,
  subCategory,
  productType,
  skinType,
  brand,
  skinConcern,
  isTopSelling,
  isFeatured,
  isCombo,
  isOnSale,
  isAvailable,
  size,
  page,
}: filters) => {
  const queryParams = new URLSearchParams();

  if (size) queryParams.append("size", size.toString());
  if (page) queryParams.append("page", page.toString());
  if (productCategory) queryParams.append("productCategory", productCategory);
  if (subCategory) queryParams.append("subCategory", subCategory);
  if (productType) queryParams.append("productType", productType);
  if (skinType) queryParams.append("skinType", skinType);
  if (brand) queryParams.append("brand", brand);
  if (skinConcern) queryParams.append("skinConcern", skinConcern);
  if (isTopSelling) queryParams.append("isTopSelling", isTopSelling);
  if (isFeatured) queryParams.append("isFeatured", isFeatured);
  if (isCombo) queryParams.append("isCombo", isCombo);
  if (isOnSale) queryParams.append("isOnSale", isOnSale);
  if (isAvailable) queryParams.append("isAvailable", isAvailable);

  const filter = queryParams.toString();

  return {
    api() {
      return axios
        .get<ProductListType>(`${BASE_URL}/products/get-all?${filter}`)
        .then(({ data }) => data);
    },
    getKey() {
      return [
        `getProductList`,
        productCategory,
        subCategory,
        productType,
        skinType,
        brand,
        skinConcern,
        isTopSelling,
        isFeatured,
        isCombo,
        isOnSale,
        isAvailable,
        size,
        page,
      ];
    },
  };
};

export const getHomePageProductListApi = ({
  productType,
  size = "10",
}: {
  productType: string;
  size?: string;
}) => {
  const queryParams = new URLSearchParams({
    productType: productType ?? "",
    size: size ?? "10",
  });

  const filter = queryParams.toString();

  return {
    api() {
      return axios
        .get<ProductListType>(`${BASE_URL}/products/home/<USER>
        .then(({ data }) => data);
    },
    getKey() {
      return [`getHomePageProductListApi`, productType];
    },
  };
};

export const getHomePageContentListApi = () => {
  return {
    api() {
      return axios
        .get<GetAdminHomeContentListResponse>(`${BASE_URL}/homepage/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return [`getHomePageContentListApi`];
    },
  };
};

export interface GetAdminHomeContentListResponse {
  message: string;
  results: SingleHomeContent[];
  status: boolean;
}

export interface SingleHomeContent {
  _id: string;
  selectedType: string;
  selectedOption: string;
  imageUrl: string;
  imageAlt: string;
  metaTitle: string;
  metaDescription: string;
  keywords: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  __v: number;
}

export const getOfferProductListApi = () => {
  const queryParams = new URLSearchParams();
  queryParams.append("size", "20");
  queryParams.append("page", "1");
  queryParams.append("productType", "skin care");

  const filter = queryParams.toString();

  return {
    api() {
      return axios
        .get<ProductListType>(`${BASE_URL}/products/get-offers?${filter}`)
        .then(({ data }) => data);
    },
    getKey() {
      return [`getOfferProductListApi`];
    },
  };
};

export const getRelatedProductListApi = ({
  slug,
  category,
  brand,
}: {
  slug: string;
  category: string;
  brand: string;
}) => {
  const queryParams = new URLSearchParams({
    slug: slug ?? "",
    productCategory: category ?? "",
    brand: brand ?? "",
  });

  const filter = queryParams.toString();

  return {
    api() {
      return axios
        .get<RelatedProductListType>(
          `${BASE_URL}/products/get-related-products?${filter}`,
        )
        .then(({ data }) => data);
    },
    getKey() {
      return [`getRelatedProductListApi`, slug, category, brand];
    },
  };
};

export const getSingleProductDetailsApi = (slug: string) => {
  return {
    api() {
      return axios
        .get<SingleProductResponse>(`${BASE_URL}/products/single/${slug}`)
        .then(({ data }) => data);
    },
    getKey() {
      return [`getSingleProductDetailsApi`, slug];
    },
  };
};

export const getSearchProductListApi = (query: string) => {
  return {
    api() {
      return axios
        .get<ProductListType>(`${BASE_URL}/products/search?query=${query}`)
        .then(({ data }) => data);
    },
    getKey() {
      return [`getSearchProductListApi`, query];
    },
  };
};
