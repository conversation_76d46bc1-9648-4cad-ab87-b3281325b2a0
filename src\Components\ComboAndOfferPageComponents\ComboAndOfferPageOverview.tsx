"use client";
import { ProductListType } from "@/Types/productTypes";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import CustomPagination from "../ReusableComponents/PaginationComponent/CustomPagination";
import ProductsListViewer from "../ReusableComponents/ProductsListViewer/ProductsListViewer";

interface Props {
  accessToken: string;
  data: ProductListType;
  url: string;
  imageUrl: string;
}

const ComboAndOfferPageOverview = ({
  accessToken,
  data,
  url,
  imageUrl,
}: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);

  const page = searchParams.get("page") || 1;

  useEffect(() => {
    if (data) {
      setIsLoading(false);
    }
  }, [data]);

  const handlePageChange = (value: number) => {
    router.push(`/${url}?page=${value}`);
  };
  return (
    <div>
      {/* <Image
        height={100}
        width={100}
        src={imageUrl}
        alt="Carousel Image 1"
        className="mb-4 mt-2 w-full"
      /> */}
      <ProductsListViewer
        productList={data.results}
        loading={isLoading}
        accessToken={accessToken ? accessToken : ""}
        xxl={6}
        xl={5}
        lg={4}
        md={3}
        sm={2}
      />
      {Number(data?.totalNoOfPages) > 1 ? (
        <CustomPagination
          currentPage={Number(page)}
          totalPages={Number(data?.totalNoOfPages)}
          handlePageChange={handlePageChange}
        />
      ) : (
        ""
      )}
    </div>
  );
};

export default ComboAndOfferPageOverview;
