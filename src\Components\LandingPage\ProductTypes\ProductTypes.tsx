"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { SingleBanner } from "@/services/bannerServices";
import Image from "next/image";
import Link from "next/link";
import { useContext, useEffect, useState } from "react";

interface fakeDataType {
  id: number;
  name: string;
  image: string;
}

const ProductTypes = () => {
  const { banners, isBannersLoading }: ContextDataType =
    useContext(LocalDataContext);
  const [rightBanners, setRightBanners] = useState<SingleBanner[]>();
  useEffect(() => {
    if (banners) {
      const right = banners.filter(
        (single: SingleBanner) => single.bannerType === "bottom",
      );
      setRightBanners(right);
    }
  }, [banners]);
  return (
    <div className="grid grid-cols-12 gap-1 md:gap-2">
      {!isBannersLoading &&
        rightBanners?.length &&
        rightBanners.map((singleType: SingleBanner) => (
          <Link
            href={`/${singleType?.redirectType}/${singleType?.redirectSlug}`}
            key={singleType._id}
            className="col-span-6 md:col-span-3"
          >
            <div>
              <Image
                src={generateProductImage(singleType.imageUrl)}
                alt="product image"
                height={100}
                width={100}
                className="h-[80px] w-full transform rounded-lg p-1 transition-all duration-700 ease-in-out hover:scale-95 md:h-[150px] md:p-2 lg:h-[200px] xl:h-[200px] 2xl:h-[250px]"
              />
            </div>
          </Link>
        ))}
    </div>
  );
};

export default ProductTypes;
