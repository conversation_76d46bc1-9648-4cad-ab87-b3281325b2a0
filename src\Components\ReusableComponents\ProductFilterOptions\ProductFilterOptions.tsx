import {
  Option,
  skinConcernsList,
  skinTypesList,
} from "@/Components/utils/filterOptionData";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import {
  SingleBrand,
  SingleSubCategory,
} from "@/services/bannerBrandCategoryServices";
import { useRouter, useSearchParams } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import CustomDropdown from "../CustomDropdown/CustomDropdown";

interface Props {
  pageType: string;
  brandName?: string;
  categoryName?: string;
  subCategoryName?: string;
}

const ProductFilterOptions = ({
  pageType,
  brandName,
  categoryName,
  subCategoryName,
}: Props) => {
  const {
    brands,
    categories,
    skinTypes,
    skinConcerns,
    isBrandsLoading,
    isCategoriesLoading,
  }: ContextDataType = useContext(LocalDataContext);

  const router = useRouter();
  const searchParams = useSearchParams();

  const [brandsList, setBrandsList] = useState<Option[]>([]);
  const [categoriesList, setCategoriesList] = useState<Option[]>([]);
  const [subCategoriesList, setSubCategoriesList] = useState<Option[]>([]);
  const [skinTypesOptions, setSkinTypesOptions] = useState<Option[]>([]);
  const [skinConcernsOptions, setSkinConcernsOptions] = useState<Option[]>([]);

  const brand =
    pageType === "brand" ? brandName : searchParams.get("brand") || "";
  const productType = searchParams.get("productType") || "";
  const category =
    pageType === "category" || pageType === "subCategory"
      ? categoryName
      : searchParams.get("category") || "";
  const subCategory =
    pageType === "subCategory"
      ? subCategoryName
      : searchParams.get("subCategory") || "";
  const skinType = searchParams.get("skinType") || "";
  const skinConcern = searchParams.get("skinConcern") || "";

  const handleFilterChange = (key: string, value: string) => {
    const query: Record<string, string> = {
      page: "1",
      ...(brand && { brand }),
      ...(productType && { productType }),
      ...(category && { category }),
      ...(subCategory && { subCategory }),
      ...(skinType && { skinType }),
      ...(skinConcern && { skinConcern }),
      [key]: value,
    };

    // Remove keys with empty string values
    Object.keys(query).forEach((k) => query[k] === "" && delete query[k]);

    if (pageType === "new-arrival") {
      router.push(`/new-arrival?${new URLSearchParams(query).toString()}`);
    } else if (pageType === "best-seller") {
      router.push(`/best-seller?${new URLSearchParams(query).toString()}`);
    } else {
      router.push(`/products?${new URLSearchParams(query).toString()}`);
    }
  };

  useEffect(() => {
    if (brands) {
      setBrandsList(
        brands.map((single: SingleBrand) => ({
          value: single.slug,
          label: single.name,
        })),
      );
    }
  }, [brands]);

  useEffect(() => {
    if (categories) {
      setCategoriesList(
        categories.map((single: SingleBrand) => ({
          value: single.slug,
          label: single.name,
        })),
      );
    }
  }, [categories]);

  useEffect(() => {
    if (skinTypes) {
      setSkinTypesOptions(
        skinTypes.map((single: SingleBrand) => ({
          value: single.name,
          label: single.name,
        })),
      );
    }
  }, [skinTypes]);

  useEffect(() => {
    if (skinConcerns) {
      setSkinConcernsOptions(
        skinConcerns.map((single: SingleBrand) => ({
          value: single.name,
          label: single.name,
        })),
      );
    }
  }, [skinConcerns]);

  useEffect(() => {
    if (category && categories) {
      const selectedCategory = categories.find(
        (single: SingleBrand) => single.slug === category,
      );
      if (selectedCategory?.subCategories) {
        setSubCategoriesList(
          selectedCategory.subCategories.map((sin: SingleSubCategory) => ({
            value: sin.slug,
            label: sin.name,
          })),
        );
      }
    }
  }, [categories, category]);

  const allType: Option = { value: "", label: "All", image: "" };

  return (
    <div className="mb-5 flex items-center gap-4 rounded-md border p-4 shadow">
      <h2 className="whitespace-nowrap text-lg font-bold">Filter Options:</h2>
      <div className="grid w-full grid-cols-1 gap-4 md:grid-cols-4">
        <CustomDropdown
          options={brandsList.length ? [allType, ...brandsList] : []}
          selectedValue={brand ?? ""}
          onSelect={(val) => handleFilterChange("brand", val)}
          width="200px"
          placeholder="Select brand"
        />
        <CustomDropdown
          options={categoriesList.length ? [allType, ...categoriesList] : []}
          selectedValue={category ?? ""}
          onSelect={(val) => handleFilterChange("category", val)}
          width="200px"
          placeholder="Select category"
        />
        {/* <CustomDropdown
          options={
            subCategoriesList.length ? [allType, ...subCategoriesList] : []
          }
          selectedValue={subCategory ?? ""}
          onSelect={(val) => handleFilterChange("subCategory", val)}
          width="200px"
          placeholder="Select sub-category"
        /> */}
        <CustomDropdown
          options={
            skinTypesOptions.length ? [allType, ...skinTypesOptions] : []
          }
          selectedValue={skinType}
          onSelect={(val) => handleFilterChange("skinType", val)}
          width="200px"
          placeholder="Select skin type"
        />
        <CustomDropdown
          options={
            skinConcernsOptions.length ? [allType, ...skinConcernsOptions] : []
          }
          selectedValue={skinConcern}
          onSelect={(val) => handleFilterChange("skinConcern", val)}
          width="200px"
          placeholder="Select skin concern"
        />
      </div>
    </div>
  );
};

export default ProductFilterOptions;