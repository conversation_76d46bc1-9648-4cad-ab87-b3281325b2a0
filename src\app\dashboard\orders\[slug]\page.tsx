import OrderDetailsOverview from "@/Components/OrderDetails/OrderDetailsOverview";
import UserDashboardLayout from "@/Layouts/UserDashboardLayout";
import { ROUTES } from "@/Routes";
import { OrdersDetailsResponse } from "@/Types/orderTypes";
import { BASE_URL } from "@/environment/environment";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound, redirect } from "next/navigation";
import React from "react";

interface Props {
  params: Promise<{ slug: string }>;
}

export const metadata: Metadata = {
  title: "Order Details | INNI",
};

const OrderDetailsPage = async ({ params }: Props) => {
  const accessToken = (await cookies()).get("GMK")?.value;
  const { slug } = await params;
  const data: OrdersDetailsResponse = await getData(slug);

  if (!accessToken) {
    redirect(ROUTES.LOG_IN(ROUTES.DASHBOARD.ORDERS.VIEW(slug)));
    return null;
  }
  return (
    <UserDashboardLayout>
      <OrderDetailsOverview orderDetails={data.result} />
    </UserDashboardLayout>
  );
};

export default OrderDetailsPage;

async function getData(orderId: string) {
  const res = await fetch(`${BASE_URL}/orders/single/${orderId}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}
