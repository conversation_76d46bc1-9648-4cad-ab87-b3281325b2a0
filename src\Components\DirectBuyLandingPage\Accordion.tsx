"use client";
import { useState } from "react";
interface Props {
  title: string;
  children: any;
}

const Accordion = ({ title, children }: Props) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="mb-3 rounded-md border border-gray-300">
      <div
        className="flex cursor-pointer items-center justify-between p-2 md:p-4"
        onClick={toggleAccordion}
      >
        <h3 className="text-sm font-semibold md:text-lg">{title}</h3>
        <div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-6 w-6 transform transition-transform ${isOpen ? "rotate-180" : "rotate-0"}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </div>
      {isOpen && <div className="border-t border-gray-300 p-4">{children}</div>}
    </div>
  );
};

export default Accordion;
