export const ROUTES = {
  HOME: "/",
  LOG_IN: (redirectUrl?: string) =>
    `/auth/login?redirect=${redirectUrl ? redirectUrl : ""}`,
  REGISTRATION: "/auth/registration",
  ABOUT_US: "/about-us",
  CONTACT_US: "/contact-us",
  FAQ: "/faq",
  VIDEOS: "/videos",
  RETURN_AND_REFUND: "/return-and-refund",
  SHIPPING_AND_DELIVERY: "/shipping-and-delivery",
  TERMS_AND_CONDITIONS: "/terms-and-conditions",
  PRIVACY_POLICY: "/privacy-policy",
  STORE_LOCATION: "/store-location",
  CART: "/cart",
  CHECKOUT: "/checkout",
  WISHLIST: "/wishlist",
  TRACK_ORDER: "/track-order",
  SUCCESS: (id: string) => `/success/${id}`,
  SEARCH: (searchQuery?: string) =>
    `/search?searchQuery=${searchQuery ? searchQuery : ""}`,
  PRODUCTS: {
    HOME: "/products?page=1",
    VIEW: (slug: string | number) => `/product/${slug}`,
    BUY: (slug: string | number) => `/buy/${slug}`,
    TYPE: (slug: string) => `/products?productType=${slug}`,
    CATEGORY: (slug: string) => `/category/${slug}`,
    SUB_CATEGORY: (category: string, subCategory: string) =>
      `/category/${category}/${subCategory}`,
    SKIN_TYPE: (slug: string) => `/products?skinType=${slug}`,
    SKIN_CONCERN: (slug: string) => `/products?skinConcern=${slug}`,
  },
  BRANDS: {
    HOME: "/brands",
    VIEW: (slug: string | number) => `/brand/${slug}`,
  },
  BLOGS: {
    HOME: "/blogs",
    VIEW: (slug: string | number) => `/blogs/${slug}`,
  },
  SITEMAP: {
    HOME: "/sitemap",
  },
  DASHBOARD: {
    HOME: "/dashboard",
    PROFILE: "/dashboard/profile",
    ACCOUNT: "/dashboard/account",
    ORDERS: {
      HOME: "/dashboard/orders",
      VIEW: (slug: string | number) => `/dashboard/orders/${slug}`,
    },
    SUPPORT: {
      HOME: "/dashboard/support",
      // VIEW: (slug: string | number) => `/dashboard/orders/${slug}`,
    },
  },
} as const;
