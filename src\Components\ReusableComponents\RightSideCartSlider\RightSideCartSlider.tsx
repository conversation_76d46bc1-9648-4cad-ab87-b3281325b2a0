"use client";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { MdClose } from "react-icons/md";
import NoUserRightSideCartComponent from "./NoUserRightSideCartComponent";
import UserRightSideCartComponent from "./UserRightSideCartComponent";

interface Props {
  openCartSlider: boolean;
  setOpenCartSlider: (e: any) => void;
  accessToken: string;
}
const RightSideCartSlider = ({
  openCartSlider,
  setOpenCartSlider,
  accessToken,
}: Props) => {
  return (
    <Transition.Root show={openCartSlider} as={Fragment}>
      <Dialog
        as="div"
        className="relative"
        onClose={setOpenCartSlider}
        style={{ zIndex: 999 }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto relative w-screen max-w-md">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-in-out duration-500"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in-out duration-500"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <div />
                  </Transition.Child>
                  <div className="flex h-full flex-col overflow-y-auto bg-[#F7F5FB] py-2 shadow-xl">
                    <div className="px-4 sm:px-6">
                      <div className="flex items-center justify-between">
                        <Dialog.Title className="text-base font-semibold leading-6 text-gray-900 mb-4">
                          Cart
                        </Dialog.Title>
                        <div>
                          <button onClick={() => setOpenCartSlider(false)}>
                            <MdClose />
                          </button>
                        </div>
                      </div>
                    </div>
                    <div>
                      {accessToken ? (
                        <UserRightSideCartComponent
                          accessToken={accessToken}
                          setOpenCartSlider={setOpenCartSlider}
                        />
                      ) : (
                        <NoUserRightSideCartComponent
                          setOpenCartSlider={setOpenCartSlider}
                        />
                      )}
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default RightSideCartSlider;
