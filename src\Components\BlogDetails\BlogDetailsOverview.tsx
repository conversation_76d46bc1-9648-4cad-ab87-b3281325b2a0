import { ROUTES } from "@/Routes";
import { BlogDescription, SingleBlogDetails } from "@/services/blogServices";
import Image from "next/image";
import Link from "next/link";
import { generateProductImage } from "../utils/GenerateProductImage";

interface Props {
  data?: SingleBlogDetails;
  relatedBlogs?: SingleBlogDetails[];
}

const BlogDetailsOverview = ({ data, relatedBlogs }: Props) => {
  return (
    <div>
      <div>
        <div className="my-2 flex flex-col items-center justify-center">
          <div className="text-center text-2xl font-bold">{data?.title}</div>
          <div className="flex items-center gap-4 text-slate-400">
            <span>By {data?.created_by}</span>
            <span>|</span>
            <span>
              {new Date(data?.createdAt ?? new Date()).toDateString()}
            </span>
            {/* <span>|</span>
            <span>4 Comments</span> */}
          </div>
        </div>
        <div>
          <Image
            src={generateProductImage(data?.image ?? "")}
            height={100}
            width={100}
            alt="Carousel Image 1"
            className="h-[200px] w-full rounded-lg md:h-[300px]"
            priority={true}
          />
        </div>
        <div className="mt-2 flex gap-2 rounded-xl border-2 border-[#092143] p-2">
          <div>
            <svg
              width="29"
              height="24"
              viewBox="0 0 29 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.39166 0.396131H12.6729V5.42801H11.2073C10.7454 5.39447 10.2823 5.4675 9.84663 5.64255C9.41101 5.81759 9.01217 6.09093 8.67479 6.44562C8.33742 6.80032 8.06872 7.22882 7.88537 7.70453C7.70201 8.18024 7.60789 8.69305 7.60886 9.21115V10.215H12.6729V21.9806C12.6729 22.2379 12.6276 22.4926 12.5396 22.7302C12.4517 22.9679 12.3228 23.1839 12.1603 23.3658C11.9978 23.5477 11.8049 23.692 11.5926 23.7904C11.3803 23.8889 11.1527 23.9395 10.9229 23.9395C10.9229 23.7191 11.0323 23.3396 10.737 23.4498C10.7008 23.4696 10.6709 23.5014 10.6513 23.5408C10.6318 23.5802 10.6235 23.6253 10.6276 23.6702C10.6283 23.7686 10.6591 23.8636 10.7151 23.9395C7.98444 23.9395 5.3655 22.7261 3.43363 20.5659C1.50176 18.4057 0.415008 15.4754 0.412109 12.4188V9.33355C0.414564 8.83733 0.451103 8.34204 0.521456 7.85211C0.596772 7.74311 0.699703 7.66205 0.816786 7.61949C0.776251 7.6431 0.731236 7.65543 0.685543 7.65543C0.63985 7.65543 0.594835 7.6431 0.5543 7.61949C0.796524 5.83791 1.56698 4.1993 2.74177 2.96714C2.74177 3.16303 2.74176 3.39566 3.03706 3.35893C3.33237 3.3222 3.09174 2.88147 2.78549 2.9182C3.41457 2.29272 4.12671 1.78057 4.89639 1.40007C4.92105 1.47313 4.97062 1.53217 5.03421 1.56432C5.0978 1.59646 5.17021 1.59902 5.23548 1.57142C5.30075 1.54382 5.35353 1.48836 5.38224 1.41718C5.41096 1.34601 5.41324 1.26494 5.38859 1.19188C6.66852 0.655994 8.02514 0.386347 9.39166 0.396131Z"
                fill="#092143"
              />
              <path
                d="M23.1069 9.17427V10.1782H28.1709V14.4021C27.7991 14.4021 27.8756 14.855 28.0944 14.953C28.1093 14.9015 28.1356 14.8552 28.1709 14.8183V17.8179C28.1163 17.5852 28.0615 17.3526 27.799 17.3649C27.7444 17.524 27.6459 17.6465 27.6022 17.8056C27.7444 17.928 27.9631 18.1362 28.1709 17.9281V21.9438C28.1709 22.4633 27.9865 22.9616 27.6584 23.329C27.3302 23.6963 26.8851 23.9027 26.421 23.9027H26.0928C23.3632 23.9027 20.7454 22.6889 18.8153 20.5283C16.8851 18.3678 15.8008 15.4374 15.8008 12.3819V9.29667C15.8008 3.64038 19.7382 0.359253 24.9116 0.359253H28.1928V5.39113H26.6506C26.1921 5.36085 25.7329 5.4366 25.3016 5.61359C24.8703 5.79058 24.4761 6.06507 24.1437 6.41996C23.8113 6.77484 23.5477 7.20254 23.3693 7.67639C23.1909 8.15025 23.1016 8.6601 23.1069 9.17427Z"
                fill="#092143"
              />
            </svg>
          </div>
          <p>{data?.brief}</p>
        </div>
        <div>
          {data?.descriptions?.map((single: BlogDescription) => (
            <div className="mt-2" key={single.image}>
              <div
                dangerouslySetInnerHTML={{
                  __html: single?.content
                    ? single?.content
                    : "<p>No content</p>",
                }}
              />
              <div className="flex w-full items-center justify-center">
                {single.image ? (
                  <Image
                    src={
                      single.image
                        ? generateProductImage(single?.image ?? "")
                        : "https://i.ibb.co/7SyJCfg/photo-2024-02-18-22-42-01.jpg"
                    }
                    height={100}
                    width={100}
                    alt="Carousel Image 1"
                    className="h-[400px] w-1/2 rounded"
                    priority={true}
                  />
                ) : (
                  ""
                )}
              </div>
            </div>
          ))}
        </div>
        <div className="mt-2">
          <h2 className="my-2 text-center text-xl font-bold">Related Posts</h2>
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            {relatedBlogs?.map((single: SingleBlogDetails) => (
              <Link href={ROUTES.BLOGS.VIEW(single.id)} key={single?.id}>
                {/* ROUTES.BLOGS.VIEW(1) */}
                <div className="rounded-b-2xl bg-slate-200">
                  <Image
                    src={generateProductImage(single.image)}
                    height={100}
                    width={100}
                    alt="Carousel Image 1"
                    className="h-[150px] w-full"
                  />
                  <div className="p-4">
                    <span>{single?.title}</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogDetailsOverview;


