import { SingleOrderDetails } from "@/Types/orderTypes";
import { SingleProductOfOrderOrCheckout } from "@/Types/public-types";
import { Document, Font, Image, Page, Text, View } from "@react-pdf/renderer";
import { styles } from "./InvoicePdfStyles";
import {
  helpLineNumber,
  shopAddress,
} from "@/Components/utils/filterOptionData";

Font.register({
  family: "Open Sans",
  fonts: [
    {
      src: "https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-regular.ttf",
    },
    {
      src: "https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-600.ttf",
      fontWeight: 800,
    },
  ],
});

interface Props {
  orderDetails: SingleOrderDetails;
}

const InvoicePdf = ({ orderDetails }: Props) => {
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.container}>
          <View style={styles.section}>
            <Text style={styles.invoiceText}>INVOICE</Text>
            <Text style={styles.invoiceNo}>
              INVOICE NO : {orderDetails?.orderId}
            </Text>

            <View style={styles.flexAndGap}>
              <Text style={styles.orderDate}>
                {new Date(orderDetails?.createdAt || "")
                  .toDateString()
                  .toUpperCase()}
              </Text>
              <Text style={styles.orderDate}>| </Text>
              <Text style={styles.orderDate}>
                {new Date(orderDetails?.createdAt || "").toLocaleTimeString()}
              </Text>
            </View>
            {/* <Image src={barcode} style={styles.barcode} /> */}
          </View>
          <View style={styles.sectionRight}>
            <Image
              src="https://i.ibb.co.com/KWwH9nq/logo-8bf80236.png"
              style={styles.companyLogo}
            />
            <Text style={styles.companyAddress}>{shopAddress}</Text>
            <Text style={styles.companyAddress}>Phone : {helpLineNumber}</Text>
            <Text style={styles.companyOtherTexts}>
              https://koreanshopbd.com/
            </Text>
            <Text style={styles.companyOtherTexts}>
              https://www.facebook.com/koreanshopsBangladesh
            </Text>
          </View>
        </View>
        <View style={styles.bottomPartContainer}>
          <View style={styles.invoiceToContainer}>
            <Text style={styles.invoiceToText}>INVOICE TO</Text>
            <Text style={styles.customerName}>{orderDetails?.name}</Text>
            <View style={styles.phoneNumberContainer}>
              <Text style={styles.text}>Phone :</Text>
              <Text style={styles.phoneNumber}>
                {orderDetails?.phoneNumber}
              </Text>
            </View>
            <View style={styles.addressContainer}>
              <Text style={styles.text}>Address :</Text>
              <Text style={styles.address}>
                {orderDetails?.deliveryAddress}
              </Text>
            </View>
            <Text style={styles.note}>
              Note :{" "}
              {orderDetails?.notes[0]
                ? orderDetails?.notes[0].message
                : "No Notes Available"}
            </Text>
            <Text style={styles.note}>Delivery Partner : Pathao</Text>
          </View>
          <View style={styles.productsTable}>
            <View style={styles.tableHeader}>
              <Text style={styles.numberTitle}>#</Text>
              <Text style={styles.nameTitle}>Product</Text>
              <Text style={styles.productPrice}>Price</Text>
              <Text style={styles.productQuantity}>QTY</Text>
              <Text style={styles.productTotal}>Total</Text>
            </View>
            <View>
              {orderDetails?.products?.map(
                (
                  singleProduct: SingleProductOfOrderOrCheckout,
                  index: number,
                ) => (
                  <View key={singleProduct?._id} style={styles.productRow}>
                    <Text style={styles.numberTitle}>{index + 1}.</Text>
                    <Text style={styles.productName}>
                      {singleProduct?.name}
                    </Text>
                    <Text style={styles.productPrice}>
                      {singleProduct?.price}
                    </Text>
                    <Text style={styles.productQuantity}>
                      {singleProduct?.quantity}
                    </Text>
                    <Text style={styles.productTotal}>
                      {singleProduct?.price * singleProduct?.quantity}
                    </Text>
                  </View>
                ),
              )}
            </View>
            <View style={styles.flexBetween}>
              <View>
                <View style={styles.duePart}>
                  <Text style={styles.payableAmount}>
                    PAYABLE AMOUNT :{" "}
                    {numberToWords(
                      Number(orderDetails?.dueAmount),
                    ).toLocaleUpperCase()}{" "}
                    TAKA ONLY
                  </Text>
                  <Text style={styles.dueOrPaid}>
                    {Number(orderDetails?.dueAmount) > 0 ? "DUE" : "PAID"}
                  </Text>
                  <Text style={styles.printed}>
                    PRINTED BY : {orderDetails?.name.toUpperCase()}
                  </Text>
                  <Text style={styles.printed}>
                    PRINTED AT : {new Date().toDateString().toUpperCase()}{" "}
                    {new Date().toLocaleTimeString()}
                  </Text>
                  <Text style={styles.systemMessage}>
                    ** THIS IS A GENERATE INVOICE, NO NEED TO HAVE SIGNATURE **
                  </Text>
                </View>
              </View>
              <View style={{ width: "150px", marginTop: "0px" }}>
                <View style={styles.subTotal}>
                  <Text style={styles.bottomBillingTextLeft}>Subtotal</Text>
                  <Text style={styles.bottomBillingTextRight}>
                    {orderDetails?.productPrice}
                  </Text>
                </View>
                <View style={styles.subTotal}>
                  <Text style={styles.bottomBillingTextLeft}>Discount</Text>
                  <Text style={styles.bottomBillingTextRight}>
                    {Number(orderDetails?.discount) +
                      Number(orderDetails?.adminDiscount)}
                  </Text>
                </View>
                <View style={styles.subTotal}>
                  <Text style={styles.bottomBillingTextLeft}>
                    Delivery Charge
                  </Text>
                  <Text style={styles.bottomBillingTextRight}>
                    {orderDetails?.deliveryCharge}
                  </Text>
                </View>
                <View style={styles.subTotal}>
                  <Text style={styles.bottomBillingTextLeft}>Total</Text>
                  <Text style={styles.bottomBillingTextRight}>
                    {orderDetails?.totalAmount}
                  </Text>
                </View>
                <View style={styles.subTotal}>
                  <Text style={styles.bottomBillingTextLeft}>Paid Amount</Text>
                  <Text style={styles.bottomBillingTextRight}>
                    {orderDetails?.paidAmount}
                  </Text>
                </View>
                <View style={styles.subTotal}>
                  <Text style={styles.bottomBillingTextLeft}>Due Amount</Text>
                  <Text style={styles.bottomBillingTextRight}>
                    {orderDetails?.dueAmount}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </Page>
    </Document>
  );
};

export default InvoicePdf;

function numberToWords(num: number): string {
  const belowTwenty: string[] = [
    "zero",
    "one",
    "two",
    "three",
    "four",
    "five",
    "six",
    "seven",
    "eight",
    "nine",
    "ten",
    "eleven",
    "twelve",
    "thirteen",
    "fourteen",
    "fifteen",
    "sixteen",
    "seventeen",
    "eighteen",
    "nineteen",
  ];
  const tens: string[] = [
    "",
    "",
    "twenty",
    "thirty",
    "forty",
    "fifty",
    "sixty",
    "seventy",
    "eighty",
    "ninety",
  ];
  const thousands: string[] = [
    "",
    "thousand",
    "million",
    "billion",
    "trillion",
  ];

  if (num === 0) return "zero";

  let words = "";

  function helper(n: number): string {
    if (n < 20) return belowTwenty[n];
    if (n < 100)
      return (
        tens[Math.floor(n / 10)] +
        (n % 10 !== 0 ? " " + belowTwenty[n % 10] : "")
      );
    if (n < 1000)
      return (
        belowTwenty[Math.floor(n / 100)] +
        " hundred" +
        (n % 100 !== 0 ? " " + helper(n % 100) : "")
      );
    for (let i = 0; i < thousands.length; i++) {
      const unit = 1000 ** (i + 1);
      if (n < unit)
        return (
          helper(Math.floor(n / (unit / 1000))) +
          " " +
          thousands[i] +
          (n % (unit / 1000) !== 0 ? " " + helper(n % (unit / 1000)) : "")
        );
    }
    return ""; // Added to satisfy TypeScript return type requirement
  }

  words = helper(num);
  return words;
}
