import React from "react";
import { Metadata } from "next";
import UserDashboardLayout from "@/Layouts/UserDashboardLayout";
import OrdersPageOverview from "@/Components/OrdersPageComponents/OrdersPageOverview";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { ROUTES } from "@/Routes";

export const metadata: Metadata = {
  title: "Orders | INNI",
};
const OrdersPage = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;

  if (!accessToken) {
    redirect(ROUTES.LOG_IN(ROUTES.DASHBOARD.ORDERS.HOME));
    return null;
  }
  return (
    <UserDashboardLayout>
      <OrdersPageOverview accessToken={accessToken ? accessToken : ""} />
    </UserDashboardLayout>
  );
};

export default OrdersPage;
