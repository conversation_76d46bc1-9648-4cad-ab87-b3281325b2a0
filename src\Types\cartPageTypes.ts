export interface CartListType {
  message: string;
  results: SingleCartItem[];
  status: boolean;
}

export interface SingleCartItem {
  _id: string;
  productId: string;
  quantity: number;
  userId: string;
  created_at: string;
  updated_at: string;
  __v: number;
  name: string;
  size: string;
  price: number;
  discountPrice: number;
  image: string;
  brand: string;
  isAvailable: boolean;
  productCategory: string;
}
