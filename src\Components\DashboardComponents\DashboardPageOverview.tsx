"use client";
import { getUserDashboardDetailsApi } from "@/services/userServices";
import { useQuery } from "@tanstack/react-query";
import OrdersPageOverview from "../OrdersPageComponents/OrdersPageOverview";
import SpinnerLoader from "../ReusableComponents/SpinnerLoader/SpinnerLoader";
import SalesSummery from "./SalesSummery";

interface Props {
  accessToken: string;
}

const DashboardPageOverview = ({ accessToken }: Props) => {
  const { api, getKey } = getUserDashboardDetailsApi();
  const { data, isLoading } = useQuery(getKey(), api, {
    refetchOnWindowFocus: false,
    enabled: accessToken ? true : false,
  });

  return (
    <div>
      {!isLoading ? (
        <>
          {data?.result.summery.totalOrderAmount ? (
            <div>
              <SalesSummery summery={data?.result.summery} />
              <h2 className="my-2 text-lg font-bold">Recent Orders</h2>
              <OrdersPageOverview accessToken={accessToken} />
            </div>
          ) : (
            <p>No Orders Yet</p>
          )}
        </>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
};

export default DashboardPageOverview;
