"use client";
import { getSearchProductListApi } from "@/services/productsServices";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import ProductsListViewer from "../ReusableComponents/ProductsListViewer/ProductsListViewer";
interface Props {
  accessToken: string;
}
const SearchProductList = ({ accessToken }: Props) => {
  const searchParams = useSearchParams();

  const searchQuery = searchParams.get("searchQuery") || "";

  const { api: searchResultApi, getKey: searchResultKey } =
    getSearchProductListApi(searchQuery);

  const { data: searchResults, isLoading } = useQuery(
    searchResultKey(),
    searchResultApi, // Pass the search query to your API function
    {
      refetchOnWindowFocus: false,
      enabled: searchQuery.length ? true : false,
    }
  );

  return (
    <div>
      <ProductsListViewer
        productList={searchResults?.results}
        loading={isLoading}
        accessToken={accessToken}
        xxl={6}
        xl={5}
        lg={4}
        md={3}
        sm={2}
      />
      {/* {Number(products?.totalNoOfPages) > 1 ? (
        <CustomPagination
          currentPage={Number(page)}
          totalPages={Number(products?.totalNoOfPages)}
          handlePageChange={handlePageChange}
        />
      ) : (
        ""
      )} */}
    </div>
  );
};

export default SearchProductList;
