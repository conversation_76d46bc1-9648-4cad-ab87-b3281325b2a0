import LocalDataProvider from "@/Context/LocalDataProvider";
import Providers from "@/hooks/Providers";
import { lato } from "@/utils/fonts";
import { GoogleTagManager } from "@next/third-parties/google";
import type { Metadata } from "next";
import "./globals.css";

const des = `INNI inspires Bangladeshis to transform their skincare habits by offering authentic Korean skincare products, expert tips, and easy-to-follow routines. We are dedicated to bringing the best and 100% genuine Korean skincare products to the people of Bangladesh at affordable prices.
Our mission is to ensure that everyone has access to original Korean skincare solutions that truly work. We are committed to delivering 100% customer satisfaction through reliable support and trusted service.
We offer a cash on delivery system within Dhaka city, making shopping easier and safer.
At INNI, our greatest strength is your trust and support.

`;

export const metadata: Metadata = {
  title: "INNI",
  description: des,
  openGraph: {
    images: ["https://i.ibb.co/G3phx3QF/korean-trendy-mall.jpg"],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={lato.className}>
      {/* <GoogleTagManager gtmId="GTM-TZ2KQCGH" /> */}
      <head>
        {/* <meta
          name="facebook-domain-verification"
          content="jceappac0a4icjpwgevp40nn46clks"
        />
        <meta
          name="google-site-verification"
          content="O0fl0zaCcsxDv2uEEhAq4ywcm2O9MfyN5VTpvkRJ8bE"
        /> */}

        {/* <script
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-FH78GGXT8Z"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-FH78GGXT8Z');
            `,
          }}
        /> */}
      </head>
      <body>
        <noscript>
          {/* <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-TZ2KQCGH"
            height="0"
            width="0"
            style={{ display: "none", visibility: "hidden" }}
          ></iframe> */}
        </noscript>
        {/* <Suspense>
          <Analytics />
        </Suspense> */}
        <Providers>
          <LocalDataProvider>
            <main>{children}</main>
          </LocalDataProvider>
        </Providers>
      </body>
      {/* <GoogleAnalytics gaId="G-G40YMNFX0F" /> */}
    </html>
  );
}

{
  /* <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-MCDKXTPBMJ');
            `,
          }}
        /> */
}
