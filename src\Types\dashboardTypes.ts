import { SingleOrderDetails } from "./orderTypes";

export interface OrderSummeryResponseType {
  message: string;
  result: {
    summery: OrderSummeryType;
    statistics: {
      categories: SingleCategorySummery[];
      brands: SingleBrandSummery[];
      status: StatusWiseSummery[];
    };
  };
  success: boolean;
}

export interface OrderSummeryType {
  _id: string;
  totalOrderCount: number;
  totalOrderAmount: number;
  cancelledOrderCount: number;
  cancelledOrderAmount: number;
  deliveredOrderCount: number;
  deliveredOrderAmount: number;
  refundedOrderCount: number;
  refundedOrderAmount: number;
  inProgressOrderCount: number;
  inProgressOrderAmount: number;
  pendingOrderCount: number;
  pendingOrderAmount: number;
  totalOrderedProducts: number;
  totalDeliveredProducts: number;
  totalProfitOnDeliveredProducts: number;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}

export interface CategoryWiseOrderSummeryResponseType {
  message: string;
  result: {
    categories: SingleCategorySummery[];
    brands: SingleBrandSummery[];
    status: StatusWiseSummery[];
    sources: SingleSourceSummery[];
  };
  success: boolean;
}

export interface SingleCategorySummery {
  category: string;
  quantity: number;
}
export interface SingleBrandSummery {
  brand: string;
  quantity: number;
}
export interface SingleSourceSummery {
  source: string;
  totalCount: number;
  totalAmount: number;
}
export interface StatusWiseSummery {
  status: string;
  totalCount: number;
  totalAmount: number;
}

export interface SevenAndThirtyDaysResponse {
  message: string;
  result: {
    report: SevenAndThirtyDaysResult[];
    summery: OrderSummeryType;
    orders: SingleOrderDetails[];
  };
  success: boolean;
}

export interface SevenAndThirtyDaysResult {
  date: string;
  totalOrderCount: number;
  totalOrderAmount: number;
  cancelledOrderCount: number;
  cancelledOrderAmount: number;
  deliveredOrderCount: number;
  deliveredOrderAmount: number;
  refundedOrderCount: number;
  refundedOrderAmount: number;
  inProgressOrderCount: number;
  inProgressOrderAmount: number;
  pendingOrderCount: number;
  pendingOrderAmount: number;
}
