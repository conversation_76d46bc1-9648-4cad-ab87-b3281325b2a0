import { Option } from "@/Components/utils/filterOptionData";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import {
  SingleBrand,
  SingleSubCategory,
} from "@/services/bannerBrandCategoryServices";
import { useRouter, useSearchParams } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import CustomDropdown from "../CustomDropdown/CustomDropdown";

interface Props {
  pageType: string;
  brandName?: string;
  categoryName?: string;
  subCategoryName?: string;
}

const ProductFilterOptionsTwo = ({
  pageType,
  brandName,
  categoryName,
  subCategoryName,
}: Props) => {
  const {
    brands,
    categories,
    skinConcerns,
    skinTypes,
    isBrandsLoading,
    isCategoriesLoading,
  }: ContextDataType = useContext(LocalDataContext);
  const router = useRouter();
  const searchParams = useSearchParams();
  const [brandsList, setBrandsList] = useState<Option[]>();
  const [categoriesList, setCategoriesList] = useState<Option[]>();
  const [subCategoriesList, setSubCategoriesList] = useState<Option[]>();
  const [skinTypesList, setSkinTypesList] = useState<Option[]>();
  const [skinConcernsList, setSkinConcernsList] = useState<Option[]>();

  const brand =
    pageType === "brand" ? brandName : searchParams.get("brand") || "";
  const productType = searchParams.get("productType") || "";
  const category =
    pageType === "category" || pageType === "subCategory"
      ? categoryName
      : searchParams.get("category") || "";
  const subCategory =
    pageType === "subCategory"
      ? subCategoryName
      : searchParams.get("subCategory") || "";
  const skinType = searchParams.get("skinType") || "";
  const skinConcern = searchParams.get("skinConcern") || "";
  const page = searchParams.get("page") || 1;

  const handleChangeBrand = (value: string) => {
    // make page = 1 in all
    //page=${page}
    router.push(
      `/products?page=1&brand=${value}${productType && `&productType=${productType}`}${category && `&category=${category}`}${subCategory && `&subCategory=${subCategory}`}${skinType && `&skinType=${skinType}`}${skinConcern && `&skinConcern=${skinConcern}`}`,
    );
  };
  const handleChangeProductType = (value: string) => {
    router.push(
      `/products?page=1${brand && `&brand=${brand}`}&productType=${value}${category && `&category=${category}`}${subCategory && `&subCategory=${subCategory}`}${skinType && `&skinType=${skinType}`}${skinConcern && `&skinConcern=${skinConcern}`}`,
    );
  };
  const handleChangeCategory = (value: string) => {
    router.push(
      `/products?page=1${brand && `&brand=${brand}`}${productType && `&productType=${productType}`}&category=${value}${skinType && `&skinType=${skinType}`}${skinConcern && `&skinConcern=${skinConcern}`}`,
    );
  };
  const handleChangeSubCategory = (value: string) => {
    router.push(
      `/products?page=1${brand && `&brand=${brand}`}${productType && `&productType=${productType}`}${category && `&category=${category}`}&subCategory=${value}${skinType && `&skinType=${skinType}`}${skinConcern && `&skinConcern=${skinConcern}`}`,
    );
  };
  const handleChangeSkinType = (value: string) => {
    router.push(
      `/products?page=1${brand && `&brand=${brand}`}${productType && `&productType=${productType}`}${category && `&category=${category}`}${subCategory && `&subCategory=${subCategory}`}&skinType=${value}${skinConcern && `&skinConcern=${skinConcern}`}`,
    );
  };
  const handleChangeSkinConcern = (value: string) => {
    router.push(
      `/products?page=1${brand && `&brand=${brand}`}${productType && `&productType=${productType}`}${category && `&category=${category}`}${skinType && `&skinType=${skinType}`}&skinConcern=${value}`,
    );
  };
  const allType = {
    value: "",
    label: "All",
    image: " ",
  };

  useEffect(() => {
    if (brands) {
      const brandsData = brands.map((single: SingleBrand) => {
        return {
          value: single.slug,
          label: single.name,
        };
      });
      setBrandsList(brandsData);
    }
  }, [brands]);
  useEffect(() => {
    if (categories) {
      const brandsData = categories.map((single: SingleBrand) => {
        return {
          value: single.slug,
          label: single.name,
        };
      });
      setCategoriesList(brandsData);
    }
  }, [categories]);
  useEffect(() => {
    if (skinTypes) {
      const brandsData = skinTypes.map((single: SingleBrand) => {
        return {
          // value: single.slug,
          value: single.name,
          label: single.name,
        };
      });
      setSkinTypesList(brandsData);
    }
  }, [skinTypes]);
  useEffect(() => {
    if (skinConcerns) {
      const brandsData = skinConcerns.map((single: SingleBrand) => {
        return {
          value: single.name,
          // value: single.slug,
          label: single.name,
        };
      });
      setSkinConcernsList(brandsData);
    }
  }, [skinConcerns]);
  useEffect(() => {
    if (category && categories) {
      const selectedCategory = categories.find(
        (single: SingleBrand) => single.slug === category,
      );
      const subData = selectedCategory?.subCategories?.map(
        (sin: SingleSubCategory) => {
          return {
            value: sin.slug,
            label: sin.name,
          };
        },
      );
      setSubCategoriesList(subData);
    }
  }, [categories, category]);

  return (
    <div className="">
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3">
        <div>
          <CustomDropdown
            options={brandsList?.length ? [allType, ...brandsList] : []}
            selectedValue={brand ? brand : ""}
            onSelect={handleChangeBrand}
            width="200px"
            placeholder="Select brand"
          />
        </div>
        <div>
          <CustomDropdown
            options={categoriesList?.length ? [allType, ...categoriesList] : []}
            selectedValue={category ? category : ""}
            onSelect={handleChangeCategory}
            width="200px"
            placeholder="Select category"
          />
        </div>
        <div className="hidden sm:block">
          <CustomDropdown
            options={skinTypesList?.length ? [allType, ...skinTypesList] : []}
            selectedValue={skinType ? skinType : ""}
            onSelect={handleChangeSkinType}
            width="200px"
            placeholder="Select skin type"
          />
        </div>
      </div>
    </div>
  );
};

export default ProductFilterOptionsTwo;
