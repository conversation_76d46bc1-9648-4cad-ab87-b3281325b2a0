// components/ProductSkeleton.tsx
import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const ProductsListSkeleton = () => {
  return (
    <div className="grid grid-cols-2 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 ">
      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num: number) => (
        <div
          className="animate-pulse overflow-hidden rounded-lg border border-gray-300 p-2 shadow-md"
          key={num}
        >
          <div className="relative  rounded-lg border-gray-200 p-1">
            <Skeleton height={220} />
          </div>

          <div className="px-2 pb-0 pt-3">
            <Skeleton height={30} style={{ marginBottom: "5px" }} />
            <Skeleton height={20} width={150} style={{ marginBottom: "5px" }} />
            <Skeleton height={30} baseColor="#092143" />
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductsListSkeleton;
