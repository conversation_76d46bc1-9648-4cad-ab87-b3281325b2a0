"use client";
import { SingleOrderDetails } from "@/Types/orderTypes";
import CheckoutOrOrderedProducts from "../ReusableComponents/CheckoutOrOrderedProducts/CheckoutOrOrderedProducts";
import TitleViewer from "../ReusableComponents/TitleViewer/TitleViewer";
import Tracking from "./Tracking";

interface Props {
  orderDetails: SingleOrderDetails;
}
const OrderDetailsOverview = ({ orderDetails }: Props) => {
  return (
    <div>
      <TitleViewer titleTwo="Order Details" titleOne="" seeAllButton={false} />
      <table className="w-full table-auto">
        <tbody>
          <tr className="bg-gray-100">
            <td className="border px-4 py-2 text-sm font-normal">Order No</td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              {orderDetails?.orderId}
            </td>
          </tr>
          <tr className="bg-white">
            <td className="border px-4 py-2 text-sm font-normal">Status</td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              {orderDetails?.orderStatus}
            </td>
          </tr>
          <tr className="bg-gray-100">
            <td className="border px-4 py-2 text-sm font-normal">Name</td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              {orderDetails?.name}
            </td>
          </tr>
          <tr className="bg-white">
            <td className="border px-4 py-2 text-sm font-normal">Phone </td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              {orderDetails?.phoneNumber}
            </td>
          </tr>
          <tr className="bg-gray-100">
            <td className="border px-4 py-2 text-sm font-normal">Email</td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              {orderDetails?.email}
            </td>
          </tr>
          <tr className="bg-white">
            <td className="border px-4 py-2 text-sm font-normal">
              Delivery Address
            </td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              {orderDetails?.deliveryAddress}
            </td>
          </tr>
          <tr className="bg-gray-100">
            <td className="border px-4 py-2 text-sm font-normal">
              Product Price
            </td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              ৳{orderDetails?.productPrice}
            </td>
          </tr>
          <tr className="bg-white">
            <td className="border px-4 py-2 text-sm font-normal">Discount</td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              -৳{orderDetails?.discount}
            </td>
          </tr>
          <tr className="bg-gray-100">
            <td className="border px-4 py-2 text-sm font-normal">Coupon</td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              ৳{orderDetails?.couponDiscount}
            </td>
          </tr>
          <tr className="bg-white">
            <td className="border px-4 py-2 text-sm font-normal">
              Delivery Charge
            </td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              +৳{orderDetails?.deliveryCharge}
            </td>
          </tr>
          <tr className="bg-gray-100">
            <td className="border px-4 py-2 text-sm font-normal">Total</td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              ৳{orderDetails?.totalAmount}
            </td>
          </tr>
          <tr className="bg-white">
            <td className="border px-4 py-2 text-sm font-normal">
              Paid Amount
            </td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              -৳{orderDetails?.paidAmount}
            </td>
          </tr>
          <tr className="bg-gray-100">
            <td className="border px-4 py-2 text-sm font-normal">Due Amount</td>
            <td className="border px-4 py-2 text-right text-sm font-normal">
              ৳{orderDetails?.dueAmount}
            </td>
          </tr>
        </tbody>
      </table>
      <CheckoutOrOrderedProducts products={orderDetails?.products} />
      <Tracking orderDetails={orderDetails!} />
    </div>
  );
};

export default OrderDetailsOverview;
