import UserDashboardLayout from "@/Layouts/UserDashboardLayout";
import { ROUTES } from "@/Routes";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import React from "react";

export const metadata: Metadata = {
  title: "User Account | INNI",
};

const AccountPage = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;
  if (!accessToken) {
    redirect(ROUTES.LOG_IN(ROUTES.DASHBOARD.HOME));
  }
  return (
    <UserDashboardLayout>
      <div>
        <h2>This is account details page</h2>
      </div>
    </UserDashboardLayout>
  );
};

export default AccountPage;
