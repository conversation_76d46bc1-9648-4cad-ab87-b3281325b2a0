/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "!./node_modules/**",
  ],
  purge: ["./src/**/*.{js,ts,jsx,tsx}", "./app/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      colors: {
        primary: "#32004A",
        secondary: "#F48F4B",
        ternary: "#092143",
        grayish: "#cecece",
      },
      screens: {
        sm: "640px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1537px",
      },
      animation: {
        "slide-down": "slide-down 0.3s ease-out forwards",
      },
      fontFamily: {
        poppins: ["var(--font-poppins)"],
        DmSerifDisplay: ["var(--font-dm-serif-display)"],
      },
    },
  },
  plugins: [],
};
