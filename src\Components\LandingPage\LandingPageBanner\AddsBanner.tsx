import Link from "next/link";

const AddsBanner = () => {
  return (
    <div className="">
      <div
        className="bg-cover bg-center bg-no-repeat p-8 rounded-md"
        style={{ backgroundImage: "url('https://i.ibb.co/Fb9GSs18/Section.png')" }}
      >
        <div className="flex flex-col md:flex-row justify-start md:justify-between  items-start md:items-center">
          <p className="text-primary mb-3 md:mb-0"><span className="font-bold">100% Secure delivery</span> without contacting the courier</p>
          <Link href="#" className="text-white bg-primary rounded-full px-4 py-2 font-bold">
            Shop Now
          </Link>
        </div>

      </div>
    </div>
  );
};

export default AddsBanner;