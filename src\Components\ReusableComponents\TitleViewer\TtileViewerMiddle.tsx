
interface Props {
  titleOne: string;
  titleTwo: string;
}

const TtileViewerMiddle = ({ titleOne, titleTwo }: Props) => {
  return (
    <div className="border-b border-primary mb-4 mt-3 sm:mt-10 md:mb-5 md:mt-16 lg:mt-20">
      <div className="flex justify-center">
        <div className="px-3 sm:px-4 py-2 bg-primary rounded-tl-xl rounded-tr-xl inline-block">
          <h2 className="text-white text-base sm:text-lg font-normal sm:font-semibold uppercase">{titleOne}</h2>
        </div>
      </div>
    </div>
  );
};

export default TtileViewerMiddle;
