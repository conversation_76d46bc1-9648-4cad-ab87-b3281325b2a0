export interface WishlistResult {
  message: string;
  results: SingleWishlistItem[];
  status: boolean;
}

export interface SingleWishlistItem {
  _id: string;
  productId: ProductDetailsInWishlist;
  userId: string;
  __v: number;
}

export interface ProductDetailsInWishlist {
  _id: string;
  id: string;
  slug: string;
  name: string;
  size: string;
  price: number;
  discountPrice: number;
  mainImageUrl: string;
  brand: string;
  productCategory: string;
  isAvailable: boolean;
  rating: number;
}
