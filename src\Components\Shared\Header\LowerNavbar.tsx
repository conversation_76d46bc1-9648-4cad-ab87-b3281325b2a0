"use client";

import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useContext, useState } from "react";
import { AiOutlineDown } from "react-icons/ai";
import { HiOutlineBars3BottomLeft } from "react-icons/hi2";
import { IoIosArrowDown } from "react-icons/io";

const LowerNavbar = () => {
  const { categories, isCategoriesLoading }: ContextDataType =
    useContext(LocalDataContext);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const pathname = usePathname();
  const [showBrandsModal, setShowBrandsModal] = React.useState(false);
  const [showSkinCareModal, setShowSkinCareModal] = React.useState(false);

  const handleToggle = () => {
    setIsModalOpen(!isModalOpen);
  };
  return (
    <div>
      <div className="hidden border-b border-b-gray-300 bg-white sm:block py-1 border-t border-t-gray-100">
        <div className="px-4 md:px-6 lg:px-20 xl:px-28">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="relative">
                <button
                  className="flex w-[200px] items-center justify-between bg-primary rounded-full px-[20px] py-[10px]"
                  onClick={handleToggle}
                >
                  <span className="flex items-center text-white">
                    <HiOutlineBars3BottomLeft className="mr-1" /> Categories
                  </span>
                  <IoIosArrowDown className="text-white" />
                </button>
                <div
                  className={`absolute left-0 top-[44px] z-10 w-[80vw] transform border-2 border-[#D5D5D5] rounded-[20px] bg-white p-5 shadow transition-all duration-500 ease-in-out ${isModalOpen
                    ? "translate-y-0 opacity-100"
                    : "pointer-events-none -translate-y-10 opacity-0"
                    }`}
                >
                  <div className="grid grid-cols-10 gap-6">
                    {categories?.map((sin) => (
                      <Link
                        href={ROUTES.PRODUCTS.CATEGORY(sin?.slug)}
                        className="flex flex-col items-center"
                        key={sin.slug}
                      >
                        <Image
                          src={generateProductImage(sin.imageUrl)}
                          width={50}
                          height={50}
                          alt="category image"
                          className="rounded-full"
                        />
                        <span className="text-center text-sm mt-2 text-gray-700">
                          {sin.name}
                        </span>
                      </Link>
                    ))}
                    {/* <Link href="#" className="flex flex-col items-center">
                      <Image
                        src={categoryImage2}
                        width={80}
                        height={80}
                        alt="category image"
                        className="rounded-full"
                      />
                      <span className="text-center text-gray-700">
                        Flash Sale
                      </span>
                    </Link>
                    <Link href="#" className="flex flex-col items-center">
                      <Image
                        src={categoryImage3}
                        width={80}
                        height={80}
                        alt="category image"
                        className="rounded-full"
                      />
                      <span className="text-center text-gray-700">
                        Special Offer
                      </span>
                    </Link>
                    <Link href="#" className="flex flex-col items-center">
                      <Image
                        src={categoryImage4}
                        width={80}
                        height={80}
                        alt="category image"
                        className="rounded-full"
                      />
                      <span className="text-center text-gray-700">
                        Skin Care
                      </span>
                    </Link>
                    <Link href="#" className="flex flex-col items-center">
                      <Image
                        src={categoryImage5}
                        width={80}
                        height={80}
                        alt="category image"
                        className="rounded-full"
                      />
                      <span className="text-center text-gray-700">
                        Body Care
                      </span>
                    </Link>
                    <Link href="#" className="flex flex-col items-center">
                      <Image
                        src={categoryImage6}
                        width={80}
                        height={80}
                        alt="category image"
                        className="rounded-full"
                      />
                      <span className="text-center text-gray-700">
                        Hair Care
                      </span>
                    </Link>
                    <Link href="#" className="flex flex-col items-center">
                      <Image
                        src={categoryImage7}
                        width={80}
                        height={80}
                        alt="category image"
                        className="rounded-full"
                      />
                      <span className="text-center text-gray-700">
                        Beauty Tools
                      </span>
                    </Link>
                    <Link href="#" className="flex flex-col items-center">
                      <Image
                        src={categoryImage8}
                        width={80}
                        height={80}
                        alt="category image"
                        className="rounded-full"
                      />
                      <span className="text-center text-gray-700">Makeup</span>
                    </Link>
                    <Link href="#" className="flex flex-col items-center">
                      <Image
                        src={categoryImage9}
                        width={80}
                        height={80}
                        alt="category image"
                        className="rounded-full"
                      />
                      <span className="text-center text-gray-700">Combo</span>
                    </Link> */}
                  </div>
                </div>
              </div>
              {/* navbar */}
              <div className="flex items-center space-x-3 text-black">
                <Link href={ROUTES.HOME} className="hidden md:block">
                  <span
                    className={`my-[6px] flex items-center gap-1 rounded-full ${pathname[pathname.length - 1] === "/" ? "bg-white text-black" : "bg-white"} px-3 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
                  >
                    <span className="text-xs uppercase md:text-sm">Home</span>
                    <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
                  </span>
                </Link>
                <Link href={ROUTES.PRODUCTS.HOME} className="hidden md:block">
                  <span
                    className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/products") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
                  >
                    <span className="text-xs uppercase md:text-sm">Products</span>
                    <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
                  </span>
                </Link>
                <div
                  className="hidden md:block"
                  // href={ROUTES.BRANDS.HOME}
                  onMouseEnter={() => setShowBrandsModal(true)}
                  onMouseLeave={() => setShowBrandsModal(false)}
                >
                  <span
                    className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/brands") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
                  >
                    <span className="text-xs uppercase md:text-sm">Brands</span>{" "}
                    <AiOutlineDown className="text-xs md:text-sm" />
                  </span>
                  {showBrandsModal && <BrandListModal />}
                </div>
                <div
                  className="hidden md:block"
                  // href={ROUTES.BRANDS.HOME}
                  onMouseEnter={() => setShowSkinCareModal(true)}
                  onMouseLeave={() => setShowSkinCareModal(false)}
                  onClick={() => setShowSkinCareModal(!showSkinCareModal)}
                >
                  <span className="my-[6px] flex items-center gap-1 rounded-full bg-white px-2 py-1 hover:bg-white hover:text-black">
                    <span className="text-xs uppercase md:text-sm">Skin Care</span>{" "}
                    <AiOutlineDown className="text-xs md:text-sm" />
                  </span>
                  {showSkinCareModal && <SkinCareModal />}
                </div>
                <Link href="/combo?page=1" className="hidden md:block">
                  <span
                    className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/combo") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
                  >
                    <span className="text-xs uppercase md:text-sm">Combo</span>
                    <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
                  </span>
                </Link>
                <Link href="/offers?page=1" className="hidden md:block">
                  <span
                    className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/offers") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
                  >
                    <span className="text-xs uppercase md:text-sm">Offers</span>
                    <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
                  </span>
                </Link>
                <Link href="/new-arrival?page=1" className="hidden md:block">
                  <span
                    className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/new-arrival") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
                  >
                    <span className="text-xs uppercase md:text-sm">New Arrival</span>
                    <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
                  </span>
                </Link>
                <Link href="/best-seller?page=1" className="hidden md:block">
                  <span
                    className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/best-seller") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
                  >
                    <span className="text-xs uppercase md:text-sm">Best Seller</span>
                    <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
                  </span>
                </Link>
                {/* small screen */}
                <div className="">
                  <div
                    className="block md:hidden"
                    onClick={() => {
                      setShowBrandsModal(!showBrandsModal);
                      setShowSkinCareModal(false);
                    }}
                  >
                    <span className="my-[6px] flex items-center rounded-full bg-white px-2 py-1 hover:bg-white hover:text-black">
                      <span className="text-xs">Brands</span>{" "}
                      <AiOutlineDown className="text-xs" />
                    </span>
                    {showBrandsModal && <BrandListModal />}
                  </div>
                </div>
                <div
                  className="block md:hidden"
                  onClick={() => {
                    setShowSkinCareModal(!showSkinCareModal);
                    setShowBrandsModal(false);
                  }}
                >
                  <span className="my-[6px] flex items-center rounded-full bg-white px-2 py-1 hover:bg-white hover:text-black">
                    <span className="text-xs">Skin Care</span>{" "}
                    <AiOutlineDown className="text-xs" />
                  </span>
                  {showSkinCareModal && <SkinCareModal />}
                </div>
                <Link href="/combo?page=1" className="block md:hidden">
                  <span
                    className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/combo") ? "bg-white text-white" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
                  >
                    <span className="text-xs md:text-sm">Combo</span>
                    <AiOutlineDown className="hidden text-xs" />
                  </span>
                </Link>
                <Link href="/offers?page=1" className="block md:hidden">
                  <span
                    className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/offers") ? "bg-white text-white" : "bg-white"} px-2 py-1 hover:bg-white hover:text-black`}
                  >
                    <span className="text-xs md:text-sm">Offers</span>
                    <AiOutlineDown className="hidden text-xs" />
                  </span>
                </Link>
              </div>
            </div>
            {/* <div className="hidden lg:block">
              <div className="flex items-center gap-2">
                <RiCustomerService2Line className="text-3xl text-gray-500" />
                <div>
                  <small className="text-xs text-gray-600">Hotline:</small>
                  <p className="translate-y-[-5px] text-sm font-semibold text-primary">
                    +88{helpLineNumber}
                  </p>
                </div>
              </div>
            </div> */}
          </div>
        </div>
      </div>

      {/* for mobile device */}
      <div className="block border-b border-t border-b-primary border-t-grayish bg-white sm:hidden">
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-3 text-black">
            <Link href={ROUTES.HOME}>
              <span
                className={`my-[6px] flex items-center gap-1 rounded-full ${pathname[pathname.length - 1] === "/" ? "bg-white text-black" : "bg-white"} px-3 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
              >
                <span className="text-xs md:text-sm">Home</span>
                <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
              </span>
            </Link>
            <Link href={ROUTES.PRODUCTS.HOME}>
              <span
                className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/products") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
              >
                <span className="text-xs md:text-sm">Products</span>
                <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
              </span>
            </Link>
            <div
              // href={ROUTES.BRANDS.HOME}
              onMouseEnter={() => setShowBrandsModal(true)}
              onMouseLeave={() => setShowBrandsModal(false)}
            >
              <span
                className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/brands") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
              >
                <span className="text-xs md:text-sm">Brands</span>{" "}
                <AiOutlineDown className="text-xs md:text-sm" />
              </span>
              {showBrandsModal && <BrandListModal />}
            </div>
            <Link href="/combo?page=1">
              <span
                className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/combo") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
              >
                <span className="text-xs md:text-sm">Combo</span>
                <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
              </span>
            </Link>
            <Link href="/offers?page=1">
              <span
                className={`my-[6px] flex items-center gap-1 rounded-full ${pathname?.includes("/offers") ? "bg-white text-black" : "bg-white"} px-2 py-1 text-xs hover:bg-white hover:text-black md:text-sm`}
              >
                <span className="text-xs md:text-sm">Offers</span>
                <AiOutlineDown className="hidden translate-y-[1.5px] text-xs md:text-sm" />
              </span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LowerNavbar;

const BrandListModal = () => {
  const { brands, isBrandsLoading }: ContextDataType =
    useContext(LocalDataContext);
  return (
    <div className="relative animate-slide-down">
      <div className="absolute left-[40px] top-[-9px] h-0 w-0 border-b-[9px] border-l-[9px] border-r-[9px] border-b-gray-200 border-l-transparent border-r-transparent sm:left-[42px]">
        <div className="absolute left-[-115px] top-[9px] max-h-[500px] w-60 overflow-y-auto rounded-[20px] border-2 border-gray-200 bg-white p-4 shadow sm:left-[-50px] md:left-[-42px] lg:w-[60vw]">
          <div>
            <h3 className="mb-3 text-sm font-semibold md:text-xl">
              List of Brands:
            </h3>
            {/* <Image
              src={brandList}
              alt="menu image"
              width={175}
              height={80}
              className="mb-2 rounded"
            /> */}
            <div className="grid grid-cols-12 gap-2 md:gap-2">
              {!isBrandsLoading && brands?.length
                ? brands?.map((sin) => (
                  <Link
                    key={sin._id}
                    href={ROUTES.BRANDS.VIEW(sin.slug)}
                    className="col col-span-12 text-xs hover:text-sm hover:font-bold hover:text-secondary md:text-sm lg:col-span-3"
                  >
                    {sin.name}
                  </Link>
                ))
                : ""}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const SkinCareModal = () => {
  const {
    categories,
    isCategoriesLoading,
    isSkinTypesLoading,
    skinTypes,
    isSkinConcernsLoading,
    skinConcerns,
  }: ContextDataType = useContext(LocalDataContext);
  return (
    <div className="relative animate-slide-down">
      <div className="absolute left-[42px] top-[-8px] h-0 w-0 border-b-[9px] border-l-[9px] border-r-[9px] border-b-gray-200 border-l-transparent border-r-transparent">
        <div className="absolute left-[-130px] top-[9px] grid max-h-[500px] w-[90vw] grid-cols-12 gap-4 overflow-y-auto rounded-[20px] border-2 border-gray-20 bg-white p-4  shadow sm:w-[60vw] md:left-[-150px] md:w-[50vw] lg:left-[-400px] lg:w-[80vw]  2xl:w-[80vw]">
          <div className="col col-span-6 lg:col-span-3">
            <h3 className="mb-2 text-lg font-semibold md:text-xl">
              Skin Types:
            </h3>
            {/* <Image
              src={skinType}
              alt="menu image"
              width={175}
              height={80}
              className="mb-2 rounded"
            /> */}
            <div className="flex flex-col gap-2">
              {!isSkinTypesLoading && skinTypes?.length
                ? skinTypes?.map((sin) => (
                  <Link
                    key={sin._id}
                    href={ROUTES.PRODUCTS.SKIN_TYPE(sin.slug)}
                    className="text-xs hover:text-secondary md:text-sm"
                  >
                    {sin.name}
                  </Link>
                ))
                : ""}
            </div>
          </div>
          <div className="col col-span-6 lg:col-span-3">
            <h3 className="mb-2 text-lg font-semibold md:text-xl">
              Skin Concerns:
            </h3>
            {/* <Image
              src={skinConcern}
              alt="menu image"
              width={175}
              height={80}
              className="mb-2 rounded"
            /> */}
            <div className="flex flex-col gap-2">
              {!isSkinConcernsLoading && skinConcerns?.length
                ? skinConcerns?.map((sin) => (
                  <Link
                    key={sin._id}
                    href={ROUTES.PRODUCTS.SKIN_CONCERN(sin.slug)}
                    className="text-xs hover:text-secondary md:text-sm"
                  >
                    {sin.name}
                  </Link>
                ))
                : ""}
            </div>
          </div>
          <div className="col col-span-12 lg:col-span-6">
            <h3 className="mb-2 text-lg font-semibold md:text-xl">
              Product Category:
            </h3>
            {/* <Image
              src={skinCategory}
              alt="menu image"
              width={175}
              height={80}
              className="mb-2 rounded"
            /> */}
            <div className="grid grid-cols-12 gap-2">
              {!isCategoriesLoading && categories?.length
                ? categories?.map((sin) => (
                  <Link
                    key={sin._id}
                    href={ROUTES.PRODUCTS.CATEGORY(sin.slug)}
                    className="col col-span-12 text-xs hover:text-secondary md:col-span-4 md:text-sm"
                  >
                    {sin.name}
                  </Link>
                ))
                : ""}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
