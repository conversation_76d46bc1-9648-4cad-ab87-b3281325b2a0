"use client";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { MdClose } from "react-icons/md";
import ProductFilterOptions from "../ProductFilterOptions/ProductFilterOptions";

const LeftSideFilterSlider = ({
  openFilterSlider,
  setOpenFilterSlider,
  pageType,
}: any) => {
  return (
    <Transition.Root show={openFilterSlider} as={Fragment}>
      <Dialog
        as="div"
        className="relative"
        onClose={setOpenFilterSlider}
        style={{ zIndex: 999 }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-50 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0  flex max-w-[200px]">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-[-200px]"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-[-200px]"
              >
                <Dialog.Panel className="pointer-events-auto relative w-screen max-w-md">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-in-out duration-500"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in-out duration-500"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <div />
                  </Transition.Child>
                  <div className="flex h-full w-[250px] flex-col overflow-y-auto bg-white py-6 shadow-xl">
                    <div className="px-2 sm:px-2">
                      <div className="flex items-center justify-between">
                        <Dialog.Title className="mb-4 text-base font-semibold leading-6 text-gray-900">
                          Filter
                        </Dialog.Title>
                        <div>
                          <button onClick={() => setOpenFilterSlider(false)}>
                            <MdClose />
                          </button>
                        </div>
                      </div>
                      <div>
                        <ProductFilterOptions pageType={pageType} />
                      </div>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default LeftSideFilterSlider;
