"use client";
import { useEffect } from "react";

const FacebookMessenger = () => {
  useEffect(() => {
    (window as any).fbAsyncInit = function () {
      (window as any).FB.init({
        xfbml: true,
        version: "v17.0",
      });
    };

    (function (d, s, id) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s);
      js.id = id;
      (js as any).src =
        "https://connect.facebook.net/en_US/sdk/xfbml.customerchat.js";
      fjs.parentNode?.insertBefore(js, fjs);
    })(document, "script", "facebook-jssdk");
  }, []);

  return (
    <>
      <div id="fb-root" />
      <div
        className="fb-customerchat"
        data-attribution="biz_inbox"
        data-page_id="103899019300656"
      />
    </>
  );
};

export default FacebookMessenger;
