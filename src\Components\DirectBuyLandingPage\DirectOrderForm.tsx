"use client";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleProductDetailsType } from "@/Types/productTypes";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { createNewOrderWithoutUserApi } from "@/services/orderServices";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import toast from "react-hot-toast";
import * as Yup from "yup";
import { generateProductImage } from "../utils/GenerateProductImage";
import {
  maximumQuantityModal,
  minimumQuantityModal,
} from "../utils/commonModal";

interface Props {
  productDetails: SingleProductDetailsType;
}
const DirectOrderForm = ({ productDetails }: Props) => {
  const router = useRouter();
  const { localCheckoutItems, userHeaderDetails }: ContextDataType =
    useContext(LocalDataContext);
  const [quantity, setQuantity] = useState<number>(1);
  const [isCreatingOrder, setIsCreatingOrder] = useState<boolean>(false);
  const [deliveryCharge, setDeliveryCharge] = useState<number>(150);
  const [delivery, setDelivery] = useState<string>("inside dhaka");
  const [paymentMethod, setPaymentMethod] = useState<string>("COD");

  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    email: Yup.string().email("Invalid email"),
    phone: Yup.string().required("Phone number is required").min(11).max(11),
    zipCode: Yup.string(),
    deliveryAddress: Yup.string().required("Address is required"),
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      email: "",
      phone: "",
      division: "",
      district: "",
      zipCode: "",
      deliveryAddress: "",
      note: "",
      bkashAccount: "",
      paidAmount: 0,
      transactionId: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      setIsCreatingOrder(true);
      const userDetails = {
        name: values?.name,
        phone: values?.phone,
        deliveryAddress: values?.deliveryAddress,
        note: values?.note,
      };
      localStorage.setItem("userDetails", JSON.stringify(userDetails));

      const orderData = {
        name: values.name,
        phoneNumber: values.phone,
        email: values.email,
        // division: userHeaderDetails?.userDetails?.division,
        // district: userHeaderDetails?.userDetails?.district,
        deliveryAddress: values.deliveryAddress,
        delivery: delivery,
        paymentMethod: paymentMethod,
        bkashNumber: values.bkashAccount,
        transactionId: values.transactionId,
        orderStatus: "Pending",
        orderType: "auto",
        createdBy: "user",
        createdFrom: "landing page",
        userIpAddress: localStorage.getItem("ip_address") ?? "not-found",
        trackingHistory: [
          { time: new Date(), message: "Order Created Successfully" },
        ],
        notes: [
          {
            name: values.name,
            image: "",
            time: new Date(),
            message: values.note,
          },
        ],
        products: [
          {
            productId: productDetails?.id,
            quantity: quantity,
          },
        ],
        userSubmittedAmount: values.paidAmount ?? 0,
      };

      toast.promise(
        createNewOrderMutedAsync(orderData),
        {
          success: "Order Created Successful",
          error: "Error Placing order",
          loading: "Creating New order",
        },
        {
          id: "add-new-order",
        },
      );
    },
  });

  // add other api when user is available

  const { api: CreateOrderWithoutUserApi } = createNewOrderWithoutUserApi();

  const { mutateAsync: createNewOrderMutedAsync } = useMutation(
    CreateOrderWithoutUserApi,
    {
      onSuccess: (data) => {
        setIsCreatingOrder(true);
        router.push(ROUTES.SUCCESS(data?.result?.orderId));
      },
      onError: () => {
        setIsCreatingOrder(false);
      },
    },
  );

  useEffect(() => {
    if (delivery === "inside dhaka") {
      setDeliveryCharge(80);
    } else {
      setDeliveryCharge(150);
    }
  }, [delivery]);

  useEffect(() => {
    if (userHeaderDetails?.userDetails) {
      formik.setFieldValue("name", userHeaderDetails?.userDetails?.name);
      formik.setFieldValue(
        "phone",
        userHeaderDetails?.userDetails?.mobileNumber,
      );
      formik.setFieldValue(
        "deliveryAddress",
        userHeaderDetails?.userDetails?.deliveryAddress,
      );
    } else {
      const loc = localStorage.getItem("userDetails");
      const userDetails = loc && JSON.parse(loc);
      if (userDetails) {
        formik.setFieldValue("name", userDetails?.name);
        formik.setFieldValue("phone", userDetails?.phone);
        formik.setFieldValue("deliveryAddress", userDetails?.deliveryAddress);
        formik.setFieldValue("note", userDetails?.note);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [localCheckoutItems]);

  useEffect(() => {
    if (productDetails) {
      const props = {
        event: "initiate_checkout",
        value: productDetails?.discountPrice,
        user_id: "",
        user_name: "",
        user_phone_number: "",
        user_email: "",
        user_zip: "",
        user_district: "",
        user_log_in_status: "not logged in",
        items: [
          {
            item_id: productDetails?.id,
            item_name: productDetails?.name,
            price: productDetails?.discountPrice,
            item_brand: productDetails?.brand,
            item_category: productDetails?.productCategory,
            slug: productDetails?.slug,
            sku: productDetails?.id?.replace("GMK-", ""),
          },
        ],
      };
      gtmDataLayerDataPass(props);
    }
  }, [productDetails]);

  const text = `অর্ডার করতে আপনার নাম, ফোন নাম্বার লিখুন। এরপর পুরো ঠিকানা লিখে "অর্ডার" বাটনে ক্লিক করুন।`;
  return (
    <div
      className=" px-2 py-6 md:px-10 md:py-10 lg:px-20 xl:px-40"
      id="order-form"
    >
      <div className="flex flex-col gap-4 rounded-xl border border-black p-2 md:p-4">
        <div className="w-full rounded-t-xl bg-secondary p-2 text-start text-sm font-bold text-white md:p-4 md:text-xl">
          {text}
        </div>
        <div>
          <div className="hidden sm:block">
            <SingleProductTable
              productDetails={productDetails}
              quantity={quantity}
              setQuantity={setQuantity}
            />
          </div>
          <div className="block sm:hidden">
            <SingleProductCard
              productDetails={productDetails}
              quantity={quantity}
              setQuantity={setQuantity}
            />
          </div>
        </div>
        <div className="flex flex-wrap gap-4 sm:flex-nowrap">
          <div className="w-full text-start">
            <form onSubmit={formik.handleSubmit}>
              <div>
                <div className="mb-3 w-full ">
                  <label
                    htmlFor="name"
                    className="mb-1 block text-sm font-normal"
                  >
                    আপনার নাম:
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    className="w-full rounded border border-slate-400 bg-slate-100 p-2 text-sm font-normal"
                    placeholder="আপনার নাম"
                  />
                  {formik.errors.name && formik.touched.name && (
                    <p className="text-xs text-red-600">{formik.errors.name}</p>
                  )}
                </div>
                <div className="mb-3 w-full ">
                  <label
                    htmlFor="name"
                    className="mb-1 block text-sm  font-normal"
                  >
                    মোবাইল নাম্বার:
                  </label>
                  <input
                    type="text"
                    id="phone"
                    name="phone"
                    value={formik.values.phone}
                    onChange={formik.handleChange}
                    className="w-full rounded border border-slate-400 bg-slate-100 p-2 text-sm font-normal"
                    placeholder="আপনার মোবাইল নাম্বার"
                    onKeyDown={(e) => {
                      if (
                        !/[\d\b]/.test(e.key) &&
                        !["ArrowLeft", "ArrowRight", "Backspace"].includes(
                          e.key,
                        )
                      ) {
                        e.preventDefault();
                      }
                    }}
                  />
                  {formik.errors.phone && formik.touched.phone && (
                    <p className="text-xs text-red-600">
                      {formik.errors.phone}
                    </p>
                  )}
                </div>
                <div className="mb-3 w-full ">
                  <label
                    htmlFor="name"
                    className="mb-1 block text-sm  font-normal"
                  >
                    আপনার ঠিকানা :
                  </label>
                  <textarea
                    id="deliveryAddress"
                    name="deliveryAddress"
                    value={formik.values.deliveryAddress}
                    onChange={formik.handleChange}
                    className="h-24 w-full rounded border border-slate-400 bg-slate-100 p-2 text-sm font-normal"
                    placeholder="বাসা, রোড, থানা, জেলা......"
                  />
                  {formik.errors.deliveryAddress &&
                    formik.touched.deliveryAddress && (
                      <p className="text-xs text-red-600">
                        {formik.errors.deliveryAddress}
                      </p>
                    )}
                </div>
                <div className="w-full">
                  <label
                    htmlFor="name"
                    className="mb-1 block text-sm font-normal"
                  >
                    অর্ডার নোট (Optional) :
                  </label>
                  <textarea
                    id="note"
                    name="note"
                    value={formik.values.note}
                    onChange={formik.handleChange}
                    className="h-28 w-full rounded border border-slate-400 bg-slate-100 px-2 pt-2 text-sm font-normal"
                    placeholder="অর্ডার নোট"
                  />
                  {/* {formik.errors.note && formik.touched.note && (
                    <p className="text-xs text-red-600">{formik.errors.note}</p>
                  )} */}
                </div>
              </div>
            </form>
          </div>
          <div className="flex w-full flex-col justify-between">
            {/* <h2>Product details</h2> */}
            <div>
              <div className="overflow-x-auto border-b-2 border-dashed border-slate-600 p-2">
                <table className="w-full border-collapse overflow-x-auto">
                  <thead>
                    <tr>
                      <th className="text-start text-sm">Product</th>
                      <th className="text-center text-sm">Price</th>
                      <th className="text-center text-sm">Quantity</th>
                      <th className="text-end text-sm">Subtotal</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="flex flex-wrap items-center py-2  md:flex-nowrap">
                        {/* <Image
                          height={100}
                          width={100}
                          src={generateProductImage(
                            productDetails?.mainImageUrl,
                          )}
                          alt={productDetails?.name}
                          className="w-16"
                        /> */}
                        <h3 className="hidden text-start text-sm font-normal  md:block">
                          {productDetails?.name}
                        </h3>
                      </td>
                      <td className="px-4 py-2 text-sm font-semibold">
                        ৳{productDetails?.discountPrice}{" "}
                      </td>
                      <td className="px-4 py-2">
                        <span className="mx-1 text-sm font-bold">
                          {quantity}
                        </span>
                      </td>
                      <td className="py-2">
                        <div className="flex w-full justify-end">
                          <div className="text-end text-sm font-semibold md:w-[100px]">
                            ৳{productDetails?.discountPrice * quantity}
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div className=" flex items-center justify-between border-b-2 border-dashed border-slate-600 py-2 text-start">
              <div className="flex flex-col gap-2">
                <p>Subtotal</p>
                <p>Delivery Charge</p>
              </div>
              <div className="flex flex-col gap-2">
                <p>৳ {productDetails?.discountPrice * quantity}</p>
                <p>৳ {deliveryCharge}</p>
              </div>
            </div>
            <div className="flex items-center justify-between py-2 text-start">
              <div>
                <p className="text-lg font-medium">Total</p>
              </div>
              <div>
                <p className="text-lg font-medium">
                  ৳ {productDetails?.discountPrice * quantity + deliveryCharge}
                </p>
              </div>
            </div>
            <div className="mt-4 flex w-full flex-wrap text-start md:flex-nowrap md:gap-4">
              <div className="mb-2 w-full md:mb-4">
                <label htmlFor="name" className="block text-sm font-normal">
                  Your Shipping Location :
                </label>
                <div className="mt-2 flex flex-col gap-2">
                  <label className="ml-2 inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio bg-primary text-[#A2784E] checked:text-[#A2784E]"
                      name="inside"
                      value="inside dhaka"
                      onClick={() => setDelivery("inside dhaka")}
                      checked={delivery === "inside dhaka"}
                    />
                    <span className="ml-2 cursor-pointer">
                      Inside Dhaka - 80 TK
                    </span>
                  </label>
                  <label className="ml-2 inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio bg-primary text-[#A2784E] checked:text-[#A2784E]"
                      name="outside"
                      value="outside dhaka"
                      onClick={() => setDelivery("outside dhaka")}
                      checked={delivery === "outside dhaka"}
                    />
                    <span className="ml-2 cursor-pointer">
                      Outside Dhaka - 150 TK
                    </span>
                  </label>
                </div>
              </div>
              <div className="mb-1 w-full md:mb-4">
                <label htmlFor="name" className="block text-sm font-normal">
                  Payment Method :
                </label>
                <div className="mt-2 flex flex-col gap-2">
                  <label className="ml-2 inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio bg-primary text-[#A2784E] checked:text-[#A2784E]"
                      name="cod"
                      value="COD"
                      onClick={() => setPaymentMethod("COD")}
                      checked={paymentMethod === "COD"}
                    />
                    <span className="ml-2 cursor-pointer">
                      Cash On Delivery
                    </span>
                  </label>
                  {/* <label className="ml-2 inline-flex items-center">
                    <input
                      type="radio"
                      className="form-radio bg-primary text-[#A2784E] checked:text-[#A2784E]"
                      name="bkash"
                      value="bkash"
                      onClick={() => setPaymentMethod("BKASH")}
                      checked={paymentMethod === "BKASH"}
                    />
                    <span className="ml-2 cursor-pointer">Bkash</span>
                  </label> */}
                </div>
              </div>
            </div>

            <div className="mb-2 text-start text-xs">
              <span>
                Your personal data will be used to process your order, support
                your experience throughout this website, and for other purposes
                described in our
              </span>
              <Link
                href={ROUTES.PRIVACY_POLICY}
                className="ml-1 font-bold text-[#092143]"
              >
                Privacy policy
              </Link>
            </div>
            <div>
              <button
                className="mt-0 w-full rounded px-4 py-4 text-xl font-bold text-white disabled:bg-gray-400 lg:text-3xl"
                style={{
                  background: !isCreatingOrder ? "#F48F4B" : "gray",
                }}
                type="button"
                onClick={() => formik.handleSubmit()}
                disabled={isCreatingOrder}
              >
                অর্ডার করুন ৳{" "}
                {productDetails?.discountPrice * quantity + deliveryCharge}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DirectOrderForm;

interface SingleProdProps {
  productDetails: SingleProductDetailsType;
  quantity: number;
  setQuantity: (qnt: number) => void;
}

const SingleProductTable = ({
  productDetails,
  quantity,
  setQuantity,
}: SingleProdProps) => {
  return (
    <div className="overflow-x-auto rounded-xl border border-slate-300 bg-slate-100 p-2">
      <table className="w-full border-collapse overflow-x-auto">
        <thead>
          <tr>
            <th className="text-start">Product</th>
            <th className="text-center">Price</th>
            <th className="text-center">Quantity</th>
            <th className="text-center">Subtotal</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td className="flex items-center py-2">
              <Image
                height={100}
                width={100}
                src={generateProductImage(productDetails?.mainImageUrl)}
                alt={productDetails?.name}
                className="w-20 border border-slate-300"
              />
              <h3 className="ml-4 text-base font-medium">
                {productDetails?.name}
              </h3>
            </td>
            <td className="px-4 py-2 text-center text-lg font-semibold">
              ৳ {productDetails?.discountPrice}{" "}
              {productDetails?.price !== productDetails?.discountPrice ? (
                <span className="text-sm font-normal text-slate-500  line-through">
                  ৳{productDetails?.price}
                </span>
              ) : (
                ""
              )}
            </td>
            <td className="px-4 py-2 text-center">
              <div className="flex w-full items-center justify-center">
                <div className="mb-4 mt-2 flex w-[150px] items-center justify-between rounded-lg border-2 border-[#092143]">
                  <button
                    className="rounded-l-lg bg-gray-300 px-4 py-2 text-xl"
                    onClick={() => {
                      if (quantity > 1) {
                        setQuantity(quantity - 1);
                      } else {
                        minimumQuantityModal();
                      }
                    }}
                  >
                    -
                  </button>
                  <span className="mx-1">{quantity}</span>
                  <button
                    className="rounded-r-lg bg-gray-300 px-4 py-2  text-xl"
                    onClick={() => {
                      if (quantity < 5) {
                        setQuantity(quantity + 1);
                      } else {
                        maximumQuantityModal();
                      }
                    }}
                  >
                    +
                  </button>
                </div>
              </div>
            </td>
            <td className="px-4 py-2">
              <div className="flex w-full justify-center">
                <div className="w-[100px] text-center font-semibold">
                  ৳{productDetails?.discountPrice * quantity}
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};
const SingleProductCard = ({
  productDetails,
  quantity,
  setQuantity,
}: SingleProdProps) => {
  return (
    <div className="text-start">
      <div className="flex w-full gap-2">
        <Image
          height={100}
          width={100}
          src={generateProductImage(productDetails.mainImageUrl)}
          alt={productDetails?.name}
          className="h-[60px] w-[60px] rounded-md"
        />
        <h3 className="text-sm font-normal">{productDetails?.name}</h3>
      </div>

      <div className="flex-1 ">
        <div className="my-2 flex flex-col">
          <div className="flex items-center justify-between">
            <span className="text-sm font-bold text-gray-600">Price :</span>
            <span className="text-lg font-semibold">
              ৳ {productDetails?.discountPrice}{" "}
              {productDetails?.price !== productDetails?.discountPrice ? (
                <span className="text-sm text-slate-500 line-through">
                  ৳{productDetails?.price}
                </span>
              ) : (
                ""
              )}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-bold text-gray-600">Quantity :</span>
            <div className="mb-4 mt-2 flex w-[150px] items-center justify-between rounded-lg border-2 border-primary">
              <button
                className="rounded-l-lg bg-gray-200 px-4 py-2"
                onClick={() => {
                  if (quantity > 1) {
                    setQuantity(quantity - 1);
                  } else {
                    minimumQuantityModal();
                  }
                }}
              >
                -
              </button>
              <span className="mx-1">{quantity}</span>
              <button
                className="rounded-r-lg bg-gray-200 px-4 py-2"
                onClick={() => {
                  setQuantity(quantity + 1);
                }}
              >
                +
              </button>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-bold text-gray-600">Subtotal :</span>
            <span className="text-lg font-semibold">
              ৳ {productDetails?.discountPrice * quantity}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
