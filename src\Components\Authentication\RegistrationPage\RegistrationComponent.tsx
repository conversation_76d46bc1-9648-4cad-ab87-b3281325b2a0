"use client";
import { ROUTES } from "@/Routes";
import { BASE_URL } from "@/environment/environment";
import axios from "axios";
import { useFormik } from "formik";
import { signIn } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";
import { FaRegEye, FaRegEyeSlash } from "react-icons/fa";
import * as Yup from "yup";

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  email: Yup.string().email("Invalid email").required("email is required"),
  mobileNumber: Yup.string()
    .required("Phone number is required")
    .min(11)
    .max(11),
  username: Yup.string().required("Username is required").min(6),
  password: Yup.string().required("Password is required").min(6),
});

const RegistrationComponent = () => {
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const router = useRouter();

  const formik = useFormik({
    initialValues: {
      name: "",
      email: "",
      mobileNumber: "",
      username: "",
      password: "",
      confirmPassword: "",
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      const userData = {
        name: values.name,
        email: values.email,
        mobileNumber: values.mobileNumber,
        username: values.username,
        password: values.password,
        role: "user",
        signUpMethod: "credentials",
        signUpBy: "self",
      };
      try {
        const res = await axios.post(`${BASE_URL}/users/add-new`, userData);
        if (res.status === 201) {
          setError(res.data.message);
        } else if (res.status === 200) {
          toast.success("Account Created successfully");
          router.push(ROUTES.LOG_IN(""));
        }
      } catch (error: any) {
        setError("something wrong try again");
      }
    },
  });

  return (
    <div className="mt-2">
      <div className="flex items-center justify-center pb-8">
        <div className="relative w-96 rounded-lg border border-primary bg-[#F48F4B0e] p-8 shadow-lg">
          <div className="absolute left-[50%] top-[-25px] h-16 w-16 translate-x-[-50%] rounded-full border-4 border-white">
            {/* Company Logo */}
            <Image
              height={100}
              width={100}
              src="https://cdn-icons-png.flaticon.com/512/295/295128.png" // Replace with the path to your company logo image
              alt="Company Logo"
              className="h-full w-full"
            />
          </div>
          <h1 className="mb-5 mt-2 text-center text-2xl font-semibold">
            Registration
          </h1>

          {/* Login Form */}
          <form className="space-y-4">
            <div className="flex space-x-2">
              <div className="flex-1">
                {/* <label htmlFor="firstName" className="block font-medium">
                  Name
                </label> */}
                <input
                  type="text"
                  id="firstName"
                  name="name"
                  className="w-full rounded-lg border px-4 py-2 text-sm font-normal focus:ring focus:ring-blue-300"
                  placeholder="Enter your first name"
                  onChange={formik.handleChange}
                />
                {formik.errors.name && formik.touched.name ? (
                  <p className="text-[10px] text-red-600">
                    {formik.errors.name}
                  </p>
                ) : (
                  ""
                )}
              </div>
            </div>

            <div>
              {/*  <label htmlFor="mobile" className="block font-medium">
                Mobile Number
              </label> */}
              <input
                type="text"
                id="mobile"
                name="mobileNumber"
                className="w-full rounded-lg border px-4 py-2 text-sm font-normal focus:ring focus:ring-blue-300"
                placeholder="Enter your mobile number"
                onChange={formik.handleChange}
                onKeyDown={(e) => {
                  // Allow only digits (0-9), backspace, and arrow keys
                  if (
                    !/[\d\b]/.test(e.key) &&
                    !["ArrowLeft", "ArrowRight", "Backspace"].includes(e.key)
                  ) {
                    e.preventDefault();
                  }
                }}
              />
              {formik.errors.mobileNumber && formik.touched.mobileNumber ? (
                <p className="text-[10px] text-red-600">
                  {formik.errors.mobileNumber}
                </p>
              ) : (
                ""
              )}
            </div>

            <div>
              {/* <label htmlFor="email" className="block font-medium">
                Email
              </label> */}
              <input
                type="email"
                id="email"
                name="email"
                className="w-full rounded-lg border px-4 py-2 text-sm font-normal focus:ring focus:ring-blue-300"
                placeholder="Enter your email address"
                onChange={formik.handleChange}
              />
              {formik.errors.email && formik.touched.email ? (
                <p className="text-[10px] text-red-600">
                  {formik.errors.email}
                </p>
              ) : (
                ""
              )}
            </div>

            <div>
              {/* <label htmlFor="username" className="block font-medium">
                Username
              </label> */}
              <input
                type="text"
                id="username"
                name="username"
                className="w-full rounded-lg border px-4 py-2 text-sm font-normal focus:ring focus:ring-blue-300"
                placeholder="Enter your username"
                onChange={formik.handleChange}
              />
              {formik.errors.username && formik.touched.username ? (
                <p className="text-[10px] text-red-600">
                  {formik.errors.username}
                </p>
              ) : (
                ""
              )}
            </div>

            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                className="w-full rounded-lg border px-4 py-2 text-sm font-normal focus:ring focus:ring-blue-300"
                placeholder="Enter your password"
                onChange={formik.handleChange}
              />
              <div className="absolute right-[10px] top-[10px]">
                <button
                  type="button"
                  className="flex items-center gap-2 text-[12px]"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <>
                      <FaRegEye className="text-lg text-gray-700" />
                    </>
                  ) : (
                    <>
                      <FaRegEyeSlash className="text-lg text-gray-700" />
                    </>
                  )}
                </button>
              </div>
              {formik.errors.password && formik.touched.password ? (
                <p className="text-[10px] text-red-600">
                  {formik.errors.password}
                </p>
              ) : (
                ""
              )}
            </div>
            {error ? (
              <span className="text-sm font-normal" style={{ color: "red" }}>
                {error}
              </span>
            ) : (
              ""
            )}

            <button
              type="submit"
              className="w-full rounded-lg bg-secondary py-2 font-normal text-white hover:bg-primary"
              onClick={(e) => {
                e.preventDefault();
                formik.handleSubmit();
              }}
            >
              Register
            </button>
          </form>

          {/* Quick Login Options */}
          <div className="mt-6">
            {/* <p className="text-center text-gray-600">Or login with</p> */}
            <div>
              <button
                className="flex w-full items-center  justify-center  gap-2 rounded-lg border border-primary p-4 py-2 font-normal text-white"
                onClick={() => signIn("google")}
              >
                <Image
                  height={100}
                  width={100}
                  src="https://i.ibb.co/JQr2Whx/download-removebg-preview.png"
                  alt="jfjf"
                  style={{ height: "20px", width: "20px" }}
                />
                <p className="text-sm text-black">Log In Using Google</p>
              </button>
              {/*  <button
                className="w-full py-2 text-white bg-blue-500 rounded-lg hover:bg-blue-600 font-normal mt-2"
                onClick={() => signIn("facebook")}
              >
                Log in with Facebook
              </button> */}
            </div>
          </div>

          {/* Create Account Button */}
          <div className="mt-6 text-center">
            <Link href={ROUTES.LOG_IN("")} className="text-sm font-normal">
              <span>Already have an account ? </span>
              <span className="text-primary hover:text-secondary hover:underline">
                Log In
              </span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegistrationComponent;
