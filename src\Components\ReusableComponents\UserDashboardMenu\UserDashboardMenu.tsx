"use client";

import { ROUTES } from "@/Routes";
import { getUserDetailsApi } from "@/services/userServices";
import { useQuery } from "@tanstack/react-query";
import { signOut } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

const UserDashboardMenu = ({ accessToken }: { accessToken?: string }) => {
  const pathname = usePathname();
  const router = useRouter();

  const { api, getKey } = getUserDetailsApi();
  const { data: userDetails, isLoading } = useQuery(getKey(), api, {
    refetchOnWindowFocus: false,
    enabled: accessToken ? true : false,
  });

  return (
    <div className="flex flex-col border p-2" style={{ minWidth: "150px" }}>
      <div className="mb-2 flex flex-col items-center">
        <Image
          src={
            userDetails?.result?.profilePicture
              ? userDetails?.result?.profilePicture
              : "https://png.pngtree.com/element_our/20190604/ourmid/pngtree-user-avatar-boy-image_1482937.jpg"
          }
          alt=""
          height={100}
          width={100}
          className="h-[80px] w-[80px] rounded-full"
        />
        <p className="text-sm font-normal">
          {userDetails?.result?.name ?? "User Name"}
        </p>
      </div>
      <hr className="mb-2" />
      <div className="flex md:block">
        <div
          style={{
            backgroundColor:
              pathname.split("/")[pathname.split("/").length - 1] ===
              "dashboard"
                ? "#e1e3e1"
                : "",
          }}
          className="w-full rounded-lg p-2 text-sm font-normal"
        >
          <Link href={ROUTES.DASHBOARD.HOME}>Dashboard</Link>
        </div>
        <div
          style={{
            backgroundColor: pathname.includes("orders") ? "#e1e3e1" : "",
          }}
          className="w-full rounded-lg p-2 text-sm font-normal"
        >
          <Link href={ROUTES.DASHBOARD.ORDERS.HOME}>Orders</Link>
        </div>
      </div>
      <div className="flex md:block">
        {/* <div
          style={{
            backgroundColor: pathname.includes("support") ? "#e1e3e1" : "",
          }}
          className="w-full rounded-lg p-2 text-sm font-normal"
        >
          <Link href={ROUTES.DASHBOARD.SUPPORT.HOME}>Support</Link>
        </div> */}
        <div
          style={{
            backgroundColor: pathname.includes("profile") ? "#e1e3e1" : "",
          }}
          className="w-full rounded-lg p-2 text-sm font-normal"
        >
          <Link href={ROUTES.DASHBOARD.PROFILE}>Profile</Link>
        </div>
      </div>
      <div>
        <Link
          href={ROUTES.DASHBOARD.ACCOUNT}
          style={{
            backgroundColor: pathname.includes("account") ? "#e1e3e1" : "",
          }}
          className="rounded-lg p-2 text-sm font-normal"
        >
          Account Details
        </Link>
      </div>
      <button
        className="mt-4 rounded-lg bg-orange-400 p-2 text-sm text-white"
        onClick={() => {
          document.cookie = `GMK=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
          signOut();
          router.push(ROUTES.HOME);
        }}
      >
        Log out
      </button>
    </div>
  );
};

export default UserDashboardMenu;
