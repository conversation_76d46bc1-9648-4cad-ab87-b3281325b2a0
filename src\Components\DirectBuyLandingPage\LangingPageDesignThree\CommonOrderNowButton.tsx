import { FaOpencart } from "react-icons/fa";

interface Props {
  handleClick: () => void; // Specify the type for handleClick
}

export const OrderButtonVersionOne = ({ handleClick }: Props) => {
  return (
    <button
      className="flex items-center rounded-full border-2 border-dashed border-white bg-secondary px-8 py-4 text-2xl font-bold text-white transition-all duration-200 hover:scale-105 sm:text-3xl"
      onClick={handleClick}
      type="button"
    >
      <FaOpencart />
      <span className="ml-3">অর্ডার করুন</span>
    </button>
  );
};

export const OrderButtonVersionTwo = ({ handleClick }: Props) => {
  return (
    <button
      className="flex items-center rounded-full border-2 border-dashed border-gray-800 bg-secondary px-8 py-4 text-2xl font-bold text-gray-800 transition-all duration-200 hover:scale-105 sm:text-3xl"
      onClick={handleClick}
      type="button"
    >
      <FaOpencart />
      <span className="ml-3 mr-0 md:mr-3">অর্ডার করুন</span>
    </button>
  );
};

export const OrderButtonVersionthree = ({ handleClick }: Props) => {
  return (
    <button
      className=" flex items-center rounded-full border-2 border-dashed border-secondary bg-white px-8 py-4 text-2xl font-bold text-secondary transition-all duration-200 hover:scale-105 sm:text-3xl"
      onClick={handleClick}
      type="button"
    >
      <FaOpencart />
      <span className="ml-3">অর্ডার করুন</span>
    </button>
  );
};

export const OrderButtonVersionFour = ({ handleClick }: Props) => {
  return (
    <button
      className="relative inline-block text-2xl sm:text-3xl group"
      onClick={handleClick}
      type="button"
    >
      <span className="relative z-10 block px-6 py-4 overflow-hidden font-medium leading-tight text-gray-800 transition-colors duration-300 ease-out border-2 border-gray-900 rounded-lg group-hover:text-white">
        <span className="absolute inset-0 w-full h-full px-6 py-4 rounded-lg bg-gray-50"></span>
        <span className="absolute left-0 w-[260px] h-[260px] -ml-2 transition-all duration-300 origin-top-right -rotate-90 -translate-x-full translate-y-14 bg-gray-900 group-hover:-rotate-180 ease"></span>
        <span className="relative flex items-center gap-x-2">
          <FaOpencart />
          অর্ডার করুন
        </span>
      </span>
      <span className="absolute bottom-0 right-0 w-full h-16 -mb-1 -mr-1 transition-all duration-200 ease-linear bg-gray-900 rounded-lg group-hover:mb-0 group-hover:mr-0" data-rounded="rounded-lg"></span>
    </button>
  );
};
export const OrderButtonVersionFive = ({ handleClick }: Props) => {
  return (
    <button
      className="inline-flex text-3xl sm:text-5xl gap-3 font-black 
bg-[#A2449A] text-white px-8 py-5 rounded-2xl 
hover:scale-105 transition-all duration-300 ease-in-out"
      onClick={handleClick}
      type="button"
    >
      অর্ডার করুন
    </button>
  );
};
