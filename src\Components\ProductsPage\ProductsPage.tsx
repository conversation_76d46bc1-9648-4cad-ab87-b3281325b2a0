"use client";
import { ProductListType } from "@/Types/productTypes";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { IoFilterCircleOutline } from "react-icons/io5";
import LeftSideFilterSlider from "../ReusableComponents/LeftSideFilterSlider/LeftSideFilterSlider";
import CustomPagination from "../ReusableComponents/PaginationComponent/CustomPagination";
import ProductFilterOptions from "../ReusableComponents/ProductFilterOptions/ProductFilterOptions";
import ProductFilterOptionsTwo from "../ReusableComponents/ProductFilterOptions/ProductFilterOptionsTwo";
import ProductsListViewer from "../ReusableComponents/ProductsListViewer/ProductsListViewer";
interface Props {
  accessToken: string;
  data: ProductListType;
  pageType: string;
  brandName?: string;
  categoryName?: string;
  subCategoryName?: string;
  description?: string;
}
const ProductsPage = ({
  accessToken,
  data,
  pageType,
  brandName,
  categoryName,
  subCategoryName,
  description,
}: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [openFilterSlider, setOpenFilterSlider] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(true);

  const brand = searchParams.get("brand") || "";
  const productType = searchParams.get("productType") || "";
  const category = searchParams.get("category") || "";
  const skinType = searchParams.get("skinType") || "";
  const skinConcern = searchParams.get("skinConcern") || "";
  const page = searchParams.get("page") || 1;

  useEffect(() => {
    if (data) {
      setIsLoading(false);
    }
  }, [data]);

  const handlePageChange = (value: number) => {
    if (pageType === "brand") {
      router.push(`/brand/${brandName}?page=${value}`);
    } else if (pageType === "category") {
      router.push(`/category/${categoryName}?page=${value}`);
    } else if (pageType === "subCategory") {
      router.push(`/category/${categoryName}/${subCategoryName}?page=${value}`);
    } else if (pageType === "new-arrival") {
      router.push(
        `/new-arrival?page=${value}${productType && `&productType=${productType}`}${brand && `&brand=${brand}`}${category && `&category=${category}`}${skinType && `&skinType=${skinType}`}${skinConcern && `&skinConcern=${skinConcern}`}`,
      );
    } else if (pageType === "best-seller") {
      router.push(
        `/best-seller?page=${value}${productType && `&productType=${productType}`}${brand && `&brand=${brand}`}${category && `&category=${category}`}${skinType && `&skinType=${skinType}`}${skinConcern && `&skinConcern=${skinConcern}`}`,
      );
    } else {
      router.push(
        `/products?page=${value}${productType && `&productType=${productType}`}${brand && `&brand=${brand}`}${category && `&category=${category}`}${skinType && `&skinType=${skinType}`}${skinConcern && `&skinConcern=${skinConcern}`}`,
      );
    }
  };

  return (
    <div>
      <div>
        {/* <div className="my-2 hidden md:col-span-2 md:block ">
          <ProductFilterOptions
            pageType={pageType}
            brandName={brandName}
            categoryName={categoryName}
            subCategoryName={subCategoryName}
          />
        </div> */}

        <div>
          <div className="hidden bg-white shadow md:block">
            <ProductFilterOptions
              pageType={pageType}
              brandName={brandName}
              categoryName={categoryName}
              subCategoryName={subCategoryName}
            />
          </div>
          <div className="mb-4 mt-2 flex w-full items-center justify-between rounded-md border px-2 py-3 md:hidden">
            <button
              onClick={() => setOpenFilterSlider(true)}
              className="flex items-center gap-1"
            >
              <IoFilterCircleOutline />
              All Filter
            </button>
            <div className="">
              <ProductFilterOptionsTwo
                pageType={pageType}
                brandName={brandName}
                categoryName={categoryName}
                subCategoryName={subCategoryName}
              />
            </div>
          </div>
          <ProductsListViewer
            productList={data?.results}
            loading={isLoading}
            accessToken={accessToken}
            xxl={6}
            xl={5}
            lg={4}
            md={3}
            sm={2}
          />
          {Number(data?.totalNoOfPages) > 1 ? (
            <CustomPagination
              currentPage={Number(page)}
              totalPages={Number(data?.totalNoOfPages)}
              handlePageChange={handlePageChange}
            />
          ) : (
            ""
          )}
          {description ? (
            <div className="mt-4">
              <div
                dangerouslySetInnerHTML={{
                  __html: description ? description : "<p>No content</p>",
                }}
                className="textStyle"
              />
            </div>
          ) : (
            ""
          )}
        </div>
      </div>
      <LeftSideFilterSlider
        openFilterSlider={openFilterSlider}
        setOpenFilterSlider={setOpenFilterSlider}
        pageType={pageType}
      />
    </div>
  );
};

export default ProductsPage;
