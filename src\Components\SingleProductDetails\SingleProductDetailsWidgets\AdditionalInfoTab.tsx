import { SingleProductDetailsType } from "@/Types/productTypes";
import React from "react";
interface Props {
  productDetails: SingleProductDetailsType;
}

const AdditionalInfoTab = ({ productDetails }: Props) => {
  return (
    <div
      dangerouslySetInnerHTML={{
        __html: productDetails?.ingredients
          ? productDetails?.ingredients
          : "<p>No information</p>",
      }}
    />
  );
};

export default AdditionalInfoTab;
