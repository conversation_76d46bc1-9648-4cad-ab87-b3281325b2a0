import ProductsListViewer from "@/Components/ReusableComponents/ProductsListViewer/ProductsListViewer";
import TitleViewer from "@/Components/ReusableComponents/TitleViewer/TitleViewer";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ROUTES } from "@/Routes";
import { getProductListApi } from "@/services/productsServices";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { useRouter } from "next/navigation";

interface Props {
  productType?: string;
  productTypeSlug?: string;
  imageUrl?: string;
  accessToken?: string;
  name?: string;
}

const HomepageSingleContentWithApi = ({
  productType,
  imageUrl,
  accessToken,
  productTypeSlug,
  name,
}: Props) => {
  const router = useRouter();
  const { api, getKey } = getProductListApi({
    // productType: productTypeSlug ?? "",
    brand: productType === "brand" ? productTypeSlug : "",
    productCategory: productType === "category" ? productTypeSlug : "",
    size: 10,
  });
  const { data, isLoading } = useQuery(getKey(), api, {
    refetchOnWindowFocus: false,
    enabled: productTypeSlug?.length ? true : false,
  });
  return (
    <div className="mt-4 md:mt-8">
      {imageUrl ? (
        <Image
          height={100}
          width={100}
          src={generateProductImage(imageUrl)}
          alt="Carousel Image 1"
          className=" mb-4 mt-8 max-h-[500px] w-full"
        />
      ) : (
        ""
      )}
      <TitleViewer
        titleOne="Shop From"
        titleTwo={name ?? productTypeSlug ?? ""}
        seeAllButton={true}
        handleClick={() =>
          router.push(
            productType === "category"
              ? ROUTES.PRODUCTS.CATEGORY(productTypeSlug ?? "")
              : ROUTES.BRANDS.VIEW(productTypeSlug ?? ""),
          )
        }
      />
      <ProductsListViewer
        productList={data?.results}
        loading={isLoading}
        accessToken={accessToken ?? ""}
        xxl={6}
        xl={5}
        lg={4}
        md={3}
        sm={2}
      />
    </div>
  );
};

export default HomepageSingleContentWithApi;
