"use client";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleCartItem } from "@/Types/cartPageTypes";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { removeAllFromCart, removeFromCart } from "@/services/cartServices";
import { addToCheckoutApi } from "@/services/checkoutServices";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { NoCartItemFound } from "../ReusableComponents/NoResultFound/NoResultFound";
import SpinnerLoader from "../ReusableComponents/SpinnerLoader/SpinnerLoader";
import TitleViewer from "../ReusableComponents/TitleViewer/TitleViewer";
import { deleteModal, notLoggedInModal } from "../utils/commonModal";
import {
  SingleItemOfCartCard,
  SingleItemOfCartTable,
} from "./SingleItemOfCart";
interface Props {
  accessToken: string;
  cartItems?: SingleCartItem[];
}

const CartPageOverview = ({ accessToken, cartItems }: Props) => {
  //need to sync with backend
  const queryClient = useQueryClient();
  const router = useRouter();

  const [selectedForCheckout, setSelectedForCheckout] = useState<
    SingleCartItem[]
  >([]);

  const [totalQuantity, setTotalQuantity] = useState<number>(0);
  const [totalProductPrice, setTotalProductPrice] = useState<number>(0);
  const [totalDiscount, setTotalDiscount] = useState<number>(0);
  const [updatedCartItems, setUpdatedCartItems] = useState<SingleCartItem[]>();
  const [changedCount, setChangedCount] = useState<number>(0);
  const [updateTotalCount, setUpdateTotalCount] = useState<number>(0);
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);

  const { userHeaderDetails }: ContextDataType = useContext(LocalDataContext);

  const handleSelectOrUnselectAll = (type: string) => {
    if (type === "select") {
      if (updatedCartItems?.length) {
        setSelectedForCheckout(updatedCartItems);
        setUpdateTotalCount(updateTotalCount + 1);
      }
    } else {
      setSelectedForCheckout([]);
    }
  };

  const handleSelectOrUnselectSingle = (product: SingleCartItem) => {
    const isExist = selectedForCheckout.some(
      (item: SingleCartItem) => item.productId === product.productId,
    );
    if (isExist) {
      setSelectedForCheckout(
        selectedForCheckout.filter(
          (singleItem: SingleCartItem) =>
            singleItem.productId !== product.productId,
        ),
      );
    } else {
      setSelectedForCheckout([...selectedForCheckout, product]);
    }
  };

  const handleUpdateSelectedItem = () => {
    const arr: SingleCartItem[] = [];
    selectedForCheckout.map((single: SingleCartItem) => {
      const item = updatedCartItems?.find(
        (sin: SingleCartItem) => sin.productId === single.productId,
      );
      if (item) {
        arr.push(item);
      }
    });
    setSelectedForCheckout(arr);
    setUpdateTotalCount(updateTotalCount + 1);
  };

  const handleUpdateQuantity = (
    product: SingleCartItem,
    newQuantity: number,
  ) => {
    if (updatedCartItems) {
      const arr = updatedCartItems;
      const ind = arr.indexOf(product);
      arr[ind].quantity = newQuantity;
      setUpdatedCartItems(arr);
      setChangedCount(changedCount + 1);
    }
  };

  const { api: AddToCheckoutApi } = addToCheckoutApi();

  const { mutateAsync: addToCheckoutMutateAsync } = useMutation(
    AddToCheckoutApi,
    {
      onSuccess: () => {
        router.push(ROUTES.CHECKOUT);
      },
    },
  );

  const handleCheckout = () => {
    const data = {
      products: selectedForCheckout?.map((single: SingleCartItem) => {
        return {
          productDetails: single?.productId,
          quantity: single?.quantity,
        };
      }),
    };
    if (accessToken) {
      toast.promise(
        addToCheckoutMutateAsync(data),
        {
          success: "Product Added to checkout Successful",
          error: "Error Adding Product to checkout",
          loading: "Adding Product to checkout",
        },
        {
          id: "add-to-checkout-product",
        },
      );
    } else {
      notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
    }
  };

  const { api: DeleteFromCartApi } = removeFromCart();

  const { mutateAsync: removeFromCartMutateAsync } = useMutation(
    DeleteFromCartApi,
    {
      onSuccess: () => {
        queryClient.invalidateQueries();
      },
    },
  );
  const handleDeleteFromCart = (id: string) => {
    if (accessToken) {
      deleteModal("Cart", () =>
        toast.promise(
          removeFromCartMutateAsync(id),
          {
            success: "Product removed from cart Successful",
            error: "Error removing Product from cart",
            loading: "Removing Product from cart",
          },
          {
            id: "remove-from-checkout",
          },
        ),
      );
    } else {
      notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
    }
  };

  const { api: DeleteAllFromCartApi } = removeAllFromCart();

  const { mutateAsync: removeAllFromCartMutateAsync } = useMutation(
    DeleteAllFromCartApi,
    {
      onSuccess: () => {
        setUpdatedCartItems([]);
        queryClient.invalidateQueries();
      },
    },
  );

  const handleDeleteAllFromCart = () => {
    if (accessToken) {
      deleteModal("Cart", () =>
        toast.promise(
          removeAllFromCartMutateAsync(),
          {
            success: "Cart cleared Successful",
            error: "Error removing cart",
            loading: "Clearing cart",
          },
          {
            id: "remove-all-from-checkout",
          },
        ),
      );
    } else {
      notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
    }
  };

  useEffect(() => {
    if (cartItems?.length) {
      setUpdatedCartItems(cartItems);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cartItems]);

  useEffect(() => {
    if (updatedCartItems) {
      if (isInitialLoad) {
        handleSelectOrUnselectAll("select");
        setIsInitialLoad(false);
      } else if (selectedForCheckout.length === updatedCartItems?.length) {
        handleSelectOrUnselectAll("select");
        setIsInitialLoad(false);
      } else {
        handleUpdateSelectedItem();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updatedCartItems, changedCount]);

  useEffect(() => {
    let quantity = 0;
    let price = 0;
    let discount = 0;
    selectedForCheckout?.map((singleItem: SingleCartItem) => {
      (quantity += singleItem.quantity),
        (price += singleItem.price * singleItem.quantity);
      discount +=
        (singleItem.price - singleItem.discountPrice) * singleItem.quantity;
    });

    setTotalQuantity(quantity);
    setTotalProductPrice(price);
    setTotalDiscount(discount);
    if (selectedForCheckout?.length) {
      const data = {
        event: "view_cart",
        url: window.location.href,
        value: price - discount,
        user_log_in_status: userHeaderDetails?.userDetails?.name
          ? "logged in"
          : "not logged in",
        user_id: userHeaderDetails?.userDetails?._id ?? "",
        user_name: userHeaderDetails?.userDetails?.name ?? "",
        user_phone_number: userHeaderDetails?.userDetails?.mobileNumber ?? "",
        user_email: userHeaderDetails?.userDetails?.email ?? "",
        user_zip: userHeaderDetails?.userDetails?.zipCode ?? "",
        user_district: userHeaderDetails?.userDetails?.district ?? "",
        items: selectedForCheckout
          ? selectedForCheckout.map((single: SingleCartItem) => {
              return {
                item_id: single?.productId,
                item_name: single?.name,
                price: single?.discountPrice,
                item_brand: single?.brand,
                item_category: single?.productCategory,
                // slug: single?.slug,
                sku: single?.productId?.replace("GMK-", ""),
              };
            })
          : null,
      };
      gtmDataLayerDataPass(data);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedForCheckout, updateTotalCount]);

  return (
    <div className="mt-2">
      <TitleViewer titleTwo="Cart" titleOne="" seeAllButton={false} />
      <>
        {updatedCartItems?.length ? (
          <div className="flex flex-col gap-2 md:flex-row">
            {/* Product list in big screen*/}
            <div className="hidden h-1/2 rounded-lg border p-4 shadow-sm md:block md:w-2/3">
              <div className="flex items-center justify-between pb-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={
                      selectedForCheckout?.length === updatedCartItems?.length
                    }
                    onClick={() =>
                      handleSelectOrUnselectAll(
                        selectedForCheckout?.length === updatedCartItems?.length
                          ? "unselect"
                          : "select",
                      )
                    }
                  />
                  <p
                    className="cursor-pointer"
                    onClick={() =>
                      handleSelectOrUnselectAll(
                        selectedForCheckout?.length === updatedCartItems?.length
                          ? "unselect"
                          : "select",
                      )
                    }
                  >
                    {selectedForCheckout?.length === updatedCartItems?.length
                      ? "Unselect All"
                      : "Select All"}
                  </p>
                </div>
                <p
                  className="cursor-pointer"
                  onClick={() => handleDeleteAllFromCart()}
                >
                  Delete all
                </p>
              </div>
              <hr className="pb-2" />
              <div className="overflow-x-auto">
                <table className="w-full border-collapse overflow-x-auto">
                  <thead>
                    <tr>
                      <th></th>
                      <th className="text-center">Product</th>
                      <th className="text-center">Price</th>
                      <th className="text-center">Quantity</th>
                      <th className="text-center">Subtotal</th>
                      <th className="text-center">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {updatedCartItems?.map(
                      (product: SingleCartItem, index: number) => (
                        <SingleItemOfCartTable
                          key={product._id}
                          product={product}
                          selectedForCheckout={selectedForCheckout}
                          handleSelectOrUnselectSingle={
                            handleSelectOrUnselectSingle
                          }
                          handleUpdateQuantity={handleUpdateQuantity}
                          handleDeleteFromCart={handleDeleteFromCart}
                          isLastItem={updatedCartItems?.length === index + 1}
                        />
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            </div>
            {/* Product list in big screen end*/}
            {/* Product list in small screen */}
            <div className="order-2 block rounded-lg border p-2 shadow-md md:hidden md:w-2/3">
              <div className="flex items-center justify-between pb-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedForCheckout?.length === cartItems?.length}
                    onClick={() =>
                      handleSelectOrUnselectAll(
                        selectedForCheckout?.length === cartItems?.length
                          ? "unselect"
                          : "select",
                      )
                    }
                  />
                  <p
                    className="cursor-pointer"
                    onClick={() =>
                      handleSelectOrUnselectAll(
                        selectedForCheckout?.length === cartItems?.length
                          ? "unselect"
                          : "select",
                      )
                    }
                  >
                    {selectedForCheckout?.length === cartItems?.length
                      ? "Unselect All"
                      : "Select All"}
                  </p>
                </div>
                <p
                  className="cursor-pointer"
                  onClick={() => handleDeleteAllFromCart()}
                >
                  Delete all
                </p>
              </div>
              <hr className="pb-2" />
              {updatedCartItems?.map(
                (product: SingleCartItem, index: number) => (
                  <SingleItemOfCartCard
                    key={product._id}
                    product={product}
                    selectedForCheckout={selectedForCheckout}
                    handleSelectOrUnselectSingle={handleSelectOrUnselectSingle}
                    handleUpdateQuantity={handleUpdateQuantity}
                    handleDeleteFromCart={handleDeleteFromCart}
                    isLastItem={updatedCartItems?.length === index + 1}
                  />
                ),
              )}
              <div className="flex w-full items-center justify-center px-2">
                <button
                  className="mt-4 w-full rounded-lg bg-primary px-4 py-2 font-normal text-white disabled:bg-gray-400"
                  disabled={selectedForCheckout.length ? false : true}
                  onClick={() => handleCheckout()}
                  type="button"
                >
                  Checkout
                </button>
              </div>
            </div>
            {/* Product list in small screen  end*/}

            {/* Right Part: Cart Summary Table */}
            <div className="h-1/2 rounded-lg border-2 p-4 shadow-lg md:w-1/3">
              <h2 className="mb-4 text-center text-2xl font-semibold">
                Cart Summary
              </h2>
              <table className="w-full ">
                <tbody>
                  <tr style={{ backgroundColor: "#E1E3E7" }}>
                    <td className="p-2 text-sm font-normal">Quantity</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">{totalQuantity}</td>
                  </tr>
                  <tr style={{ backgroundColor: "white" }}>
                    <td className="p-2 text-sm font-normal">Product Price</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      ৳ {totalProductPrice}
                    </td>
                  </tr>
                  <tr style={{ backgroundColor: "#E1E3E7" }}>
                    <td className="p-2 text-sm font-normal">Discount</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      ৳ {totalDiscount}
                    </td>
                  </tr>
                  <tr style={{ backgroundColor: "white" }}>
                    <td className="p-2 text-sm font-normal">Subtotal Price</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      ৳ {totalProductPrice - totalDiscount}
                    </td>
                  </tr>
                  <tr style={{ backgroundColor: "#E1E3E7" }}>
                    <td className="p-2 text-sm font-normal">Delivery Charge</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      Inside Dhaka : ৳ 80 <br /> Outside Dhaka : ৳ 150
                    </td>
                  </tr>
                  <tr style={{ backgroundColor: "white" }}>
                    <td className="p-2 text-sm font-normal">Total</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      ৳ {totalProductPrice - totalDiscount} + delivery charge
                    </td>
                  </tr>
                </tbody>
              </table>
              <div className="flex w-full items-center justify-center px-2">
                <button
                  className="mt-4 w-full rounded-lg bg-primary px-4 py-2 font-normal text-white disabled:bg-gray-400"
                  disabled={selectedForCheckout.length ? false : true}
                  onClick={() => handleCheckout()}
                  type="button"
                >
                  Checkout
                </button>
              </div>
            </div>
          </div>
        ) : (
          <NoCartItemFound />
        )}
      </>
    </div>
  );
};

export default CartPageOverview;
