import { useEffect, useRef, useState } from "react";
import { IoChevronDown, IoChevronUp } from "react-icons/io5";

interface Option {
  value: string;
  label: string;
}

interface DropdownProps {
  options: Option[];
  selectedValue: string;
  onSelect: (value: string) => void;
  width: string;
  placeholder: string;
}

const CustomDropdown: React.FC<DropdownProps> = ({
  options,
  selectedValue,
  onSelect,
  width,
  placeholder,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [filteredOptions, setFilteredOptions] = useState<Option[]>();

  const toggleDropdown = () => {
    setIsOpen((prev) => !prev);
  };

  const handleOptionClick = (value: string) => {
    setIsOpen(false);
    onSelect(value);
    setFilteredOptions(options);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  const handleFilter = (value: string) => {
    const filtered = options.filter((single: Option) =>
      single.label.toLowerCase().includes(value.toLowerCase()),
    );
    setFilteredOptions(filtered);
  };

  return (
    <div className="relative w-full" ref={dropdownRef}>
      <button
        className="flex w-full items-center justify-between rounded-lg border border-gray-300 bg-gray-50 px-4 py-2 placeholder:text-gray-600 focus:border-[#092143] focus:outline-none focus:ring"
        onClick={toggleDropdown}
      >
        <span className="truncate text-sm font-normal">
          {selectedValue || placeholder}
        </span>
        {isOpen ? (
          <IoChevronUp
            className="h-4 w-4 text-gray-500"
            onClick={toggleDropdown}
          />
        ) : (
          <IoChevronDown
            className="h-4 w-4 text-gray-500"
            onClick={toggleDropdown}
          />
        )}
      </button>
      {isOpen && (
        <div
          className="absolute z-10 mt-2 max-h-[400px] w-[200px] overflow-y-auto rounded-lg border-2 border-gray-300 bg-white shadow-lg"
          style={{ width }}
        >
          <input
            type="text"
            onChange={(e) => handleFilter(e.target.value)}
            className="w-full rounded border border-black p-2 text-xs font-normal"
            placeholder="Search"
          />
          {filteredOptions?.map((option) => (
            <button
              key={option.value}
              className="block w-full px-4 py-2 text-left text-sm font-normal hover:bg-[#0C1E32] hover:text-white"
              onClick={() => handleOptionClick(option.value)}
              style={{
                background:
                  selectedValue.length && selectedValue === option.value
                    ? "#092143"
                    : "",
                color:
                  selectedValue.length && selectedValue === option.value
                    ? "white"
                    : "",
              }}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomDropdown;
