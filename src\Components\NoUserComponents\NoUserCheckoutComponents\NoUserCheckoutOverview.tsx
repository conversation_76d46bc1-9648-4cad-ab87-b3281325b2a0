"use client";
import CheckoutOrOrderedProducts from "@/Components/ReusableComponents/CheckoutOrOrderedProducts/CheckoutOrOrderedProducts";
import CartSummery from "@/Components/ReusableComponents/CheckoutPageComponents/CartSummery";
import DeliveryDetailsForm from "@/Components/ReusableComponents/CheckoutPageComponents/DeliveryDetailsForm";
import { divisionsWithDistrict } from "@/Components/utils/divisionData";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleProductOfOrderOrCheckout } from "@/Types/public-types";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { createNewOrderWithoutUserApi } from "@/services/orderServices";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import * as Yup from "yup";

const NoUserCheckoutOverview = () => {
  const router = useRouter();
  const {
    localCheckoutItems,
    handleClearLocalCart,
    handleClearLocalCheckout,
  }: ContextDataType = useContext(LocalDataContext);
  const [districts, setDistricts] = useState<string[]>([]);
  const [deliveryCharge, setDeliveryCharge] = useState<number>(150);
  const [totalQuantity, setTotalQuantity] = useState<number>(0);
  const [totalProductPrice, setTotalProductPrice] = useState<number>(0);
  const [totalDiscount, setTotalDiscount] = useState<number>(0);
  const [delivery, setDelivery] = useState<string>("inside dhaka");
  const [paymentMethod, setPaymentMethod] = useState<string>("COD");
  const [isCreatingOrder, setIsCreatingOrder] = useState<boolean>(false);

  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    email: Yup.string().email("Invalid email"),
    phone: Yup.string().required("Phone number is required").min(11).max(11),
    zipCode: Yup.string(),
    deliveryAddress: Yup.string().required("Address is required"),
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      email: "",
      phone: "",
      division: "",
      district: "",
      zipCode: "",
      deliveryAddress: "",
      note: "",
      bkashAccount: "",
      paidAmount: 0,
      transactionId: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      setIsCreatingOrder(true);
      const userDetails = {
        name: values?.name,
        phone: values?.phone,
        deliveryAddress: values?.deliveryAddress,
        note: values?.note,
        email: values?.email,
        district: values.district,
        zipCode: values.zipCode,
      };
      localStorage.setItem("userDetails", JSON.stringify(userDetails));

      const orderData = {
        name: values.name,
        phoneNumber: values.phone,
        email: values.email,
        district: values.district,
        zipCode: values.zipCode,
        deliveryAddress: values.deliveryAddress,
        delivery: delivery,
        paymentMethod: paymentMethod,
        bkashNumber: values.bkashAccount,
        transactionId: values.transactionId,
        orderStatus: "Pending",
        orderType: "auto",
        createdBy: "user",
        createdFrom: "website",
        userIpAddress: localStorage.getItem("ip_address") ?? "not-found",
        customerSource: localStorage.getItem("utm_source") ?? "not-found",
        signUpMethod: "none",
        signUpBy: "from order",
        trackingHistory: [
          { time: new Date(), message: "Order Created Successfully" },
        ],
        notes: [
          {
            name: values.name,
            image: "",
            time: new Date(),
            message: values.note,
          },
        ],
        products: localCheckoutItems.map((product: any) => {
          return {
            productId: product?.id,
            quantity: product.quantity,
          };
        }),
        userSubmittedAmount: values.paidAmount ?? 0,
      };

      toast.promise(
        createNewOrderMutedAsync(orderData),
        {
          success: "Order Created Successful",
          error: "Error Placing order",
          loading: "Creating New order",
        },
        {
          id: "add-new-order",
        },
      );
    },
  });

  const { api: CreateOrderWithoutUserApi } = createNewOrderWithoutUserApi();

  const { mutateAsync: createNewOrderMutedAsync } = useMutation(
    CreateOrderWithoutUserApi,
    {
      onSuccess: (data) => {
        setIsCreatingOrder(false);
        handleClearLocalCart();
        handleClearLocalCheckout();
        router.push(ROUTES.SUCCESS(data?.result?.orderId));
      },
      onError: () => {
        setIsCreatingOrder(false);
      },
    },
  );

  useEffect(() => {
    if (localCheckoutItems) {
      let quantity = 0;
      let price = 0;
      let discount = 0;
      localCheckoutItems?.map((singleItem: SingleProductOfOrderOrCheckout) => {
        (quantity += singleItem.quantity),
          (price += singleItem.price * singleItem.quantity);
        discount +=
          (singleItem.price - singleItem.discountPrice) * singleItem.quantity;
      });
      setTotalQuantity(quantity);
      setTotalProductPrice(price);
      setTotalDiscount(discount);
      if (localCheckoutItems?.length) {
        const fakeData = {
          event: "initiate_checkout",
          url: window.location.href,
          value: price - discount,
          user_id: "",
          user_name: "",
          user_phone_number: "",
          user_email: "",
          user_zip: "",
          user_district: "",
          user_log_in_status: "not logged in",
          items: localCheckoutItems
            ? localCheckoutItems?.map((single: any) => {
                return {
                  item_id: single?.id,
                  item_name: single?.name,
                  price: single?.discountPrice,
                  item_brand: single?.brand,
                  item_category: single?.productCategory,
                  item_quantity: single?.quantity,
                  // slug: single?.slug,
                  sku: single?.id?.replace("GMK-", ""),
                };
              })
            : null,
        };
        gtmDataLayerDataPass(fakeData);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [localCheckoutItems]);

  useEffect(() => {
    const val = divisionsWithDistrict.find(
      (si: any) => si.division === formik.values.division,
    )?.districts;

    setDistricts(val || []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik?.values.division]);

  useEffect(() => {
    if (delivery === "inside dhaka") {
      setDeliveryCharge(80);
    } else {
      setDeliveryCharge(150);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [delivery]);

  useEffect(() => {
    const loc = localStorage.getItem("userDetails");
    const userDetails = loc && JSON.parse(loc);
    if (userDetails) {
      formik.setFieldValue("name", userDetails?.name);
      formik.setFieldValue("phone", userDetails?.phone);
      formik.setFieldValue("deliveryAddress", userDetails?.deliveryAddress);
      formik.setFieldValue("note", userDetails?.note);
      formik.setFieldValue("district", userDetails?.district);
      formik.setFieldValue("zipCode", userDetails?.zipCode);
      formik.setFieldValue("email", userDetails?.email);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [localCheckoutItems]);

  return (
    <div className="mt-2">
      <div className="flex flex-col gap-4 md:flex-row">
        {/* Left Part: User Details */}
        <DeliveryDetailsForm
          delivery={delivery}
          setDelivery={setDelivery}
          paymentMethod={paymentMethod}
          setPaymentMethod={setPaymentMethod}
          deliveryCharge={deliveryCharge}
          formik={formik}
          isOrderCreating={isCreatingOrder}
          totalProductPrice={totalProductPrice}
          totalDiscount={totalDiscount}
        />

        {/* Right Part: Order Details and Payment */}
        {/*  */}
        <CartSummery
          totalQuantity={totalQuantity}
          totalProductPrice={totalProductPrice}
          totalDiscount={totalDiscount}
          deliveryCharge={deliveryCharge}
          formik={formik}
          isOrderCreating={isCreatingOrder}
        />
      </div>
      <div>
        <CheckoutOrOrderedProducts products={localCheckoutItems} />
      </div>
    </div>
  );
};

export default NoUserCheckoutOverview;
