import ProductsPage from "@/Components/ProductsPage/ProductsPage";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import ShopLayout from "@/Layouts/ShopLayout";
import { BASE_URL } from "@/environment/environment";
import { GetSingleCategoryDetailsResponse } from "@/services/bannerBrandCategoryServices";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";

interface Props {
  params: Promise<{ slug: string }>;
  searchParams: Promise<SearchParams>;
}

interface SearchParams {
  page: string;
  category: string;
}

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug } = await params;
  const data: GetSingleCategoryDetailsResponse = await getCategoryDetails(slug);
  return {
    title: data?.result?.metaTitle ?? data?.result?.metaTitle,
    description: data?.result?.metaDescription,
    openGraph: {
      title: data?.result?.metaTitle ?? data?.result?.metaTitle,
      description: data?.result?.metaDescription,
      images: [generateProductImage(data?.result?.imageUrl)],
    },
  };
};

const CategoryPage = async ({ params, searchParams }: Props) => {
  const accessToken = (await cookies()).get("GMK")?.value;
  const { page } = await searchParams;
  const { slug } = await params;
  const data = await getProductList({ page: page, category: slug });
  const categoryData: GetSingleCategoryDetailsResponse =
    await getCategoryDetails(slug);

  return (
    <ShopLayout>
      <ProductsPage
        accessToken={accessToken ? accessToken : ""}
        data={data}
        pageType="category"
        categoryName={slug}
        description={categoryData?.result?.description}
      />
    </ShopLayout>
  );
};

export default CategoryPage;

async function getProductList({ page, category }: SearchParams) {
  const queryParams = new URLSearchParams();

  if (page) queryParams.append("page", page.toString());
  if (category) queryParams.append("productCategory", category);

  const filter = queryParams.toString();
  const res = await fetch(`${BASE_URL}/products/get-all?${filter}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}

async function getCategoryDetails(slug: string) {
  const res = await fetch(`${BASE_URL}/categories/single/${slug}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}