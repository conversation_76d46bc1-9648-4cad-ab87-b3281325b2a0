"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import {
    LandingPageProductDetails,
    WhyBest,
} from "@/Types/landingPageProductType";
import { convertToBanglaNumber } from "@/utils/EnglishToBanglaConvert";
import Image from "next/image";
import { GiMoebiusStar, GiStarShuriken } from "react-icons/gi";
import logo from "../../../../src/dummyImages/logo.png";
import Accordion from "../Accordion";
import DirectOrderForm from "../DirectOrderForm";
import { OrderButtonVersionFour, OrderButtonVersionOne, OrderButtonVersionTwo } from "../LangingPageDesignThree/CommonOrderNowButton";


interface Props {
    productDetails: LandingPageProductDetails;
}

const LandingPageFourOverview = ({ productDetails }: Props) => {
    const handleScrollToOrderForm = () => {
        const orderFormDiv = document?.getElementById("order-form");
        if (orderFormDiv) {
            orderFormDiv?.scrollIntoView({ behavior: "smooth" });
        }
    };
    return (
        <div className="landing-page-two overflow-hidden">
            <section className="hero-section bg-no-repeat bg-cover bg-center" style={{ backgroundImage: "url('https://i.ibb.co/KpVQKBG8/Hero-area-bg.png')" }}>
                <div className="flex justify-center">
                    <div className="bg-white px-10 pt-10 rounded-[100px] mt-[-50px]">
                        <div className="flex justify-center pb-2 pt-4">
                            <Image src={logo} alt="logo" width={200} height={100} />
                        </div>
                    </div>
                </div>
                <div className="container mx-auto px-4">
                    <div className="grid gap-8 grid-cols-1 md:grid-cols-2 items-center pt-8 pb-32">
                        <div className="left">
                            <h1 className="text-3xl sm:text-4xl font-extrabold leading-[1.4em] lg:text-5xl sm:leading-[1.5em] text-white text-center md:text-left">
                                {productDetails?.title}
                            </h1>
                            <div className="mt-3 text-xl sm:text-2xl text-center md:text-left">
                                <div
                                    className="dangerouslyHtml"
                                    dangerouslySetInnerHTML={{
                                        __html: productDetails?.punchLine
                                            ? productDetails?.punchLine
                                            : "<p>No answer</p>",
                                    }}
                                />{" "}
                                মাএ{" "}
                                <span className="text-gray-600 line-through">
                                    {convertToBanglaNumber(productDetails?.productDetails?.price)}
                                </span>{" "}
                                <span className="text-3xl font-semibold text-white">
                                    {convertToBanglaNumber(
                                        productDetails?.productDetails?.discountPrice,
                                    )}
                                </span>{" "}
                                টাকায়
                            </div>
                            <div className="mt-8 flex justify-center md:justify-start">
                                <OrderButtonVersionFour handleClick={handleScrollToOrderForm} />
                            </div>
                        </div>
                        <div className="right">
                            <Image
                                src='https://i.ibb.co/PZ8hTw8K/pp.jpg'
                                alt="logo"
                                width={570}
                                height={570}
                                className="w-full h-auto rounded-tl-[400px] rounded-tr-[400px] rounded-bl-md rounded-br-md"
                            />
                        </div>
                    </div>
                </div>
            </section>
            <section className="video">
                <div className="container mx-auto px-4">
                    <div className="max-w-full sm:max-w-6xl mx-auto bg-white p-5 rounded-2xl mt-[-100px]">
                        {productDetails?.youtubeUrl ? (
                            <iframe
                                className="md:min-h-96 aspect-video w-full self-stretch rounded-lg"
                                src={productDetails?.youtubeUrl}
                                frameBorder="0"
                                title="Product Overview Video"
                                aria-hidden="true"
                            />
                        ) : (
                            <div>
                                <div className="h-[450px] max-w-6xl rounded-xl border-2 border-white bg-white p-4 shadow-2xl md:h-[550px]">
                                    <video
                                        src={
                                            productDetails?.video
                                                ? generateProductImage(productDetails?.video)
                                                : "https://beautysiaa.com/wp-content/uploads/2023/11/bb-cream-jahsbf.mp4"
                                        }
                                        className="h-full w-full rounded-lg"
                                        controls
                                        autoPlay
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </section>

            {productDetails?.sections?.map((section: WhyBest, index: number) => {
                return index % 2 === 0 ? (
                    <section className="why-buy mt-14 sm:mt-16 md:mt-20 xl:mt-24">
                        <div className="container mx-auto px-4">
                            <div className="rounded-lg bg-[#FA0566] py-4">
                                <h1 className="text-center text-3xl font-bold text-white sm:text-4xl">
                                    {section?.title}
                                </h1>
                            </div>
                            <div className="mt-10 grid grid-cols-1 items-center gap-8 sm:grid-cols-2 xl:grid-cols-3">
                                <div className="col-1 space-y-4 sm:space-y-7">
                                    {section?.options
                                        ?.slice(0, Math.ceil(section?.options?.length / 2))
                                        .map((single: string) => (
                                            <div
                                                className="usefullnessCard flex items-center"
                                                key={single}
                                            >
                                                <div className="icon rounded-full bg-[#FA0566] p-2">
                                                    <GiStarShuriken className="text-3xl text-white" />
                                                </div>
                                                <div className="card-text ml-3">
                                                    <h3>{single}</h3>
                                                </div>
                                            </div>
                                        ))}
                                </div>
                                <div className="col-2 hidden xl:block">
                                    <div className="w-full relative pb-[100%] overflow-hidden border-2 border-[#FA0566] rounded-md">
                                        <Image
                                            src={generateProductImage(section?.image)}
                                            // src={'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQwN8U653OvsAZLlAPY_kXfCufC4kZTJvkv6Q&s'}
                                            alt="product"
                                            width={570}
                                            height={570}
                                            className="max-w-full absolute top-0 bottom-0 left-0 right-0 m-auto rounded-md object-cover"
                                        />
                                    </div>
                                </div>
                                <div className="col-3 rtl-md space-y-4 sm:space-y-7">
                                    {section?.options
                                        ?.slice(
                                            Math.ceil(section?.options?.length / 2),
                                            Number(section?.options?.length),
                                        )
                                        .map((single: string) => (
                                            <div
                                                className="usefullnessCard flex items-center"
                                                key={single}
                                            >
                                                <div className="icon rounded-full bg-[#FA0566] p-2">
                                                    <GiStarShuriken className="text-3xl text-white" />
                                                </div>
                                                <div className="card-text ml-3">
                                                    <h3>{single}</h3>
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </div>
                            <div className="mt-8 flex justify-center">
                                <OrderButtonVersionFour handleClick={handleScrollToOrderForm} />
                            </div>
                        </div>
                    </section>
                ) : (
                    <section className="how-to-use mt-14 sm:mt-16 md:mt-20 xl:mt-24">
                        <div className="container mx-auto px-4">
                            <div className="grid grid-cols-1 lg:grid-cols-2">
                                <div className="img order-2 lg:order-1">
                                    <Image
                                        src={generateProductImage(section?.image)}
                                        alt="logo"
                                        width={100}
                                        height={100}
                                        className="h-auto max-h-[600px] w-full rounded-tr-[40px] rounded-br-[40px] rounded-bl-[40px] lg:rounded-bl-md rounded-tl-[40px] lg:rounded-tl-md border-2 border-[#FA0566]"
                                    />
                                </div>
                                <div className="text bg-gradient-to-b from-[#FA0566] to-[#E8035F] my-0 lg:my-[60px] rounded-bl-[40px] lg:rounded-bl-none rounded-tl-[40px] lg:rounded-tl-none rounded-tr-[40px] rounded-br-[40px] order-1 lg:order-2">
                                    <h1 className="text-center text-2xl font-bold text-white sm:text-3xl mt-3">{section?.title}</h1>
                                    <div className="space-y-3 pt-5 xl:space-y-5 xl:pt-10 pl-5">
                                        {section?.options?.map(
                                            (single: string, index: number) => (
                                                <div
                                                    className="step-card flex items-center"
                                                    key={single}
                                                >
                                                    <div className="point ml-3">
                                                        <p className="text-white">{single}</p>
                                                    </div>
                                                </div>
                                            ),
                                        )}
                                    </div>
                                    <div className="ml-8 mt-3 mb-3 flex justify-center xl:mt-8">
                                        <OrderButtonVersionFour
                                            handleClick={handleScrollToOrderForm}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                );
            })}

            <section className="discount mt-14 sm:mt-16 md:mt-20 xl:mt-24">
                <div className="container mx-auto px-4">
                    <div className="rounded-xl bg-gradient-to-b from-[#FA0566] to-[#E8035F] py-10">
                        <h3 className="mb-5 text-center text-3xl font-bold text-gray-800 line-through sm:text-4xl">
                            প্রোডাক্টের রেগুলার মূল্য:{" "}
                            {convertToBanglaNumber(productDetails?.productDetails?.price)}{" "}
                            টাকা
                        </h3>
                        <h3 className="text-center text-3xl font-bold text-white sm:text-4xl">
                            ডিস্কাউন্ট অফারে বর্তমান মূল্য:{" "}
                            {convertToBanglaNumber(
                                productDetails?.productDetails?.discountPrice,
                            )}{" "}
                            টাকা
                        </h3>
                        <div className="mt-5 flex justify-center">
                            <OrderButtonVersionFour handleClick={handleScrollToOrderForm} />
                        </div>
                    </div>
                </div>
            </section>

            <div>
                <DirectOrderForm productDetails={productDetails?.productDetails} />
            </div>

            {productDetails?.generalQuestions?.length ? (
                <div className="px-2 py-6 text-start md:px-10 md:py-10 lg:px-20 xl:px-40">
                    <div className="pb-4 text-center text-2xl font-bold">
                        সাধারণ জিজ্ঞাসা
                    </div>
                    {productDetails?.generalQuestions?.map((single) => (
                        <Accordion title={single?.question} key={single?.question}>
                            <div
                                dangerouslySetInnerHTML={{
                                    __html: single?.answer ? single?.answer : "<p>No answer</p>",
                                }}
                            />
                        </Accordion>
                    ))}
                </div>
            ) : (
                ""
            )}
        </div>
    );
};

export default LandingPageFourOverview;
