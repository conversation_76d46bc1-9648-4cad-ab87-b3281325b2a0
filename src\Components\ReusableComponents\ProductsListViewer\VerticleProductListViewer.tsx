"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { notLoggedInModal } from "@/Components/utils/commonModal";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleProductOfList } from "@/Types/productTypes";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { addToCartApi } from "@/services/cartServices";
import { addToWishListApi } from "@/services/wishlistServices";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useContext, useState } from "react";
import toast from "react-hot-toast";
import { AiFillHeart, AiOutlineHeart } from "react-icons/ai";
import { BsCartCheck } from "react-icons/bs";
import AddToCartModal from "../AddToCartModal/AddToCartModal";
import Modal from "../Modal/Modal";
import SpinnerLoader from "../SpinnerLoader/SpinnerLoader";
interface Props {
  productList?: SingleProductOfList[] | undefined;
  loading: boolean;
  accessToken: string;
}

const VerticalProductListViewer = ({
  productList,
  accessToken,
  loading,
}: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { wishlist, handleAddToLocalCart, userHeaderDetails }: ContextDataType =
    useContext(LocalDataContext);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [selectedProductInfo, setSelectedProductInfo] =
    useState<SingleProductOfList>();

  const { api: AddToWishlistApi } = addToWishListApi();

  const { mutateAsync: addToWishlistMutateAsync } = useMutation(
    AddToWishlistApi,
    {
      onSuccess: () => {
        queryClient.invalidateQueries();
      },
    },
  );

  const handleAddToWishlist = async (product: SingleProductOfList) => {
    const productInfo = {
      productId: product._id,
      ...product,
    };

    if (accessToken) {
      toast.promise(
        addToWishlistMutateAsync(productInfo),
        {
          success: "Product Added to Wishlist Successful",
          error: "Error Adding Product to Wishlist",
          loading: "Adding Product to Wishlist",
        },
        {
          id: "add-to-wishlist-product",
        },
      );
    } else {
      notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
    }
  };

  const { api: AddToCartApi } = addToCartApi();

  const { mutateAsync: addToCartMutateAsync } = useMutation(AddToCartApi, {
    onSuccess: () => {
      queryClient.invalidateQueries();
    },
  });

  const handleAddToCart = async (product: SingleProductOfList) => {
    const productInfo = {
      productId: product?._id || "",
      quantity: 1,
      price: product?.discountPrice,
    };

    const addToCartDataLayer = {
      event: "add_to_cart",
      value: product.discountPrice,
      user_log_in_status: userHeaderDetails?.userDetails?.name
        ? "logged in"
        : "not logged in",
      user_id: userHeaderDetails?.userDetails?._id ?? "",
      user_name: userHeaderDetails?.userDetails?.name ?? "",
      user_phone_number: userHeaderDetails?.userDetails?.mobileNumber ?? "",
      user_email: userHeaderDetails?.userDetails?.email ?? "",
      user_zip: userHeaderDetails?.userDetails?.zipCode ?? "",
      user_district: userHeaderDetails?.userDetails?.district ?? "",
      items: [
        {
          item_id: product.id,
          item_name: product.name,
          price: product.discountPrice,
          item_brand: product.brand,
          slug: product.slug,
          sku: product?.id?.replace("GMK-", ""),
          item_category: product.productCategory,
        },
      ],
    };
    gtmDataLayerDataPass(addToCartDataLayer);

    if (accessToken) {
      toast.promise(
        addToCartMutateAsync(productInfo),
        {
          success: "Product Added to cart Successful",
          error: "Error Adding Product to cart",
          loading: "Adding Product to cart",
        },
        {
          id: "add-to-cart-product",
        },
      );
    } else {
      handleAddToLocalCart({ ...product, quantity: 1 });
      toast.success("Product Added to cart Successful");
    }
  };
  return (
    <div className="flex max-h-[450px] w-full flex-col gap-2 overflow-y-auto px-2 py-1 pr-1 shadow-lg 2xl:max-h-[500px]">
      {!loading ? (
        <>
          {productList?.length ? (
            <>
              {productList?.map((product: SingleProductOfList) => {
                const isProductInWishlist = wishlist?.some(
                  (wishlistProduct: any) =>
                    wishlistProduct.productId === product.id,
                );
                return (
                  <Link
                    key={product.id}
                    href={ROUTES.PRODUCTS.VIEW(product?.slug)}
                  >
                    <div className="grid w-full grid-cols-12 gap-2 rounded border bg-[#F4F4F4] shadow p-1">
                      <div className="col-span-4 flex items-center rounded bg-white border border-gray-200">
                        <Image
                          src={generateProductImage(product?.mainImageUrl)}
                          alt="pr"
                          height={100}
                          width={100}
                          className="w-[95%] scale-110"
                        />
                      </div>
                      <div className="col-span-8 flex flex-col gap-1">
                        <span className="text-xs font-semibold 2xl:text-sm">
                          {product.name}
                        </span>
                        <div className="mt-4 flex items-center justify-between md:mt-0">
                          <h2 className="font-semibold md:text-sm">
                            ৳ {product.discountPrice}{" "}
                            {product.price !== product.discountPrice ? (
                              <span className="text-xs text-slate-500 line-through md:text-xs">
                                ৳{product.price}
                              </span>
                            ) : (
                              ""
                            )}
                          </h2>
                          <button
                            className="rounded-lg bg-white p-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              handleAddToWishlist(product);
                            }}
                            disabled={!product.isAvailable}
                          >
                            {isProductInWishlist ? (
                              <AiFillHeart
                                className={`text-xl ${isProductInWishlist
                                  ? "text-red-500"
                                  : "text-black"
                                  }`}
                              />
                            ) : (
                              <AiOutlineHeart
                                className={`text-xl ${isProductInWishlist
                                  ? "text-red-500"
                                  : "text-black"
                                  }`}
                              />
                            )}
                          </button>
                        </div>
                        <div className="flex gap-1">
                          <button
                            className="rounded bg-primary px-2 py-1 text-xs font-normal text-white hover:bg-secondary disabled:bg-gray-300 disabled:text-gray-500  md:font-semibold 2xl:py-2"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              handleAddToCart(product);
                            }}
                            disabled={!product.isAvailable}
                          >
                            <BsCartCheck />
                          </button>
                          <button
                            className="w-full rounded bg-primary py-1 text-xs font-normal text-white hover:bg-secondary disabled:bg-gray-300 disabled:text-gray-500 md:font-semibold 2xl:py-2"
                            data-testid="buy-now-button"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              setSelectedProductInfo(product);
                              setShowModal(true);
                            }}
                            disabled={!product.isAvailable}
                          >
                            Buy Now
                          </button>
                        </div>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </>
          ) : (
            <p>No Related Product</p>
          )}
        </>
      ) : (
        <SpinnerLoader />
      )}
      <Modal showModal={showModal} setShowModal={setShowModal}>
        <div className="flex w-full items-center justify-center">
          <AddToCartModal
            product={selectedProductInfo!}
            handleClose={() => setShowModal(false)}
            accessToken={accessToken}
          />
        </div>
      </Modal>
    </div>
  );
};

export default VerticalProductListViewer;
