import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

const SingleProductDetailsSkeleton = () => {
  return (
    <div className="mt-4 pb-4 pt-0">
      {/* Top part */}
      <div className="rounded-lg border-2 px-3 py-4 shadow-lg">
        <div className="flex flex-wrap gap-4">
          {/* Left Part - Image Skeleton */}
          <div className="flex items-center justify-center rounded-xl bg-slate-200 p-4 md:w-[450px] lg:w-[500px]">
            <Skeleton height={300} width={300} />
          </div>

          {/* Right Part - Details Skeleton */}
          <div className="flex-1 px-2 text-black md:px-4">
            <Skeleton height={40} width={"100%"} style={{ marginBottom: 10 }} />
            <Skeleton count={3} style={{ marginBottom: 10 }} />
            <div className="flex w-1/2 items-center justify-between ">
              <Skeleton height={20} width={100} style={{ marginBottom: 10 }} />
              <Skeleton height={20} width={20} style={{ marginBottom: 10 }} />
              <Skeleton height={20} width={100} style={{ marginBottom: 10 }} />
            </div>
            <div className="flex w-1/2 items-center justify-between ">
              <Skeleton height={20} width={100} style={{ marginBottom: 10 }} />
              <Skeleton height={20} width={20} style={{ marginBottom: 10 }} />
              <Skeleton height={20} width={100} style={{ marginBottom: 10 }} />
            </div>
            <div className="flex w-1/2 items-center justify-between ">
              <Skeleton height={20} width={100} style={{ marginBottom: 10 }} />
              <Skeleton height={20} width={20} style={{ marginBottom: 10 }} />
              <Skeleton height={20} width={100} style={{ marginBottom: 10 }} />
            </div>
            <div className="flex w-1/2 items-center justify-between ">
              <Skeleton height={20} width={100} style={{ marginBottom: 10 }} />
              <Skeleton height={20} width={20} style={{ marginBottom: 10 }} />
              <Skeleton height={20} width={100} style={{ marginBottom: 10 }} />
            </div>
            <div className="flex gap-4">
              <Skeleton height={40} width={120} />
              <Skeleton height={40} width={120} />
            </div>
            <div className="mb-4 mt-2 flex w-[150px] items-center justify-between border-2 border-none">
              <Skeleton height={40} width={40} />
              <Skeleton height={40} width={40} />
              <Skeleton height={40} width={40} />
            </div>
            <div className="flex items-center gap-5 pt-2">
              <Skeleton height={40} width={100} />
              <Skeleton height={40} width={80} />
              <Skeleton height={40} width={80} />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Part - Tabs */}
      <div className="mt-8 rounded-lg border-2 p-4 text-black shadow-lg">
        <div className="mb-4 flex items-center justify-center">
          <div className="flex flex-wrap items-center gap-2 border-none">
            {["DESCRIPTION", "REVIEWS", "DELIVERY"].map((singleTab, index) => (
              <Skeleton key={index} height={30} width={100} />
            ))}
          </div>
        </div>
        <div>
          <Skeleton count={4} height={50} style={{ marginBottom: 10 }} />
        </div>
      </div>
    </div>
  );
};

export default SingleProductDetailsSkeleton;
