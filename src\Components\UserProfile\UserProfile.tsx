"use client";
import SpinnerLoader from "@/Components/ReusableComponents/SpinnerLoader/SpinnerLoader";
import {
  divisions,
  divisionsWithDistrict,
} from "@/Components/utils/divisionData";
import {
  getUserDetailsApi,
  updateUserDetailsApi,
} from "@/services/userServices";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import * as Yup from "yup";

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  email: Yup.string().email("Invalid email").required("email is required"),
  phone: Yup.string().required("Phone number is required").min(11).max(11),
  division: Yup.string().required("Division is required"),
  district: Yup.string().required("District is required"),
  zipCode: Yup.string(),
  deliveryAddress: Yup.string().required("Address is required"),
});

interface Props {
  accessToken: string;
}

const UserProfile = ({ accessToken }: Props) => {
  const [districts, setDistricts] = useState<string[]>([]);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);

  const { api, getKey } = getUserDetailsApi();
  const {
    data: userDetails,
    isLoading,
    refetch,
  } = useQuery(getKey(), api, {
    refetchOnWindowFocus: false,
    enabled: accessToken ? true : false,
    onSuccess(data) {
      formik.values.name = data.result.name;
      formik.values.phone = data.result.mobileNumber;
      formik.values.email = data.result.email;
      formik.values.division = data.result.division;
      formik.values.district = data.result.district;
      formik.values.zipCode = data.result.zipCode;
      formik.values.deliveryAddress = data.result.deliveryAddress;
      setIsLoaded(true);
    },
  });

  const { api: updateDetailsApi } = updateUserDetailsApi();

  const { mutateAsync: updateDetailsMutateAsync } = useMutation(
    updateDetailsApi,
    {
      onSuccess: () => {
        refetch();
      },
    },
  );

  const formik = useFormik({
    initialValues: {
      name: "",
      email: "",
      phone: "",
      division: "",
      district: "",
      zipCode: "",
      deliveryAddress: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      const userData = {
        name: values.name,
        mobileNumber: values.phone,
        division: values.division,
        district: values.district,
        zipCode: values.zipCode,
        deliveryAddress: values.deliveryAddress,
      };

      if (accessToken) {
        toast.promise(
          updateDetailsMutateAsync(userData),
          {
            success: "User details updated Successful",
            error: "Error updating details",
            loading: "Updating user details",
          },
          {
            id: "update-user-details",
          },
        );
      }
    },
  });

  useEffect(() => {
    const val = divisionsWithDistrict.find(
      (si) => si.division === formik.values.division,
    )?.districts;

    setDistricts(val || []);
  }, [formik?.values.division]);

  return (
    <div>
      {!isLoading && isLoaded ? (
        <div className="w-full rounded-lg  border-2 p-4 shadow-lg md:border-r">
          <h2 className="mb-4 text-2xl font-semibold">User Details</h2>
          <form onSubmit={formik.handleSubmit}>
            <div className="flex flex-wrap gap-4 md:flex-nowrap">
              <div className="mb-4 w-full">
                <label htmlFor="name" className="block text-sm font-normal">
                  Name:
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  className="w-full rounded border p-2 text-sm font-normal"
                />
                {formik.errors.name && formik.touched.name && (
                  <p className="text-xs text-red-600">{formik.errors.name}</p>
                )}
              </div>
              <div className="mb-4 w-full">
                <label htmlFor="name" className="block text-sm font-normal">
                  Email (optional):
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  className="w-full rounded border p-2 text-sm font-normal"
                />
              </div>
            </div>
            <div className="flex w-full gap-4 ">
              <div className="mb-4 w-full">
                <label htmlFor="name" className="block text-sm font-normal">
                  Phone Number :
                </label>
                <input
                  type="text"
                  id="phone"
                  name="phone"
                  onKeyDown={(e) => {
                    // Allow only digits (0-9), backspace, and arrow keys
                    if (
                      !/[\d\b]/.test(e.key) &&
                      !["ArrowLeft", "ArrowRight", "Backspace"].includes(e.key)
                    ) {
                      e.preventDefault();
                    }
                  }}
                  value={formik.values.phone}
                  onChange={formik.handleChange}
                  className="w-full rounded border p-2 text-sm  font-normal"
                />
                {formik.errors.phone && formik.touched.phone && (
                  <p className="text-xs text-red-600">{formik.errors.phone}</p>
                )}
              </div>
              <div className="mb-4 w-full">
                <label htmlFor="name" className="block text-sm font-normal">
                  Emergency Number :
                </label>
                <input
                  type="text"
                  id="phone"
                  name="phone"
                  value={formik.values.phone}
                  onChange={formik.handleChange}
                  className="w-full rounded border p-2 text-sm  font-normal"
                />
                {formik.errors.phone && formik.touched.phone && (
                  <p className="text-xs text-red-600">{formik.errors.phone}</p>
                )}
              </div>
            </div>
            <div className="flex w-full gap-4">
              <div className="mb-4 w-full">
                <label htmlFor="name" className="block text-sm font-normal">
                  Division :
                </label>
                <select
                  className="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 text-xs font-normal leading-tight focus:outline-none"
                  // style={{ minWidth: "200px" }}
                  name="division"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.division}
                >
                  <option value="" label="Select Division" />

                  {divisions.map((division) => (
                    <option value={division} label={division} key={division} />
                  ))}
                </select>
                {formik.errors.division && formik.touched.division && (
                  <p className="text-xs text-red-600">
                    {formik.errors.division}
                  </p>
                )}
              </div>
              <div className="mb-4 w-full">
                <label htmlFor="name" className="block text-sm font-normal">
                  District:
                </label>
                <select
                  className="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 text-xs font-normal leading-tight focus:outline-none"
                  // style={{ minWidth: "200px" }}
                  name="district"
                  onChange={formik.handleChange}
                  value={formik.values.district}
                  onBlur={formik.handleBlur}
                >
                  <option value="" label="Select District" />
                  {formik.values.division === "" && (
                    <option value="" label="Select a division first" />
                  )}

                  {districts?.map((division) => (
                    <option value={division} label={division} key={division} />
                  ))}
                </select>
                {formik.errors.district && formik.touched.district && (
                  <p className="text-xs text-red-600">
                    {formik.errors.district}
                  </p>
                )}
              </div>
            </div>
            <div className="flex w-full gap-4">
              <div className="mb-4 w-full">
                <label htmlFor="name" className="block text-sm font-normal">
                  Post/ZIP code :
                </label>
                <input
                  type="text"
                  id="zipCode"
                  name="zipCode"
                  value={formik.values.zipCode}
                  onChange={formik.handleChange}
                  className="w-full rounded border p-2 text-sm  font-normal"
                />
              </div>
              <div className="mb-4 w-full">
                <label htmlFor="name" className="block text-sm font-normal">
                  Delivery Address :
                </label>
                <input
                  type="text"
                  id="deliveryAddress"
                  name="deliveryAddress"
                  value={formik.values.deliveryAddress}
                  onChange={formik.handleChange}
                  className="w-full rounded border p-2 text-sm font-normal"
                />
                {formik.errors.deliveryAddress &&
                  formik.touched.deliveryAddress && (
                    <p className="text-xs text-red-600">
                      {formik.errors.deliveryAddress}
                    </p>
                  )}
              </div>
            </div>
            <div className="flex justify-center">
              <button
                type="submit"
                className="rounded-lg bg-blue-500 px-4 py-2 text-sm font-normal text-white disabled:bg-gray-400"
                disabled={!formik.dirty}
              >
                Update
              </button>
            </div>
          </form>
        </div>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
};

export default UserProfile;
