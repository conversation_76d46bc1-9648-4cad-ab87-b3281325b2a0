import LandingPageFiveOverview from "@/Components/DirectBuyLandingPage/LandingPageDesignFive/LandingPageFiveOverview";
import LandingPageFourOverview from "@/Components/DirectBuyLandingPage/LandingPageDesignFour/LandingPageFourOverview";
import LandingPageFixOverview from "@/Components/DirectBuyLandingPage/LandingPageDesignSix/LandingPageFixOverview";
import LandingPageThreeOverview from "@/Components/DirectBuyLandingPage/LangingPageDesignThree/LandingPageThreeOverview";
import LandingPageTwoOverview from "@/Components/DirectBuyLandingPage/LangingPageDesignTwo/LandingPageTwoOverview";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ROUTES } from "@/Routes";
import { LandingPageProductDetailsResponse } from "@/Types/landingPageProductType";
import { BASE_URL } from "@/environment/environment";
import { Metadata } from "next";
import { redirect } from "next/navigation";

interface Props {
  params: Promise<{ slug: string }>;
}

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug } = await params;
  const data: LandingPageProductDetailsResponse = await getData(slug);
  return {
    title: data?.result?.productDetails?.name,
    description: data?.result?.productDetails?.details,
    openGraph: {
      title: data?.result?.productDetails?.name,
      description: data?.result?.productDetails?.details,
      images: [
        generateProductImage(data?.result?.productDetails?.mainImageUrl),
      ],
    },
  };
};

const DirectBuyPage = async ({ params }: Props) => {
  const { slug } = await params;
  const data: LandingPageProductDetailsResponse = await getData(slug);
  return (
    <div>
      {/* <DirectBuyLandingPageOverview productDetails={data.result} /> */}
      <LandingPageThreeOverview productDetails={data?.result} />
      {/* <LandingPageFiveOverview productDetails={data?.result} /> */}
      {/* <LandingPageFixOverview productDetails={data?.result} /> */}
      {/* <LandingPageFourOverview productDetails={data?.result} /> */}
      {/* <LandingPageTwoOverview productDetails={data?.result} /> */}
    </div>
  );
};

export default DirectBuyPage;

async function getData(productId: string) {
  const res = await fetch(
    `${BASE_URL}/landing-page-product/single/${productId}`,
    {
      // cache: "no-cache",
      next: { revalidate: 0 },
    },
  );

  if (!res.ok) {
    // notFound();
    // throw new Error("Failed to fetch data");
    redirect(ROUTES.PRODUCTS.VIEW(productId));
  }

  return res.json();
}
