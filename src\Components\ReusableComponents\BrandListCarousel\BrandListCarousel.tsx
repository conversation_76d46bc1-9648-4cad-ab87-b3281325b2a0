import ScrollRevealWrapper from "@/Components/ReusableComponents/ScrollRevealWrapper/ScrollRevealWrapper";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleBrand } from "@/services/bannerBrandCategoryServices";
import Image from "next/image";
import Link from "next/link";
import { useContext } from "react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

const BrandListCarousel = () => {
  const { brands, isBrandsLoading }: ContextDataType =
    useContext(LocalDataContext);
  return (
    <ScrollRevealWrapper>
      <Swiper
        effect="coverflow"
        grabCursor={true}
        centeredSlides={false}
        slidesPerView={4}
        spaceBetween={6}
        coverflowEffect={{
          rotate: 0,
          stretch: 10,
          depth: 100,
          modifier: 1,
          slideShadows: true,
        }}
        pagination={{ clickable: true }}
        className="mySwiper"
        autoplay={{
          delay: 3000,
          disableOnInteraction: false,
          reverseDirection: false,
        }}
        loop={true}
        breakpoints={{
          1024: {
            slidesPerView: 7,
          },
          768: {
            slidesPerView: 3,
          },
        }}
        modules={[Autoplay]}
      >
        {!isBrandsLoading &&
          brands?.map((card: SingleBrand) => (
            <SwiperSlide
              key={card.name}
              style={{
                display: card.name.toLowerCase() === "none" ? "none" : "",
              }}
            >
              <Link href={ROUTES.BRANDS.VIEW(card.slug)}>
                <div className="overflow-hidden rounded-lg border bg-white px-1 py-2 shadow hover:shadow-lg">
                  <Image
                    height={100}
                    width={100}
                    src={
                      card?.imageUrl
                        ? generateProductImage(card?.imageUrl)
                        : "https://cdn-icons-png.flaticon.com/512/5309/5309779.png"
                    }
                    alt={card.name}
                    className="h-20 w-full rounded-lg  transition-transform hover:scale-105"
                  />
                </div>
              </Link>
            </SwiperSlide>
          ))}
      </Swiper>
    </ScrollRevealWrapper>
  );
};

export default BrandListCarousel;
