import ContactUs from "@/Components/FooterComponents/ContactUs";
import TitleViewer from "@/Components/ReusableComponents/TitleViewer/TitleViewer";
import ShopLayout from "@/Layouts/ShopLayout";
import { Metadata } from "next";
import { BiPhone } from "react-icons/bi";
import { BsClock } from "react-icons/bs";
import { IoLocation } from "react-icons/io5";

export const metadata: Metadata = {
  title: "Contact Us",
};

const ContactUsPage = () => {
  return (
    <ShopLayout>
      <div>
        <TitleViewer titleOne="Contact" titleTwo="US" seeAllButton={false} />
        <div className="flex flex-col items-center">
          <h2 className="mb-4 inline-block border-b-2 border-black px-4 text-2xl font-bold">
            Keep In Touch with Us
          </h2>
          <p className="text-center">
            {`We’re talking about clean beauty gift sets, of course – and we’ve `}{" "}
            <br />{" "}
            {`got a bouquet of beauties for yourself or someone you love.`}
          </p>
        </div>
        <div className="mt-8 flex flex-wrap items-start justify-evenly">
          <div className="flex items-start gap-2">
            <div className="rounded-full bg-[#FFE2DC] p-2">
              <IoLocation className="text-xl" />
            </div>
            <div>
              <span className="font-bold">Store Location</span>
              <p>50 Zigatola Kacha Bazar, Dhaka , Bangladesh</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <div className="rounded-full bg-[#FFE2DC] p-2">
              <BiPhone className="text-xl" />
            </div>
            <div>
              <span className="font-bold">Contact</span>
              <p>
                <span className="text-gray-400">Mobile : </span> 01303-779646
              </p>
              <p>
                <span className="text-gray-400">Hotline : </span> 01303-779646
              </p>
              <p>
                <span className="text-gray-400">Email : </span>{" "}
                koreantrendymall.com
              </p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <div className="rounded-full bg-[#FFE2DC] p-2">
              <BsClock className="text-xl" />
            </div>
            <div>
              <span className="font-bold">Open Hours</span>
              <p>
                <span className="text-gray-400">Online : </span> 24/7
              </p>
              <p>
                <span className="text-gray-400">Store : </span> 10 AM - 8 PM
              </p>
            </div>
          </div>
        </div>
        <div className="my-8 px-4 md:px-20">
          <iframe
            id="gmap_canvas"
            src="https://maps.google.com/maps?width=100%25&amp;height=600&amp;hl=en&amp;q=Shop-1036,%20Level-01,%20Shimanto%20Shomvar,+(My%20Business%20Name)&amp;t=&amp;z=14&amp;ie=UTF8&amp;iwloc=B&amp;output=embed"
            scrolling="no"
            className="h-[300px] w-full rounded-2xl"
          />
        </div>
        <ContactUs />
      </div>
    </ShopLayout>
  );
};

export default ContactUsPage;
