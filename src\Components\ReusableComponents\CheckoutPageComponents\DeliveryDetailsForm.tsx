interface Props {
  delivery: string;
  setDelivery: (id: string) => void;
  paymentMethod: string;
  setPaymentMethod: (id: string) => void;
  formik: any;
  deliveryCharge: number;
  isOrderCreating: boolean;
  totalProductPrice: number;
  totalDiscount: number;
}

const DeliveryDetailsForm = ({
  formik,
  delivery,
  setDelivery,
  paymentMethod,
  setPaymentMethod,
  deliveryCharge,
  isOrderCreating,
  totalProductPrice,
  totalDiscount,
}: Props) => {
  return (
    <div className="rounded-lg border-2  p-2 shadow-lg md:w-2/3 md:border-r md:p-4">
      <h2 className="mb-2 text-lg font-semibold md:mb-4 md:text-2xl">
        Delivery Details
      </h2>
      <form onSubmit={formik.handleSubmit}>
        <div className="flex w-full flex-wrap lg:flex-nowrap lg:gap-4">
          <div className="mb-2 w-full md:mb-4">
            <label htmlFor="name" className="block text-sm font-normal">
              Name (নাম):
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formik.values.name}
              onChange={formik.handleChange}
              className="w-full rounded border p-2 text-sm font-normal"
            />
            {formik.errors.name && formik.touched.name && (
              <p className="text-xs text-red-600">{formik.errors.name}</p>
            )}
          </div>
          <div className="mb-2 w-full md:mb-4">
            <label htmlFor="name" className="block text-sm font-normal">
              Phone Number (মোবাইল নাম্বার):
            </label>
            <input
              type="text"
              id="phone"
              name="phone"
              value={formik.values.phone.replace(" ", "")}
              onChange={formik.handleChange}
              className="w-full rounded border p-2 text-sm  font-normal"
              onKeyDown={(e) => {
                // Allow only digits (0-9), backspace, and arrow keys
                if (
                  !/[\d\b]/.test(e.key) &&
                  !["ArrowLeft", "ArrowRight", "Backspace"].includes(e.key)
                ) {
                  e.preventDefault();
                }
              }}
            />
            {formik.errors.phone && formik.touched.phone && (
              <p className="text-xs text-red-600">{formik.errors.phone}</p>
            )}
          </div>
        </div>
        <div className="flex w-full flex-wrap lg:flex-nowrap lg:gap-4">
          <div className="mb-2 w-full md:mb-4">
            <label htmlFor="name" className="block text-sm font-normal">
              Delivery Address (ঠিকানা):
            </label>
            <textarea
              id="deliveryAddress"
              name="deliveryAddress"
              value={formik.values.deliveryAddress}
              onChange={formik.handleChange}
              className="h-24 w-full rounded border p-2 text-sm font-normal"
            />
            {formik.errors.deliveryAddress &&
              formik.touched.deliveryAddress && (
                <p className="text-xs text-red-600">
                  {formik.errors.deliveryAddress}
                </p>
              )}
          </div>
          <div className="mb-2 w-full md:mb-4">
            <label htmlFor="name" className="block text-sm font-normal">
              Note (optional):
            </label>
            <textarea
              id="note"
              name="note"
              value={formik.values.note}
              onChange={formik.handleChange}
              className="h-24 w-full rounded border p-2 text-sm font-normal"
            />
          </div>
        </div>
        <div className="flex w-full flex-wrap md:flex-nowrap md:gap-4">
          <div className="mb-2 w-full md:mb-4">
            <label htmlFor="name" className="block text-sm font-normal">
              Your Shipping Location :
            </label>
            <div className="mt-2 flex flex-col gap-2">
              <label className="ml-2 inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio bg-primary text-[#092143] checked:text-[#092143]"
                  name="inside"
                  value="inside dhaka"
                  onClick={() => setDelivery("inside dhaka")}
                  checked={delivery === "inside dhaka"}
                />
                <span className="ml-2 cursor-pointer">
                  Inside Dhaka - 80 TK
                </span>
              </label>
              <label className="ml-2 inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio bg-primary text-[#092143] checked:text-[#092143]"
                  name="outside"
                  value="outside dhaka"
                  onClick={() => setDelivery("outside dhaka")}
                  checked={delivery === "outside dhaka"}
                />
                <span className="ml-2 cursor-pointer">
                  Outside Dhaka - 150 TK
                </span>
              </label>
            </div>
          </div>
          {/* <div className="mb-2 w-full md:mb-4">
            <label htmlFor="name" className="block text-sm font-normal">
              Payment Method :
            </label>
            <div className="mt-2 flex flex-col gap-2">
              <label className="ml-2 inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio bg-primary text-[#092143] checked:text-[#092143]"
                  name="cod"
                  value="COD"
                  onClick={() => setPaymentMethod("COD")}
                  checked={paymentMethod === "COD"}
                />
                <span className="ml-2 cursor-pointer">Cash On Delivery</span>
              </label>
              <label className="ml-2 inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio bg-primary text-[#092143] checked:text-[#092143]"
                  name="bkash"
                  value="bkash"
                  onClick={() => setPaymentMethod("BKASH")}
                  checked={paymentMethod === "BKASH"}
                />
                <span className="ml-2 cursor-pointer">Bkash</span>
              </label>
            </div>
          </div> */}
        </div>

        {/* {formik.values.district && formik.values.district !== "Dhaka" ? (
          <div>
            <p className="pb-2 text-sm font-normal text-red-600">
              Please pay {deliveryCharge} tk delivery charge in this ***********
              number to confirm your order***
            </p>
            <div className="flex w-full gap-4 ">
              <div className="mb-2 w-full md:mb-4">
                <label htmlFor="name" className="block text-sm font-normal">
                  Bkash Number :
                </label>
                <input
                  type="text"
                  id="bkashAccount"
                  name="bkashAccount"
                  value={formik.values.bkashAccount}
                  onChange={formik.handleChange}
                  className="w-full rounded border p-2 text-sm  font-normal"
                />
                {formik.errors.name && formik.touched.name && (
                  <p className="text-xs text-red-600">{formik.errors.name}</p>
                )}
              </div>
              <div className="mb-2 w-full md:mb-4">
                <label htmlFor="name" className="block text-sm font-normal">
                  Paid Amount :
                </label>
                <input
                  type="number"
                  id="paidAmount"
                  name="paidAmount"
                  value={formik.values.paidAmount}
                  onChange={formik.handleChange}
                  className="w-full rounded border p-2 text-sm font-normal"
                />
                {formik.errors.name && formik.touched.name && (
                  <p className="text-xs text-red-600">{formik.errors.name}</p>
                )}
              </div>
            </div>
            <div className="mb-2 w-full md:mb-4">
              <label htmlFor="name" className="block text-sm font-normal">
                Transaction Id :
              </label>
              <input
                type="text"
                id="transactionId"
                name="transactionId"
                value={formik.values.transactionId}
                onChange={formik.handleChange}
                className="w-full rounded border p-2 text-sm  font-normal"
              />
              {formik.errors.name && formik.touched.name && (
                <p className="text-xs text-red-600">{formik.errors.name}</p>
              )}
            </div>
          </div>
        ) : (
          ""
        )} */}
        <div className="flex justify-center">
          <button
            className="w-full rounded-lg bg-primary px-4 py-2 font-normal text-white disabled:bg-gray-400 md:w-[300px] "
            id="button one"
            disabled={isOrderCreating ? true : false}
            onClick={() => formik.handleSubmit()}
            type="button"
          >
            {isOrderCreating ? (
              <Spinner />
            ) : (
              <span>
                অর্ডার করুন (৳
                {totalProductPrice - totalDiscount + deliveryCharge})
              </span>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default DeliveryDetailsForm;

const Spinner = () => {
  return (
    <div
      className="flex items-center justify-center"
      style={{ height: "20px" }}
    >
      <div className="h-[20px] w-[20px] animate-spin rounded-full border-t-4 border-white"></div>
    </div>
  );
};
