import BlogDetailsOverview from "@/Components/BlogDetails/BlogDetailsOverview";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import ShopLayout from "@/Layouts/ShopLayout";
import { BASE_URL } from "@/environment/environment";
import { SingleBlogDetailsResponse } from "@/services/blogServices";
import { Metadata } from "next";
import { notFound } from "next/navigation";

interface Props {
  params: Promise<{ slug: string }>;
}

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug } = await params;
  const data: SingleBlogDetailsResponse = await getData(slug);
  return {
    title: data?.result?.title,
    description: data?.result?.brief,
    openGraph: {
      title: data?.result?.title,
      description: data?.result?.brief,
      images: [generateProductImage(data?.result?.image)],
    },
  };
};

const BlogDetailsPage = async ({ params }: Props) => {
  const { slug } = await params;
  const data: SingleBlogDetailsResponse = await getData(slug);

  return (
    <ShopLayout>
      <BlogDetailsOverview
        data={data.result}
        relatedBlogs={data.relatedBlogs}
      />
    </ShopLayout>
  );
};

export default BlogDetailsPage;

async function getData(blogId: string) {
  const res = await fetch(`${BASE_URL}/blogs/single/${blogId}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}
