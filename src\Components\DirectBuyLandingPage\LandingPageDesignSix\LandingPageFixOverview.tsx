"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { LandingPageProductDetails } from "@/Types/landingPageProductType";
import { convertToBanglaNumber } from "@/utils/EnglishToBanglaConvert";
import Image from "next/image";
import { useState } from "react";
import {
  FaCheckCircle,
  FaFacebookMessenger,
  FaPhone,
  FaStar,
} from "react-icons/fa";
import { IoLogoWhatsapp } from "react-icons/io";
import { PiSealCheckFill } from "react-icons/pi";
import { RxCross2 } from "react-icons/rx";
import { TbHandStop } from "react-icons/tb";
import Iframe from "react-iframe";
import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import { A11y, Autoplay, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import logo from "../../../../src/dummyImages/logo.png";
import Accordion from "../Accordion";
import DirectOrderForm from "../DirectOrderForm";
import { OrderButtonVersionFive } from "../LangingPageDesignThree/CommonOrderNowButton";

interface Props {
  productDetails: LandingPageProductDetails;
}

const LandingPageFixOverview = ({ productDetails }: Props) => {
  const handleScrollToOrderForm = () => {
    const orderFormDiv = document?.getElementById("order-form");
    if (orderFormDiv) {
      orderFormDiv?.scrollIntoView({ behavior: "smooth" });
    }
  };

  const [isHovered, setIsHovered] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(false);

  const handleHover = () => {
    setIsHovered(true);
    if (!isContentVisible) {
      setIsContentVisible(true);
    }
  };

  const handleLeave = () => {
    if (!isContentVisible) {
      setIsHovered(false);
    }
  };

  const handleCrossClick = () => {
    setIsContentVisible(false);
    setIsHovered(false);
  };

  const handleTriggerClick = () => {
    setIsContentVisible((prev) => !prev);
    setIsHovered((prev) => !prev);
  };

  return (
    <div
      className="landing-page-two overflow-hidden bg-repeat"
      style={{ backgroundImage: "url('https://i.ibb.co/YFPCdRjV/bg.jpg')" }}
    >
      <section className="hero-section bg-white pb-10">
        <div className="bg-gradient-to-t from-[#FFFF] to-[#7728A8] pb-8">
          <div className="flex justify-center">
            <div className="mt-[-50px] rounded-[100px] bg-white px-10 pt-10">
              <div className="flex justify-center pb-2 pt-4">
                <Image src={logo} alt="logo" width={200} height={100} />
              </div>
            </div>
          </div>
        </div>
        <div className="container mx-auto px-4">
          <div className="flex justify-center">
            <div className="max-w-full sm:max-w-5xl">
              <div className="grid grid-cols-1 items-center gap-10 lg:grid-cols-2">
                <div className="left flex justify-center lg:justify-start">
                  {productDetails?.youtubeUrl ? (
                    <Iframe
                      className="aspect-video h-96 w-96 self-stretch rounded-lg"
                      url={productDetails?.youtubeUrl}
                      // frameBorder="0"
                      title="Product Overview Video"
                      aria-hidden="true"
                    />
                  ) : (
                    <div>
                      <div className="h-[390px] max-w-6xl rounded-xl border-2 border-[#A2449A] bg-[#A2449A] p-2 shadow-2xl md:h-[500px]">
                        <video
                          src={
                            productDetails?.video
                              ? generateProductImage(productDetails?.video)
                              : "https://beautysiaa.com/wp-content/uploads/2023/11/bb-cream-jahsbf.mp4"
                          }
                          className="h-full w-full rounded-lg"
                          controls
                          autoPlay
                        />
                      </div>
                    </div>
                  )}
                </div>
                <div className="right">
                  <h1 className="text-center text-3xl font-bold leading-[1.4em] text-[#A2449A] sm:text-3xl sm:leading-[1.5em] lg:text-start">
                    {productDetails?.title}
                  </h1>
                  <div className="mt-3 text-center text-xl sm:text-2xl lg:text-start">
                    <div
                      className="dangerouslyHtml"
                      dangerouslySetInnerHTML={{
                        __html: productDetails?.punchLine
                          ? productDetails?.punchLine
                          : "<p>No answer</p>",
                      }}
                    />{" "}
                    মাএ{" "}
                    <span className="text-gray-600 line-through">
                      {convertToBanglaNumber(
                        productDetails?.productDetails?.price,
                      )}
                    </span>{" "}
                    <span className="text-3xl font-semibold text-[#A2449A]">
                      {convertToBanglaNumber(
                        productDetails?.productDetails?.discountPrice,
                      )}
                    </span>{" "}
                    টাকায়
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="my-8">
        <div className="flex justify-center">
          <OrderButtonVersionFive handleClick={handleScrollToOrderForm} />
        </div>
      </div>

      <section className="">
        <div className="container mx-auto bg-white">
          <div className="bg-black py-2">
            <h2 className="text-center text-2xl font-bold text-white sm:text-3xl">
              উপাদান
            </h2>
          </div>
          <div className="py-5">
            <ul className="ml-0 list-none space-y-3 px-3 sm:ml-10 sm:px-0">
              <li className="flex items-center gap-3 text-2xl font-bold">
                <FaCheckCircle className="flex-shrink-0 text-black" />
                Biotin, Vitamin E, and various B vitamins ,Zinc ,Amino Acids &
                Plant Extracts
              </li>
              <li className="flex items-center gap-3 text-2xl font-bold">
                <FaCheckCircle className="flex-shrink-0 text-black" />
                Biotin, Vitamin E, and various B vitamins ,Zinc ,Amino Acids &
                Plant Extracts
              </li>
            </ul>
          </div>
          <div className="bg-black py-2">
            <h2 className="text-center text-2xl font-bold text-white sm:text-3xl">
              উপকারিতা
            </h2>
          </div>
          <div className="py-5">
            <ul className="ml-0 list-none space-y-3 px-3 sm:ml-10 sm:px-0">
              <li className="flex items-center gap-3 text-2xl font-bold">
                <PiSealCheckFill className="flex-shrink-0 text-black" />
                চুলের গ্রোথ বাড়ায়
              </li>
              <li className="flex items-center gap-3 text-2xl font-bold">
                <PiSealCheckFill className="flex-shrink-0 text-black" />
                চুল পড়া প্রতিরোধ করে
              </li>
              <li className="flex items-center gap-3 text-2xl font-bold">
                <PiSealCheckFill className="flex-shrink-0 text-black" />
                চুলকে মজবুত করে
              </li>
              <li className="flex items-center gap-3 text-2xl font-bold">
                <PiSealCheckFill className="flex-shrink-0 text-black" />
                চুলের ফ্রিজিনেস দূর হয়
              </li>
              <li className="flex items-center gap-3 text-2xl font-bold">
                <PiSealCheckFill className="flex-shrink-0 text-black" />
                দ্রুত নতুন চুল গজায়
              </li>
            </ul>
          </div>
          <div className="bg-black py-2">
            <h2 className="text-center text-2xl font-bold text-white sm:text-3xl">
              ব্যাবহারবিধি
            </h2>
          </div>
          <div className="py-5">
            <div className="ml-0 flex items-start gap-3 px-3 sm:ml-10 sm:px-0">
              <TbHandStop className="flex-shrink-0 translate-y-1 text-2xl text-black" />
              <p className="text-2xl font-bold">
                পরিস্কার চুলে একটি TVLV Hair capsule কেটে ব্যাবহার করতে হবে ।
                TVLV Hair capsule তেলের সাথে মিশিয়েও ব্যাবহার করা যাবে । ভাল
                হয় রাতে ব্যাবহার করে দিনে ভাল ভাবে শ্যাম্পু করে নিলে ।
              </p>
            </div>
          </div>
          <div className="bg-black py-2">
            <h2 className="text-center text-2xl font-bold text-white sm:text-3xl">
              Customer Review
            </h2>
          </div>
          <div className="px-10 py-5">
            <Swiper
              modules={[Navigation, Pagination, A11y, Autoplay]}
              spaceBetween={20}
              slidesPerView={3}
              pagination={{ clickable: true }}
              autoplay={{
                delay: 3500,
                disableOnInteraction: false,
              }}
              loop={true}
              breakpoints={{
                320: {
                  slidesPerView: 1,
                },
                640: {
                  slidesPerView: 2,
                },
                1024: {
                  slidesPerView: 3,
                },
              }}
              onSwiper={(swiper) => console.log(swiper)}
              onSlideChange={() => console.log("slide change")}
            >
              <SwiperSlide>
                <div className="review-card h-full rounded-2xl bg-gray-100 p-5">
                  <div className="flex items-center gap-3">
                    <div className="h-[60px] w-[60px] rounded-full border border-gray-200">
                      <Image
                        src="https://i.pinimg.com/736x/8b/16/7a/8b167af653c2399dd93b952a48740620.jpg"
                        alt="logo"
                        width={20}
                        height={20}
                        className="h-full w-full rounded-full object-cover"
                      />
                    </div>
                    <div>
                      <h5 className="text-2xl font-semibold">Hasibur Rahman</h5>
                      <p>Pro Developer</p>
                    </div>
                  </div>
                  <p className="mt-3">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit.
                    Accusamus, quisquam optio eveniet ipsa, asperiores dolore
                    animi libero minus illum, dolorum voluptatum! Vero quibusdam
                    eaque optio, laudantium nihil ut accusamus dolorem.
                  </p>
                  <div className="mt-5 flex items-center gap-4">
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                  </div>
                </div>
              </SwiperSlide>
              <SwiperSlide>
                <div className="review-card h-full rounded-2xl bg-gray-100 p-5">
                  <div className="flex items-center gap-3">
                    <div className="h-[60px] w-[60px] rounded-full border border-gray-200">
                      <Image
                        src="https://i.pinimg.com/736x/8b/16/7a/8b167af653c2399dd93b952a48740620.jpg"
                        alt="logo"
                        width={20}
                        height={20}
                        className="h-full w-full rounded-full object-cover"
                      />
                    </div>
                    <div>
                      <h5 className="text-2xl font-semibold">Hasibur Rahman</h5>
                      <p>Pro Developer</p>
                    </div>
                  </div>
                  <p className="mt-3">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit.
                    Accusamus, quisquam optio eveniet ipsa, asperiores dolore
                    animi libero minus illum, dolorum voluptatum! Vero quibusdam
                    eaque optio, laudantium nihil ut accusamus dolorem.
                  </p>
                  <div className="mt-5 flex items-center gap-4">
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                  </div>
                </div>
              </SwiperSlide>
              <SwiperSlide>
                <div className="review-card h-full rounded-2xl bg-gray-100 p-5">
                  <div className="flex items-center gap-3">
                    <div className="h-[60px] w-[60px] rounded-full border border-gray-200">
                      <Image
                        src="https://i.pinimg.com/736x/8b/16/7a/8b167af653c2399dd93b952a48740620.jpg"
                        alt="logo"
                        width={20}
                        height={20}
                        className="h-full w-full rounded-full object-cover"
                      />
                    </div>
                    <div>
                      <h5 className="text-2xl font-semibold">Hasibur Rahman</h5>
                      <p>Pro Developer</p>
                    </div>
                  </div>
                  <p className="mt-3">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit.
                    Accusamus, quisquam optio eveniet ipsa, asperiores dolore
                    animi libero minus illum, dolorum voluptatum! Vero quibusdam
                    eaque optio, laudantium nihil ut accusamus dolorem.
                  </p>
                  <div className="mt-5 flex items-center gap-4">
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                  </div>
                </div>
              </SwiperSlide>
              <SwiperSlide>
                <div className="review-card h-full rounded-2xl bg-gray-100 p-5">
                  <div className="flex items-center gap-3">
                    <div className="h-[60px] w-[60px] rounded-full border border-gray-200">
                      <Image
                        src="https://i.pinimg.com/736x/8b/16/7a/8b167af653c2399dd93b952a48740620.jpg"
                        alt="logo"
                        width={20}
                        height={20}
                        className="h-full w-full rounded-full object-cover"
                      />
                    </div>
                    <div>
                      <h5 className="text-2xl font-semibold">Hasibur Rahman</h5>
                      <p>Pro Developer</p>
                    </div>
                  </div>
                  <p className="mt-3">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit.
                    Accusamus, quisquam optio eveniet ipsa, asperiores dolore
                    animi libero minus illum, dolorum voluptatum! Vero quibusdam
                    eaque optio, laudantium nihil ut accusamus dolorem.
                  </p>
                  <div className="mt-5 flex items-center gap-4">
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                    <FaStar className="text-yellow-500" />
                  </div>
                </div>
              </SwiperSlide>
            </Swiper>
          </div>
        </div>
      </section>

      <div className="mt-16 bg-white">
        <DirectOrderForm productDetails={productDetails?.productDetails} />
      </div>

      {productDetails?.generalQuestions?.length ? (
        <div className="px-2 py-6 text-start md:px-10 md:py-10 lg:px-20 xl:px-40">
          <div className="pb-4 text-center text-2xl font-bold">
            সাধারণ জিজ্ঞাসা
          </div>
          {productDetails?.generalQuestions?.map((single) => (
            <Accordion title={single?.question} key={single?.question}>
              <div
                dangerouslySetInnerHTML={{
                  __html: single?.answer ? single?.answer : "<p>No answer</p>",
                }}
              />
            </Accordion>
          ))}
        </div>
      ) : (
        ""
      )}

      <div className="social-icons fixed bottom-4 right-4">
        <div className="all-content relative">
          <div
            className="hover-and-cross-trigger-button"
            onMouseEnter={handleHover}
            onMouseLeave={handleLeave}
          >
            <button
              className={`trigger-button flex h-[60px] w-[60px] items-center justify-center rounded-full bg-red-600 ${
                isHovered || isContentVisible ? "hidden" : ""
              }`}
              onClick={handleTriggerClick}
            >
              <FaFacebookMessenger className="text-3xl text-white" />
            </button>
            <button
              className={`cross-trigger-button flex h-[60px] w-[60px] items-center justify-center rounded-full bg-red-600 transition-transform duration-500 ease-in-out ${
                isHovered ? "" : "hidden"
              }`}
              onClick={handleCrossClick}
            >
              <RxCross2 className="active:rotate-360 text-3xl text-white transition-transform duration-500 ease-in-out hover:rotate-180" />
            </button>
          </div>
          <div
            className={`content absolute bottom-[70px] left-0 space-y-2 transition-all duration-500 ease-in-out ${
              isContentVisible
                ? "translate-y-0 opacity-100 delay-150"
                : "translate-y-5 opacity-0"
            }`}
          >
            <div className="space-y-2">
              <a
                href="#"
                className="trigger-button flex h-[60px] w-[60px] items-center justify-center rounded-full bg-[#03E78B]"
              >
                <FaPhone className="text-3xl text-white" />
              </a>
              <a
                href="#"
                className="trigger-button flex h-[60px] w-[60px] items-center justify-center rounded-full bg-[#49E670]"
              >
                <IoLogoWhatsapp className="text-3xl text-white" />
              </a>
              <a
                href="#"
                className="trigger-button flex h-[60px] w-[60px] items-center justify-center rounded-full bg-[#1E88E5]"
              >
                <FaFacebookMessenger className="text-3xl text-white" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPageFixOverview;
