"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import banner2 from "@/dummyImages/hero/hero-img-2.png";
import banner1 from "@/dummyImages/hero/hero-img.png";
import { SingleBanner } from "@/services/bannerServices";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";

const LandingPageBannerTwo = ({ banners }: { banners: SingleBanner[] }) => {
  const router = useRouter();

  return (
    <>
      <div className="px-4 md:px-6 lg:px-20 xl:px-28">
        <div className="grid grid-cols-12 gap-5">
          <div className="col col-span-12 md:col-span-8">
            <div className="">
              <Carousel
                autoPlay={true}
                showThumbs={false}
                showStatus={false}
                infiniteLoop={true}
                interval={4000}
                transitionTime={2000}
              >
                {banners &&
                  banners
                    ?.filter((sin: SingleBanner) => sin.bannerType === "main")
                    ?.map((banner: SingleBanner) => (
                      <div
                        key={banner._id}
                        className="h-full"
                        onClick={() =>
                          router.push(
                            `/${banner.redirectType}/${banner.redirectSlug}`,
                          )
                        }
                      >
                        <Image
                          height={100}
                          width={100}
                          src={generateProductImage(banner.imageUrl)}
                          alt="Carousel Image 1"
                          className="h-[22vh] w-full rounded-lg sm:h-[40vh] md:h-[35vh] lg:h-[40vh] xl:h-[55vh] 2xl:h-[60vh]"
                          priority={true}
                        />
                      </div>
                    ))}
              </Carousel>
            </div>
          </div>
          <div className="col col-span-12 hidden md:col-span-4 md:block">
            <div className="h-full">
              <Image
                height={100}
                width={100}
                src={
                  banners?.find((sin) => sin.bannerType === "right")
                    ? generateProductImage(
                        banners?.find((sin) => sin?.bannerType === "right")
                          ?.imageUrl as string,
                      )
                    : banner2
                }
                alt={"Carousel Image 2"}
                className="h-[22vh] w-full rounded-lg sm:h-[40vh] md:h-[35vh] lg:h-[40vh] xl:h-[55vh] 2xl:h-[60vh]"
                priority={true}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LandingPageBannerTwo;
