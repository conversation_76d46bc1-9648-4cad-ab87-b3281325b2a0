name: Deploy to Production Pipeline

on:
  push:
    branches: [main]

jobs:
  setup:
    runs-on: ubuntu-22.04
    steps:
      - name: Execute commands on server
        uses: appleboy/ssh-action@v1.2.0
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SERVER_PASSWORD }}
          script: |
            echo "Checking if Docker is installed..."
            if ! command -v docker &> /dev/null
            then
              echo "Docker not found. Installing Docker..."
              sudo apt-get update
              sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common
              curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
              echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
              sudo apt-get update
              sudo apt-get install -y docker-ce docker-ce-cli containerd.io
              sudo usermod -aG docker $USER
              echo "Docker installed successfully."
            else
              echo "Docker is already installed."
            fi

            echo "Checking if Docker Compose is installed..."
            if ! command -v docker-compose &> /dev/null
            then
              echo "Docker Compose not found. Installing Docker Compose..."
              sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
              sudo chmod +x /usr/local/bin/docker-compose
              sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose
              echo "Docker Compose installed successfully."
            else
              echo "Docker Compose is already installed."
            fi

            echo "Checking if NGINX is installed..."
            if ! command -v nginx &> /dev/null
            then
              echo "NGINX not found. Installing NGINX..."
              sudo apt-get update
              sudo apt-get install -y nginx
              sudo systemctl start nginx
              sudo systemctl enable nginx
              echo "NGINX installed and started successfully."
            else
              echo "NGINX is already installed."
            fi

            echo "Checking if Certbot and python3-certbot-nginx are installed..."
            if ! command -v certbot &> /dev/null
            then
              echo "Certbot not found. Installing Certbot and python3-certbot-nginx..."
              sudo apt-get update
              sudo apt-get install -y certbot python3-certbot-nginx
              echo "Certbot and python3-certbot-nginx installed successfully."
            else
              echo "Certbot and python3-certbot-nginx are already installed."
            fi

  build:
    needs: setup
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            hrd2571/ktm-frontend:latest
          # cache-from: type=registry,ref=${{ secrets.DOCKER_USERNAME }}/${{ env.DOCKER_REPOSITORY }}:buildcache
          # cache-to: type=registry,ref=${{ secrets.DOCKER_USERNAME }}/${{ env.DOCKER_REPOSITORY }}:buildcache,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Copy docker-compose.yml to server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SERVER_PASSWORD }}
          source: "./docker-compose.yml"
          target: "~/ktm-frontend"

      - name: Copy nginx config to server
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SERVER_PASSWORD }}
          source: "./frontend.conf"
          target: "/etc/nginx/conf.d/"

      - name: Deploy and ensure HTTPS
        uses: appleboy/ssh-action@v1.2.0
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          password: ${{ secrets.SERVER_PASSWORD }}
          script: |
            cd ~/ktm-frontend

            # Restart application containers
            docker-compose down
            docker container prune -f
            docker image prune -f
            docker-compose pull
            docker-compose up -d
            docker ps -a
            docker-compose logs --tail=100 app

            # Reload NGINX to apply new configuration
            sudo nginx -t && sudo systemctl reload nginx

            # Ensure HTTPS with Certbot
            DOMAIN_NAME=${{ secrets.DOMAIN_NAME }}
            sudo certbot --nginx -d $DOMAIN_NAME --non-interactive --agree-tos --email ${{ secrets.ADMIN_EMAIL }}

            # Verify the SSL certificate
            # sudo certbot renew --dry-run
