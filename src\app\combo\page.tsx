import ComboAndOfferPageOverview from "@/Components/ComboAndOfferPageComponents/ComboAndOfferPageOverview";
import ShopLayout from "@/Layouts/ShopLayout";
import { BASE_URL } from "@/environment/environment";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
  title: "Products | INNI",
};

interface SearchParams {
  size: number;
  page: string;
}

const ComboPage = async ({
  searchParams,
}: {
  searchParams: Promise<SearchParams>;
}) => {
  const accessToken = (await cookies()).get("GMK")?.value;
  const { size, page } = await searchParams;
  const data = await getProductList({
    size: size ? parseInt(size.toString()) : 20,
    page: page ? page.toString() : "1",
  });

  return (
    <ShopLayout>
      <ComboAndOfferPageOverview
        accessToken={accessToken ? accessToken : ""}
        data={data}
        url="combo"
        imageUrl="https://i.ibb.co/YkpDCmb/color-cosmetics-banner.gif"
      />
    </ShopLayout>
  );
};

export default ComboPage;

async function getProductList({ size, page }: SearchParams) {
  const queryParams = new URLSearchParams();

  if (size) queryParams.append("size", size.toString());
  if (page) queryParams.append("page", page.toString());
  queryParams.append("isCombo", "true");

  const filter = queryParams.toString();
  const res = await fetch(`${BASE_URL}/products/get-all?${filter}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}
