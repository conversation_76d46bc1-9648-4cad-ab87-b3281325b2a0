import ProductsPage from "@/Components/ProductsPage/ProductsPage";
import ShopLayout from "@/Layouts/ShopLayout";
import { BASE_URL } from "@/environment/environment";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";

interface Props {
  params: Promise<{ slug: string; sub: string }>;
  searchParams: Promise<SearchParams>;
}

interface SearchParams {
  page: string;
  category: string;
  subCategory: string;
}

const CategoryPage = async ({ params, searchParams }: Props) => {
  const accessToken = (await cookies()).get("GMK")?.value;
  const { page } = await searchParams;
  const { slug, sub } = await params;
  const data = await getProductList({
    page: page,
    category: slug,
    subCategory: sub,
  });

  return (
    <ShopLayout>
      <ProductsPage
        accessToken={accessToken ? accessToken : ""}
        data={data}
        pageType="subCategory"
        categoryName={slug}
        subCategoryName={sub}
      />
    </ShopLayout>
  );
};

export default CategoryPage;

async function getProductList({ page, category, subCategory }: SearchParams) {
  const queryParams = new URLSearchParams();

  if (page) queryParams.append("page", page.toString());
  if (category) queryParams.append("productCategory", category);
  if (subCategory) queryParams.append("subCategory", subCategory);

  const filter = queryParams.toString();
  const res = await fetch(`${BASE_URL}/products/get-all?${filter}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}
