import {
  GeneralQuestion,
  LandingPageProductDetails,
} from "@/Types/landingPageProductType";
import Image from "next/image";
import { generateProductImage } from "../utils/GenerateProductImage";
import Accordion from "./Accordion";
import DirectBuySecondPart from "./DirectBuySecondPart";
import DirectBuyThirdPart from "./DirectBuyThirdPart";
import DirectBuyTopPart from "./DirectBuyTopPart";
import DirectOrderForm from "./DirectOrderForm";

interface Props {
  productDetails: LandingPageProductDetails;
}

const DirectBuyLandingPageOverview = ({ productDetails }: Props) => {
  return (
    <div
      className="scroll-smooth text-center"
      style={{ scrollBehavior: "smooth" }}
    >
      <DirectBuyTopPart
        title={productDetails?.title}
        video={productDetails?.video}
        youtubeUrl={productDetails?.youtubeUrl}
        punchLine={productDetails?.punchLine}
        priceLine={productDetails?.priceLine}
        mainImageUrl={productDetails.productDetails?.mainImageUrl}
      />
      <DirectBuySecondPart
        isCombo={productDetails?.productDetails?.isCombo}
        whyBest={productDetails?.whyBest}
        whyBestTwo={productDetails?.whyBestTwo}
      />
      {/* <div className="mt-4 flex flex-col items-center gap-8 px-2 py-6 sm:px-4 md:px-10 md:py-10 lg:px-20 xl:px-40">
        <div className="flex flex-wrap items-center gap-8 sm:flex-nowrap">
          <div className="flex w-full flex-col gap-4 text-start  text-xl">
            <div className="text-start font-semibold md:text-2xl md:font-bold">
              {productDetails?.whyBuy?.title}
            </div>
            {productDetails?.whyBuy?.options?.map((single: string) => (
              <div className="flex items-start gap-2" key={single}>
                <div>
                  <AiFillHeart className="text-xl md:text-2xl" />
                </div>
                <span className="text-sm md:text-lg">{single}</span>
              </div>
            ))}
            <OrderNowButton />
          </div>
          <Image
            height={100}
            width={100}
            src={generateProductImage(productDetails.whyBuy?.image)}
            alt="why best"
            className="w-full  rounded-xl border-4 border-[#4E001F] lg:w-[400px]"
          />
        </div>
      </div> */}
      <DirectBuyThirdPart usage={productDetails?.usage} />
      {productDetails?.reviewImages?.length ? (
        <div className="px-2 py-6 text-start md:px-10 md:py-10 lg:px-20 xl:px-40">
          <div className="pb-4 text-center text-2xl font-bold">
            আমাদের সম্মানিত কাস্টমার রিভিউ
          </div>
          <div className="lg:gird-cols-4 grid grid-cols-2 gap-4 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4">
            {productDetails?.reviewImages?.map((single: string) => (
              <Image
                key={single}
                height={100}
                width={100}
                src={generateProductImage(single)}
                alt="usage"
                className="h-[250px] w-full rounded-xl border-4 border-[#4E001F] md:h-[350px]  lg:w-[350px]"
              />
            ))}
          </div>
        </div>
      ) : (
        ""
      )}
      <div className="px-2 py-6 text-start md:px-10 md:py-10 lg:px-20 xl:px-40">
        <div className="pb-4 text-center text-2xl font-bold">
          সাধারণ জিজ্ঞাসা
        </div>
        {productDetails?.generalQuestions?.map((single: GeneralQuestion) => (
          <Accordion title={single?.question} key={single?.question}>
            <div
              dangerouslySetInnerHTML={{
                __html: single?.answer ? single?.answer : "<p>No answer</p>",
              }}
            />
          </Accordion>
        ))}
      </div>
      <div>
        <DirectOrderForm productDetails={productDetails?.productDetails} />
      </div>
    </div>
  );
};

export default DirectBuyLandingPageOverview;
