export interface ProductListType {
  status: boolean;
  message: string;
  results: SingleProductOfList[];
  count: number;
  totalNoOfPages: number;
  currentPage: string;
}

export interface RelatedProductListType {
  status: boolean;
  message: string;
  result: {
    relatedProducts: SingleProductOfList[];
    sameBrandProducts: SingleProductOfList[];
  };
}

export interface SingleProductOfList {
  _id: string;
  id: string;
  slug: string;
  name: string;
  price: number;
  discountPrice: number;
  deal: string;
  mainImageUrl: string;
  rating: number;
  brand: string;
  size: string;
  isAvailable: boolean;
  productCategory: string;
  totalSold: number;
}

export interface SingleProductResponse {
  status: boolean;
  message: string;
  result: SingleProductDetailsType;
}
export interface SingleProductDetailsType {
  _id: string;
  id: string;
  slug: string;
  name: string;
  size: string;
  availableQuantity: number;
  price: number;
  discountPrice: number;
  mainImageUrl: string;
  video: string;
  images: string[];
  deal: string;
  brand: string;
  brandSlug: string;
  categorySlug: string;
  productCategory: string;
  ingredients: string;
  skinType: string[];
  skinConcern: string;
  // productType: string;
  origin: string;
  isFeatured: boolean;
  isTopSelling: boolean;
  isCombo: boolean;
  isOnSale: boolean;
  details: string;
  productDescription: string;
  howToUse: string;
  banglaDescription: string;
  isAvailable: boolean;
  rating: number;
  relatedProducts: any[];
  linkedProduct: {
    slug: string;
    name: string;
    mainImageUrl: string;
    price: number;
    discountPrice: number;
    id: string;
    size: string;
    deal: string;
    rating: number;
    brand: string;
    isAvailable: boolean;
  };
  reviews?: {
    id: string;
    reviewerId: string;
    reviewerName: string;
    reviewerEmail: string;
    reviewMessage: string;
    reviewImage: string;
    rating: number;
  }[];
}
