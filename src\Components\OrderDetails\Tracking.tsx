import { SingleOrderDetails } from "@/Types/orderTypes";
import { BsCircleFill } from "react-icons/bs";
import TitleViewer from "../ReusableComponents/TitleViewer/TitleViewer";

interface Props {
  orderDetails: SingleOrderDetails;
}
const Tracking = ({ orderDetails }: Props) => {
  return (
    <div className="mt-4">
      <TitleViewer titleTwo="Tracking" titleOne="" seeAllButton={false} />
      <div className="flex items-center justify-center">
        <div>
          {orderDetails?.trackingHistory.reverse().map((single, index) => (
            <div
              key={index}
              className="flex  gap-4 text-green-600"
              style={{
                alignItems:
                  index === orderDetails.trackingHistory.length - 1
                    ? "start"
                    : "",
              }}
            >
              <p className="text-sm w-[100px]">
                {new Intl.DateTimeFormat("en-US", {
                  day: "2-digit",
                  month: "short",
                  year: "numeric",
                }).format(new Date(single.time))}
                <br />
                {new Intl.DateTimeFormat("en-US", {
                  hour: "2-digit",
                  minute: "2-digit",
                }).format(new Date(single.time))}
              </p>
              <div className="flex flex-col items-center justify-center">
                <BsCircleFill />
                <div
                  style={{
                    display:
                      index === orderDetails.trackingHistory.length - 1
                        ? "none"
                        : "block",
                    height: "60px",
                    width: "2px",
                  }}
                  className="bg-green-600"
                ></div>
              </div>
              <p className="w-[200px] md:w-[350px]">{single.message}</p>
            </div>
          ))}
          {/* <div className="flex  gap-4 text-green-600">
            <p className="text-sm"></p>
            <div className="flex flex-col items-center justify-center">
              <BsCircleFill />
            </div>
            <p></p>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default Tracking;
