import { HomePageContentResponse } from "@/Components/LandingPage/LandingPageOverview/homePageContentTypes";
import LandingPageUpdatedOverview from "@/Components/LandingPage/LandingPageOverview/LandingPageUpdatedOverview";
import { BASE_URL } from "@/environment/environment";
import HomepageLayout from "@/Layouts/HomepageLayout";
import { cookies } from "next/headers";
import NotFound from "./not-found";

export default async function Home() {
  const accessToken = (await cookies()).get("GMK")?.value;
  const data: HomePageContentResponse = await getHomePageContent();
  return (
    <HomepageLayout>
      <LandingPageUpdatedOverview
        accessToken={accessToken ? accessToken : ""}
        data={data}
      />
    </HomepageLayout>
  );
}

async function getHomePageContent() {
  const res = await fetch(`${BASE_URL}/homepage/get-all-home`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    NotFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}
