"use client";
import { NoCartItemFound } from "@/Components/ReusableComponents/NoResultFound/NoResultFound";
import TitleViewer from "@/Components/ReusableComponents/TitleViewer/TitleViewer";
import { deleteModal } from "@/Components/utils/commonModal";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleLocalCartItem } from "@/Types/localCartTypes";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import toast from "react-hot-toast";
import {
  SingleItemOfCartCard,
  SingleItemOfCartTable,
} from "./NoUserCartSingleItem";

const NoUserCartOverview = () => {
  const router = useRouter();
  const {
    localCartItems,
    handleDeleteItemFromLocalCart,
    handleAddToLocalCheckout,
    handleClearLocalCart,
    updateLocalCartRefreshCounter,
  }: ContextDataType = useContext(LocalDataContext);

  const [selectedForCheckout, setSelectedForCheckout] = useState<
    SingleLocalCartItem[]
  >([]);

  const [totalQuantity, setTotalQuantity] = useState<number>(0);
  const [totalProductPrice, setTotalProductPrice] = useState<number>(0);
  const [totalDiscount, setTotalDiscount] = useState<number>(0);
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);

  const handleSelectOrUnselectAll = (type: string) => {
    if (type === "select") {
      setSelectedForCheckout(localCartItems);
    } else {
      setSelectedForCheckout([]);
    }
  };

  const handleSelectOrUnselectSingle = (product: SingleLocalCartItem) => {
    const isExist = selectedForCheckout.some(
      (item: SingleLocalCartItem) => item.id === product.id,
    );
    if (isExist) {
      setSelectedForCheckout(
        selectedForCheckout.filter(
          (singleItem: SingleLocalCartItem) => singleItem.id !== product.id,
        ),
      );
    } else {
      setSelectedForCheckout([...selectedForCheckout, product]);
    }
  };

  //need to modify this logic
  const handleUpdateQuantity = () => {
    const selectedArray: SingleLocalCartItem[] = [];
    selectedForCheckout.map((single: SingleLocalCartItem) => {
      const item = localCartItems.find(
        (sin: SingleLocalCartItem) => sin.id === single.id,
      );
      selectedArray.push(item!);
    });
    setSelectedForCheckout(selectedArray);
  };

  const handleCheckout = () => {
    handleAddToLocalCheckout(selectedForCheckout);
    toast.success("Added for checkout");
    router.push(ROUTES.CHECKOUT);
  };

  const handleDeleteFromCart = (id: string) => {
    deleteModal("Cart", () => {
      handleDeleteItemFromLocalCart(id);
      setSelectedForCheckout(
        selectedForCheckout.filter(
          (single: SingleLocalCartItem) => single?.id !== id,
        ),
      );
      toast.success("Item Deleted From Cart");
    });
  };

  const handleDeleteAllFromCart = () => {
    deleteModal("Cart", () => {
      handleClearLocalCart();
      setSelectedForCheckout([]);
      updateLocalCartRefreshCounter();
      window.location.reload();
      toast.success("Cart Cleared");
    });
  };

  useEffect(() => {
    let quantity = 0;
    let price = 0;
    let discount = 0;
    selectedForCheckout?.map((singleItem: SingleLocalCartItem) => {
      (quantity += singleItem?.quantity),
        (price += singleItem?.price * singleItem?.quantity);
      discount +=
        (singleItem?.price - singleItem?.discountPrice) * singleItem?.quantity;
    });
    setTotalQuantity(quantity);
    setTotalProductPrice(price);
    setTotalDiscount(discount);
    if (selectedForCheckout?.length) {
      const data = {
        event: "view_cart",
        url: window.location.href,
        value: price - discount,
        user_id: "",
        user_name: "",
        user_phone_number: "",
        user_email: "",
        user_zip: "",
        user_district: "",
        user_log_in_status: "not logged in",
        items: selectedForCheckout
          ? selectedForCheckout.map((single: SingleLocalCartItem) => {
              return {
                item_id: single?.id,
                item_name: single?.name,
                price: single?.discountPrice,
                item_brand: single?.brand,
                item_category: single?.productCategory,
                slug: single?.slug,
                sku: single?.id?.replace("GMK-", ""),
              };
            })
          : null,
      };
      gtmDataLayerDataPass(data);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedForCheckout]);

  useEffect(() => {
    if (localCartItems) {
      if (isInitialLoad) {
        handleSelectOrUnselectAll("select");
        setIsInitialLoad(false);
      } else if (
        !isInitialLoad &&
        selectedForCheckout.length === localCartItems.length
      ) {
        handleSelectOrUnselectAll("select");
      } else {
        handleUpdateQuantity();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [localCartItems]);

  return (
    <div className="mt-2">
      <TitleViewer titleTwo="Cart" titleOne="" seeAllButton={false} />
      <>
        {localCartItems?.length ? (
          <div className="flex flex-col gap-2 md:flex-row">
            {/* Product list in big screen*/}
            <div className="hidden h-1/2 rounded-lg border p-4 shadow-sm md:block md:w-2/3">
              <div className="flex items-center justify-between pb-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={
                      selectedForCheckout?.length === localCartItems.length
                    }
                    onClick={() =>
                      handleSelectOrUnselectAll(
                        selectedForCheckout?.length === localCartItems.length
                          ? "unselect"
                          : "select",
                      )
                    }
                  />
                  <p
                    className="cursor-pointer"
                    onClick={() =>
                      handleSelectOrUnselectAll(
                        selectedForCheckout?.length === localCartItems.length
                          ? "unselect"
                          : "select",
                      )
                    }
                  >
                    {selectedForCheckout?.length === localCartItems.length
                      ? "Unselect All"
                      : "Select All"}
                  </p>
                </div>
                <p
                  className="cursor-pointer"
                  onClick={() => handleDeleteAllFromCart()}
                >
                  Delete all
                </p>
              </div>
              <hr className="pb-2" />
              <div className="overflow-x-auto">
                <table className="w-full border-collapse overflow-x-auto">
                  <thead>
                    <tr>
                      <th></th>
                      <th className="text-center">Product</th>
                      <th className="text-center">Price</th>
                      <th className="text-center">Quantity</th>
                      <th className="text-center">Subtotal</th>
                      <th className="text-center">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {localCartItems.map(
                      (product: SingleLocalCartItem, index: number) => (
                        <SingleItemOfCartTable
                          key={product.id}
                          product={product}
                          selectedForCheckout={selectedForCheckout}
                          handleSelectOrUnselectSingle={
                            handleSelectOrUnselectSingle
                          }
                          handleDeleteFromCart={handleDeleteFromCart}
                          isLastItem={localCartItems?.length === index + 1}
                        />
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            </div>
            {/* Product list in big screen end*/}
            {/* Product list in small screen */}
            <div className="order-2 block rounded-lg border p-2 shadow-md md:hidden md:w-2/3">
              <div className="flex items-center justify-between pb-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={
                      selectedForCheckout?.length === localCartItems.length
                    }
                    onClick={() =>
                      handleSelectOrUnselectAll(
                        selectedForCheckout?.length === localCartItems.length
                          ? "unselect"
                          : "select",
                      )
                    }
                  />
                  <p
                    className="cursor-pointer"
                    onClick={() =>
                      handleSelectOrUnselectAll(
                        selectedForCheckout?.length === localCartItems.length
                          ? "unselect"
                          : "select",
                      )
                    }
                  >
                    {selectedForCheckout?.length === localCartItems.length
                      ? "Unselect All"
                      : "Select All"}
                  </p>
                </div>
                <p
                  className="cursor-pointer"
                  onClick={() => handleDeleteAllFromCart()}
                >
                  Delete all
                </p>
              </div>
              <hr className="pb-2" />
              {localCartItems.map(
                (product: SingleLocalCartItem, index: number) => (
                  <SingleItemOfCartCard
                    key={product.id}
                    product={product}
                    selectedForCheckout={selectedForCheckout}
                    handleSelectOrUnselectSingle={handleSelectOrUnselectSingle}
                    handleDeleteFromCart={handleDeleteFromCart}
                    isLastItem={localCartItems?.length === index + 1}
                  />
                ),
              )}
              <div className="flex w-full items-center justify-center px-2">
                <button
                  className="bg-primary mt-4 w-full rounded-lg px-4 py-2 font-normal text-white disabled:bg-gray-400"
                  disabled={selectedForCheckout.length ? false : true}
                  onClick={() => handleCheckout()}
                  type="button"
                >
                  Checkout
                </button>
              </div>
            </div>
            {/* Product list in small screen  end*/}

            {/* Right Part: Cart Summary Table */}
            <div className="h-1/2 rounded-lg border-2 p-4 shadow-lg md:w-1/3">
              <h2 className="mb-4 text-center text-2xl font-semibold">
                Cart Summary
              </h2>
              <table className="w-full ">
                <tbody>
                  <tr style={{ backgroundColor: "#E1E3E7" }}>
                    <td className="p-2 text-sm font-normal">Quantity</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">{totalQuantity}</td>
                  </tr>
                  <tr style={{ backgroundColor: "white" }}>
                    <td className="p-2 text-sm font-normal">Product Price</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      ৳ {totalProductPrice}
                    </td>
                  </tr>
                  <tr style={{ backgroundColor: "#E1E3E7" }}>
                    <td className="p-2 text-sm font-normal">Discount</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      ৳ {totalDiscount}
                    </td>
                  </tr>
                  <tr style={{ backgroundColor: "white" }}>
                    <td className="p-2 text-sm font-normal">Subtotal Price</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      ৳ {totalProductPrice - totalDiscount}
                    </td>
                  </tr>
                  <tr style={{ backgroundColor: "#E1E3E7" }}>
                    <td className="p-2 text-sm font-normal">Delivery Charge</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      Inside Dhaka : ৳ 80 <br /> Outside Dhaka : ৳ 150
                    </td>
                  </tr>
                  <tr style={{ backgroundColor: "white" }}>
                    <td className="p-2 text-sm font-normal">Total</td>
                    <td className="p-2 text-sm font-normal">:</td>
                    <td className="p-2 text-sm font-normal">
                      ৳ {totalProductPrice - totalDiscount} + delivery charge
                    </td>
                  </tr>
                </tbody>
              </table>
              <div className="flex w-full items-center justify-center px-2">
                <button
                  className="bg-primary mt-4 w-full rounded-lg px-4 py-2 font-normal text-white disabled:bg-gray-400"
                  disabled={selectedForCheckout.length ? false : true}
                  onClick={() => handleCheckout()}
                  type="button"
                >
                  Checkout
                </button>
              </div>
            </div>
          </div>
        ) : (
          <NoCartItemFound />
        )}
      </>
    </div>
  );
};

export default NoUserCartOverview;
