"use client";

import { SingleCartItem } from "@/Types/cartPageTypes";
import { SingleLocalCartItem } from "@/Types/localCartTypes";
import { UserHeaderResponse } from "@/Types/userTypes";
import { SingleWishlistItem } from "@/Types/wishlistTypes";
import { readCookie } from "@/services/apiResource";
import {
  SingleBrand,
  SingleCategory,
  getBrandsListApi,
  getCategoryListApi,
  getSkinConcernListApi,
  getSkinTypeListApi,
} from "@/services/bannerBrandCategoryServices";
import { SingleBanner, getBannersApi } from "@/services/bannerServices";
import { getCartApi } from "@/services/cartServices";
import { getUserHeaderDetailsApi } from "@/services/userServices";
import { getWishlistApi } from "@/services/wishlistServices";
import { useQuery } from "@tanstack/react-query";
import { createContext, useEffect, useState } from "react";

export const LocalDataContext = createContext<any>(null);

const LocalDataProvider = ({ children }: { children: any }) => {
  const allData = LocalDataProviderHook();
  return (
    <LocalDataContext.Provider value={allData}>
      {children}
    </LocalDataContext.Provider>
  );
};

export default LocalDataProvider;

const LocalDataProviderHook = () => {
  const accessToken = readCookie();
  const [localCartItems, setLocalCartItems] = useState<SingleLocalCartItem[]>();
  const [localCheckoutItems, setLocalCheckoutItems] = useState<any>();
  const [refreshCounter, setRefreshCounter] = useState<number>(0);
  const [refreshCheckoutCounter, setRefreshCheckoutCounter] =
    useState<number>(0);
  const [userHeaderDetails, setUserHeaderDetails] =
    useState<UserHeaderResponse>();
  const [wishlist, setWishlist] = useState<SingleWishlistItem[]>();
  const [isWishlistLoading, setIsWishlistLoading] = useState<boolean>(true);
  const [cartItems, setCartItems] = useState<SingleCartItem[]>();
  const [isCartLoading, setIsCartLoading] = useState<boolean>(true);

  const updateLocalCartRefreshCounter = () => {
    setRefreshCounter(refreshCounter + 1);
  };

  // add product on local storage cart
  const handleAddToLocalCart = (productData: SingleLocalCartItem) => {
    const loc = localStorage.getItem("cartItems");
    const existingItems: SingleLocalCartItem[] = loc && JSON.parse(loc);
    if (existingItems) {
      const isExist: boolean = existingItems.some(
        (singleItem: SingleLocalCartItem) => singleItem.id === productData.id,
      );
      if (isExist) {
        handleUpdateSingleCartItem(productData.id, productData.quantity);
      } else {
        const newItems = [...existingItems, productData];
        localStorage.setItem("cartItems", JSON.stringify(newItems));
        updateLocalCartRefreshCounter();
      }
    } else {
      localStorage.setItem("cartItems", JSON.stringify([productData]));
      updateLocalCartRefreshCounter();
    }
  };

  // delete product from local storage cart
  const handleDeleteItemFromLocalCart = (id: string) => {
    const loc = localStorage.getItem("cartItems");
    const existingItems: SingleLocalCartItem[] = loc && JSON.parse(loc);
    const remaining = existingItems?.filter(
      (single: SingleLocalCartItem) => single.id !== id,
    );
    localStorage.setItem("cartItems", JSON.stringify(remaining));
    updateLocalCartRefreshCounter();
  };

  // update product on local storage cart
  const handleUpdateSingleCartItem = (
    id: string | number,
    quantity: number,
  ) => {
    const loc = localStorage.getItem("cartItems");
    const existingItems: SingleLocalCartItem[] = loc && JSON.parse(loc);
    const index = existingItems.findIndex((item) => item.id === id);
    if (index !== -1) {
      existingItems[index].quantity = quantity;
      localStorage.setItem("cartItems", JSON.stringify(existingItems));
      setRefreshCounter(refreshCounter + 1);
    } else {
      console.error(`Item with id ${id} not found in cart.`);
    }
  };

  // add product on local storage checkout
  const handleAddToLocalCheckout = (data: any) => {
    if (data.length) {
      localStorage.setItem("checkoutItems", JSON.stringify(data));
      setRefreshCheckoutCounter(refreshCheckoutCounter + 1);
    }
  };

  // clear cart
  const handleClearLocalCart = () => {
    localStorage.removeItem("cartItems");
    updateLocalCartRefreshCounter();
  };

  // clear checkout
  const handleClearLocalCheckout = () => {
    localStorage.removeItem("checkoutItems");
    setRefreshCheckoutCounter(refreshCheckoutCounter + 1);
  };

  // get user header info
  const { api, getKey } = getUserHeaderDetailsApi();
  const { data } = useQuery(getKey(), api, {
    refetchOnWindowFocus: false,
    enabled: accessToken ? true : false,
    onSuccess(data) {
      setUserHeaderDetails(data);
    },
  });

  // get user wishlist info
  const { api: GetWishlistApi, getKey: GetWishlistKey } = getWishlistApi();
  const { isLoading, refetch: refetchWishlist } = useQuery(
    GetWishlistKey(),
    GetWishlistApi,
    {
      refetchOnWindowFocus: false,
      enabled: accessToken ? true : false,
      onSuccess(data) {
        setWishlist(data.results);
        setIsWishlistLoading(false);
      },
    },
  );

  // get user cart info
  const { api: GetCartApi, getKey: GetCartKey } = getCartApi();
  const { data: cart, refetch: refetchCart } = useQuery(
    GetCartKey(),
    GetCartApi,
    {
      refetchOnWindowFocus: false,
      enabled: accessToken ? true : false,
      onSuccess(data) {
        setCartItems(data.results);
        setIsCartLoading(false);
      },
    },
  );

  //get categories
  const { api: categoriesApi, getKey: categoriesGetKey } = getCategoryListApi();
  const { data: categories, isLoading: isCategoriesLoading } = useQuery(
    categoriesGetKey(),
    categoriesApi,
    {
      refetchOnWindowFocus: false,
    },
  );

  //get brands
  const { api: brandsApi, getKey: getBrandsKey } = getBrandsListApi();
  const { data: brands, isLoading: isBrandsLoading } = useQuery(
    getBrandsKey(),
    brandsApi,
    {
      refetchOnWindowFocus: false,
    },
  );

  //get skin types
  const { api: skinTypesApi, getKey: getSkinTypesKey } = getSkinTypeListApi();
  const { data: skinTypes, isLoading: isSkinTypesLoading } = useQuery(
    getSkinTypesKey(),
    skinTypesApi,
    {
      refetchOnWindowFocus: false,
    },
  );

  //get skin concern
  const { api: skinConcernApi, getKey: getSkinConcernKey } =
    getSkinConcernListApi();
  const { data: skinConcern, isLoading: isSkinConcernLoading } = useQuery(
    getSkinConcernKey(),
    skinConcernApi,
    {
      refetchOnWindowFocus: false,
    },
  );

  //get banners
  const { api: bannersApi, getKey: getBannersKey } = getBannersApi();
  const { data: banners, isLoading: isBannersLoading } = useQuery(
    getBannersKey(),
    bannersApi,
    {
      refetchOnWindowFocus: false,
    },
  );

  // hook to get local cart
  useEffect(() => {
    const loc = localStorage.getItem("cartItems");
    loc && setLocalCartItems(JSON.parse(loc));
  }, [refreshCounter]);

  // hook to get local checkout
  useEffect(() => {
    const loc = localStorage.getItem("checkoutItems");
    loc && setLocalCheckoutItems(JSON.parse(loc));
  }, [refreshCheckoutCounter]);

  return {
    userHeaderDetails,
    isWishlistLoading,
    wishlist,
    isCartLoading,
    cartItems,
    localCartItems,
    localCheckoutItems,
    handleAddToLocalCart,
    handleUpdateSingleCartItem,
    handleDeleteItemFromLocalCart,
    handleAddToLocalCheckout,
    handleClearLocalCart,
    handleClearLocalCheckout,
    refetchCart,
    updateLocalCartRefreshCounter,
    categories: categories?.results,
    isCategoriesLoading,
    brands: brands?.results,
    isBrandsLoading,
    banners: banners?.results,
    isBannersLoading,
    skinTypes: skinTypes?.results,
    isSkinTypesLoading: isSkinTypesLoading,
    skinConcerns: skinConcern?.results,
    isSkinConcernsLoading: isSkinConcernLoading,
  };
};

export interface ContextDataType {
  userHeaderDetails: UserHeaderResponse;
  isWishlistLoading: boolean;
  wishlist: SingleWishlistItem[];
  isCartLoading: boolean;
  cartItems: SingleCartItem[];
  localCartItems: SingleLocalCartItem[];
  localCheckoutItems: any;
  handleAddToLocalCart: (product: SingleLocalCartItem) => void;
  handleUpdateSingleCartItem: (id: number | string, quantity: number) => void;
  handleDeleteItemFromLocalCart: (id: string | number) => void;
  handleAddToLocalCheckout: (data: any) => void;
  handleClearLocalCart: () => void;
  handleClearLocalCheckout: () => void;
  refetchCart: () => void;
  updateLocalCartRefreshCounter: () => void;
  categories: SingleCategory[];
  brands: SingleBrand[];
  isCategoriesLoading: boolean;
  isBrandsLoading: boolean;
  banners: SingleBanner[];
  isBannersLoading: boolean;
  skinTypes: SingleBrand[];
  isSkinTypesLoading: boolean;
  skinConcerns: SingleBrand[];
  isSkinConcernsLoading: boolean;
}
