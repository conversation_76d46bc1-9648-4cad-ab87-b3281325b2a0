import ShopLocations from "@/Components/ShopLocations/ShopLocations";
import { BASE_URL } from "@/environment/environment";
import ShopLayout from "@/Layouts/ShopLayout";
import { notFound } from "next/navigation";
import React from "react";

export interface Location {
  _id: string;
  branchName: string;
  address: string;
  imageUrl: string;
  mapLink: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

const StoreLocation = async () => {
  const data: any = await getData();

  return (
    <ShopLayout>
      <ShopLocations locations={data?.results} />
    </ShopLayout>
  );
};

async function getData() {
  const res = await fetch(`${BASE_URL}/content/shops`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
  }

  return res.json();
}

export default StoreLocation;
