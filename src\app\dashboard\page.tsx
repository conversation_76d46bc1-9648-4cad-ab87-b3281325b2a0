import DashboardPageOverview from "@/Components/DashboardComponents/DashboardPageOverview";
import UserDashboardLayout from "@/Layouts/UserDashboardLayout";
import { ROUTES } from "@/Routes";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Dashboard | INNI",
};

const DashboardPage = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;
  if (!accessToken) {
    redirect(ROUTES.LOG_IN(ROUTES.DASHBOARD.HOME));
  }
  return (
    <UserDashboardLayout>
      <DashboardPageOverview accessToken={accessToken ? accessToken : ""} />
    </UserDashboardLayout>
  );
};

export default DashboardPage;
