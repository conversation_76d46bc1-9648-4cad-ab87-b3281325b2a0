export interface UserDetailsResponse {
  success: boolean;
  message: string;
  result: UserDetails;
}

export interface UserHeaderResponse {
  success: boolean;
  message: string;
  userDetails: UserDetails;
  wishlistCount: number;
  cartCount: number;
  totalCartPrice: number;
}

export interface UserDetails {
  _id: string;
  name: string;
  email: string;
  profilePicture: string;
  role: string;
  mobileNumber: string;
  username: string;
  password: string;
  division: string;
  district: string;
  zipCode: string;
  deliveryAddress: string;
  totalCanceledOrderAmount: number;
  totalCanceledOrderCount: number;
  totalCompletedOrderAmount: number;
  totalCompletedOrderCount: number;
  totalOrderAmount: number;
  totalOrderCount: number;
  totalPendingOrderAmount: number;
  totalPendingOrderCount: number;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}


