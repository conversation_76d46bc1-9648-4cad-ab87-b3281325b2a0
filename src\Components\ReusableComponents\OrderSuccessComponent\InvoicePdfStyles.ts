import { StyleSheet } from "@react-pdf/renderer";

export const styles = StyleSheet.create({
  page: {
    backgroundColor: "white",
    padding: 20,
  },
  container: {
    // width: "1000px",
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "row",
  },
  section: {
    textAlign: "right",
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
    gap: "4px",
  },
  invoiceText: {
    fontSize: "16px",
    fontWeight: 800,
    fontFamily: "Open Sans",
  },
  invoiceNo: {
    fontSize: "12px",
    fontWeight: "bold",
    fontFamily: "Open Sans",
  },
  orderDate: {
    fontSize: "10px",
    fontWeight: "bold",
    fontFamily: "Open Sans",
  },
  barcode: {
    width: 60,
    height: 30,
    padding: 0,
    marginLeft: "-2px",
  },
  sectionRight: {
    textAlign: "right",
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-end",
    gap: "4px",
  },
  companyLogo: {
    width: 100,
    height: 30,
    marginBottom: 2,
  },
  companyAddress: {
    fontSize: "10px",
    fontFamily: "Open Sans",
    fontWeight: 800,
  },
  companyOtherTexts: {
    fontSize: "10px",
    fontFamily: "Open Sans",
    // fontWeight: 800,
  },
  bottomPartContainer: {
    display: "flex",
    alignItems: "flex-start",
    flexDirection: "row",
    gap: "4px",
    marginTop: "4px",
  },
  invoiceToContainer: {
    width: "170px",
    border: "1px solid black",
    padding: 4,
    display: "flex",
    flexDirection: "column",
    gap: "4px",
  },
  invoiceToText: {
    fontSize: "14px",
    fontWeight: "bold",
    fontFamily: "Open Sans",
  },
  customerName: {
    fontSize: "12px",
    fontWeight: "bold",
    maxWidth: "180px",
    textAlign: "left",
    fontFamily: "Open Sans",
  },
  phoneNumberContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: "4px",
  },
  phoneNumber: {
    fontSize: "12px",
    fontWeight: 800,
    fontFamily: "Open Sans",
  },
  addressContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "flex-start",
    gap: "4px",
  },
  address: {
    fontSize: "10px",
    maxWidth: "120px",
    textAlign: "left",
    fontFamily: "Open Sans",
    fontWeight: 800,
  },

  note: {
    fontSize: "8px",
  },
  productsTable: {
    // marginTop: "-10px",
  },

  tableHeader: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 4,
    border: "1px solid black",
    backgroundColor: "#dedcdc",
    flexDirection: "row",
  },
  numberTitle: {
    width: "10px",
    fontSize: "8px",
    fontWeight: "bold",
    fontFamily: "Open Sans",
  },
  nameTitle: {
    width: "280px",
    fontSize: "8px",
    fontWeight: "bold",
    fontFamily: "Open Sans",
  },
  productPrice: {
    width: "30px",
    fontSize: "8px",
    fontWeight: "bold",
    fontFamily: "Open Sans",
    textAlign: "center",
  },
  productQuantity: {
    width: "20px",
    fontSize: "8px",
    fontWeight: "bold",
    fontFamily: "Open Sans",
    textAlign: "center",
  },
  productTotal: {
    width: "30px",
    fontSize: "8px",
    fontWeight: "bold",
    fontFamily: "Open Sans",
    textAlign: "right",
  },
  productRow: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 4,
    borderBottom: "1px solid black",
    borderLeft: "1px solid black",
    borderRight: "1px solid black",
    flexDirection: "row",
  },
  productName: {
    width: "280px",
    fontSize: "8px",
  },
  duePart: {
    width: "220px",
    display: "flex",
    alignItems: "center",
    flexDirection: "column",
    justifyContent: "center",
    gap: "10px",
  },
  payableAmount: {
    fontSize: "8px",
    textAlign: "center",
    padding: "10px  10px 0px 10px",
  },
  dueOrPaid: {
    border: "1px solid black",
    padding: "4px 20px",
    fontSize: "14px",
    backgroundColor: "#dedcdc",
  },
  printed: {
    fontSize: "8px",
  },
  systemMessage: {
    fontSize: "6px",
    fontFamily: "Open Sans",
    fontWeight: "bold",
  },

  subTotal: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: "4px 20px 4px 4px",
    borderBottom: "1px solid black",
    borderLeft: "1px solid black",
    borderRight: "1px solid black",
    flexDirection: "row",
  },

  bottomBillingTextLeft: {
    width: "100px",
    fontSize: "10px",
    fontFamily: "Open Sans",
    fontWeight: 800,
  },
  bottomBillingTextRight: {
    fontSize: "10px",
    textAlign: "right",
    marginRight: "-18px",
    fontFamily: "Open Sans",
    fontWeight: 800,
  },

  flexAndGap: {
    display: "flex",
    alignItems: "flex-start",
    flexDirection: "row",
    gap: "2px",
  },

  text: {
    fontSize: "10px",
  },

  flexBetween: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    flexDirection: "row",
  },
});
