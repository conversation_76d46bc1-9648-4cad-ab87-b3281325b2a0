import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ROUTES } from "@/Routes";
import { getBlogsApi } from "@/services/blogServices";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import { FaChevronRight } from "react-icons/fa";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

const BlogAndOVideoSection = () => {
  const { api, getKey } = getBlogsApi();
  const { data, isLoading, refetch } = useQuery(getKey(), api, {
    refetchOnWindowFocus: false,
  });

  return (
    <>
      {!isLoading && data?.results?.length ? (
        <>
          <div className="my-2 flex items-center justify-between pe-2">
            <span className="text-xl font-bold">Korean Shop Articles</span>
            <Link href={ROUTES.BLOGS.HOME}>
              <span className="flex items-center gap-1">
                <span className="text-[#092143]">View All</span>{" "}
                <FaChevronRight className="text-[#092143]" />
              </span>
            </Link>
          </div>
          <div className="grid grid-cols-12 gap-4">
            {data?.results.map((sin: any) => (
              <Link
                href={ROUTES.BLOGS.VIEW(sin.id)}
                key={sin.id}
                className="col col-span-6 md:col-span-4 lg:col-span-3"
              >
                <div className="w-full overflow-hidden rounded-lg p-2">
                  <Image
                    className="h-[150px] w-full"
                    src={generateProductImage(sin.image)}
                    // src="https://i.ibb.co.com/xzwnXtn/image.png"
                    alt="img"
                    height={100}
                    width={100}
                  />
                  <div className="flex h-full flex-col justify-between">
                    <p>{new Date().toLocaleDateString()}</p>
                    <p>{sin.title}</p>
                    {/* <p className="text-slate-400">{sin.brief}</p> */}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </>
      ) : (
        ""
      )}
    </>
  );
};

export default BlogAndOVideoSection;

{
  /* <div className="grid grid-cols-12 gap-2">
      <div className="col col-span-12 md:col-span-9">
        <div>
          <Image
            src="https://i.ibb.co/MSQ31zS/image-2024-02-18-21-28-48.png"
            height={100}
            width={100}
            alt="Carousel Image 1"
            className="h-[280px] w-full"
          />
        </div>
        <div className="mt-2">
          <div className="my-2 flex items-center justify-between pe-2">
            <span className="text-xl font-bold">Video</span>
            <Link href={ROUTES.VIDEOS}>
              <span className="flex items-center gap-1">
                <span className="text-[#092143]">View All</span>{" "}
                <FaChevronRight className="text-[#092143]" />
              </span>
            </Link>
          </div>
          <div>
            <Swiper
              effect="coverflow"
              grabCursor={true}
              centeredSlides={false}
              slidesPerView={2}
              spaceBetween={6}
              coverflowEffect={{
                rotate: 0,
                stretch: 10,
                depth: 100,
                modifier: 1,
                slideShadows: true,
              }}
              pagination={{ clickable: true }}
              className="mySwiper"
              autoplay={{
                delay: 3000,
                disableOnInteraction: false,
                reverseDirection: false,
              }}
              loop={true}
              breakpoints={{
                1024: {
                  slidesPerView: 3,
                },
                768: {
                  slidesPerView: 2,
                },
              }}
              modules={[Autoplay]}
            >
              {[
                "https://www.youtube.com/embed/k6kNlL7NSjk?si=mFbLKtytMR6CduMh",
                "https://www.youtube.com/embed/O_qgaOUR3Fo?si=H-tIEmQtuePoq116",
                "https://www.youtube.com/embed/O_qgaOUR3Fo?si=ncN-jQ_oMwZxRI98",
                "https://www.youtube.com/embed/PYS3UZFPJWI?si=vjOga-5xkRSL9cgI",
                "https://www.youtube.com/embed/HhM0BYCHL00?si=2jHu5KFBsDkjD_WM",
              ].map((singleUrl: string) => (
                <SwiperSlide key={singleUrl}>
                  <iframe
                    src={singleUrl}
                    title="YouTube video player"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    className="h-[150px] w-full rounded-xl border-2 border-[#092143] md:h-[200px]"
                  />
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      </div>
      <div className="col col-span-12    pr-2 md:col-span-3 ">
        <div className="hidden h-[550px] overflow-y-auto  pr-2 md:col-span-3 md:block">
          <div className="my-2 flex items-center justify-between pe-2">
            <span className="text-xl font-bold">Blogs</span>
            <Link href={ROUTES.BLOGS.HOME}>
              <span className="flex items-center gap-1">
                <span className="text-[#092143]">View All</span>{" "}
                <FaChevronRight className="text-[#092143]" />
              </span>
            </Link>
          </div>
          <div className="flex flex-col gap-2">
            {BlogsData.map((sin: any) => (
              <Link href={ROUTES.BLOGS.VIEW(sin.id)} key={sin.id}>
                <div className="relative h-[155px] w-full overflow-hidden rounded-lg p-2 transition-all duration-700 ease-in-out hover:scale-95">
                  <Image
                    className="absolute left-0 top-0 h-full w-full opacity-50"
                    src={sin.image}
                    alt="img"
                    height={100}
                    width={100}
                  />
                  <div className="relative flex h-full flex-col justify-between">
                    <span>SKIN CARE</span>
                    <p>{sin.title}</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
        <div className="block w-full md:hidden">
          <div className="my-2 flex items-center justify-between pe-2">
            <span className="text-xl font-bold">Blogs</span>
            <Link href={ROUTES.BLOGS.HOME}>
              <span className="flex items-center gap-1">
                <span className="text-[#092143]">View All</span>{" "}
                <FaChevronRight className="text-[#092143]" />
              </span>
            </Link>
          </div>
          <Swiper
            effect="coverflow"
            grabCursor={true}
            centeredSlides={false}
            slidesPerView={2}
            spaceBetween={6}
            coverflowEffect={{
              rotate: 0,
              stretch: 10,
              depth: 100,
              modifier: 1,
              slideShadows: true,
            }}
            pagination={{ clickable: true }}
            className="mySwiper"
            autoplay={{
              delay: 3000,
              disableOnInteraction: false,
              reverseDirection: false,
            }}
            loop={true}
            breakpoints={{
              1024: {
                slidesPerView: 3,
              },
              768: {
                slidesPerView: 2,
              },
            }}
            modules={[Autoplay]}
          >
            {[1, 2, 3, 4, 5, 6, 7].map((sin: number) => (
              <SwiperSlide key={sin}>
                <Link href={ROUTES.BLOGS.VIEW(sin)} key={sin}>
                  <div
                    style={{
                      backgroundImage:
                        "linear-gradient(to right top, #092143, #b8905c, #cea96c, #e2c37c, #0C1E32)",
                    }}
                    className="h-[150px] w-full rounded-lg"
                  >
                    <p>Blog {sin}</p>
                  </div>
                </Link>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </div> */
}
