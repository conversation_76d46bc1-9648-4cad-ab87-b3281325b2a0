"use client";
import { ROUTES } from "@/Routes";
import { useQueryClient } from "@tanstack/react-query";
import { signIn } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";
import { FaRegEye, FaRegEyeSlash } from "react-icons/fa";

const LoginPageOverview = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoggingIn, setIsLoggingIn] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [error, setError] = useState("");
  const redirectUrl = searchParams.get("redirect") || "";

  const handleSubmit = async (e: any) => {
    setIsLoggingIn(true);
    e.preventDefault();
    try {
      const res = await signIn("credentials", {
        email: username,
        password: password,
        redirect: false,
      });
      queryClient.invalidateQueries();
      if (!res?.error) {
        await queryClient.invalidateQueries();
        setTimeout(() => {
          setIsLoggingIn(false);
          window.location.reload();
          router.push(redirectUrl ? redirectUrl : ROUTES.HOME);
        }, 2000);
      } else {
        setIsLoggingIn(false);
        window.location.reload();
      }
      setError(res?.error ? res.error : "");
    } catch (error) {
      setIsLoggingIn(false);
      toast.error("Error on log in");
    }
  };

  return (
    <div className="mt-4 flex h-[80vh] items-center justify-center pb-8">
      <div className="relative w-96 rounded-lg border border-primary bg-[#F48F4B0e] p-8 shadow-lg">
        <div className="absolute left-[50%] top-[-25px] h-16 w-16 translate-x-[-50%] rounded-full border-4 border-white">
          {/* Company Logo */}
          <Image
            height={100}
            width={100}
            src="https://cdn-icons-png.flaticon.com/512/295/295128.png" // Replace with the path to your company logo image
            alt="Company Logo"
            className="h-full w-full"
          />
        </div>
        <h1 className="mb-5 mt-2 text-center text-2xl font-semibold">Log in</h1>

        {/* Login Form */}
        <form className="space-y-4">
          <div>
            <input
              type="text"
              id="username"
              className="w-full rounded-lg border px-4 py-2 font-normal focus:ring focus:ring-blue-300"
              placeholder="Enter your username/phone/email"
              onChange={(e) => setUsername(e.target.value)}
            />
          </div>

          <div className="relative">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              className="w-full rounded-lg border px-4 py-2 font-normal focus:ring focus:ring-blue-300"
              placeholder="Enter your password"
              onChange={(e) => setPassword(e.target.value)}
            />
            <div className="absolute right-[10px] top-[12px]">
              <button
                type="button"
                className="flex items-center gap-2 text-[12px]"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <>
                    <FaRegEye className="text-lg text-gray-700" />
                  </>
                ) : (
                  <>
                    <FaRegEyeSlash className="text-lg text-gray-700" />
                  </>
                )}
              </button>
            </div>
          </div>
          <div></div>
          {error ? (
            <span
              className="text-sm font-normal text-red-500"
              style={{ color: "red" }}
            >
              {error}
            </span>
          ) : (
            ""
          )}

          <button
            disabled={isLoggingIn}
            type="submit"
            className="flex w-full items-center justify-center rounded-lg bg-secondary py-2 font-normal text-white hover:bg-orange-400"
            onClick={(e) => handleSubmit(e)}
          >
            {isLoggingIn ? (
              <div className="h-8 w-8 animate-spin rounded-full border-t-4 border-white" />
            ) : (
              "Login"
            )}
          </button>

          <div className="flex justify-between">
            <a
              href="#"
              className="text-sm font-normal text-primary hover:text-secondary hover:underline"
            >
              Forgot Password?
            </a>
            <Link
              href={ROUTES.REGISTRATION}
              className="text-sm font-normal text-primary hover:text-secondary hover:underline"
            >
              Create Account
            </Link>
          </div>
        </form>

        {/* Quick Login Options */}
        <div>
          <div className="my-4 flex items-center">
            <div className="flex-grow border-t border-secondary"></div>
            <span className="mx-3 text-sm font-normal text-primary">Or</span>
            <div className="flex-grow border-t border-secondary"></div>
          </div>
          <div className="mt-2 flex items-center justify-center gap-8">
            <button
              className=" flex items-center  gap-2  rounded-lg border border-[#092143] font-normal text-white"
              onClick={() => signIn("google")}
            >
              <div className="rounded-l-[6px] bg-primary p-2">
                <Image
                  height={100}
                  width={100}
                  src="https://i.ibb.co/JQr2Whx/download-removebg-preview.png"
                  alt=""
                  style={{ height: "20px", width: "20px" }}
                />
              </div>
              <p className="pr-2 text-sm text-black">Log In Using Google</p>
            </button>
            {/* <button
              className=" py-2 text-white  rounded-lg  font-normal border border-blue-500 p-4"
              onClick={() => signIn("facebook")}
            >
              <Image
                height={100}
                width={100}
                src="https://i.ibb.co/phYkSS0/facebook.png"
                alt=""
                style={{ height: "30px" }}
              />
            </button> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPageOverview;
