import RegistrationComponent from "@/Components/Authentication/RegistrationPage/RegistrationComponent";
import ShopLayout from "@/Layouts/ShopLayout";
import { ROUTES } from "@/Routes";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Registration",
};
const RegistrationPage = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;
  if (accessToken) {
    redirect(ROUTES.HOME);
  }
  return (
    <ShopLayout>
      <RegistrationComponent />
    </ShopLayout>
  );
};

export default RegistrationPage;
