import { SingleProductResponse } from "@/Types/productTypes";
import { BASE_URL } from "@/environment/environment";
import axios from "axios";

export const getLandingPageSingleProductDetailsApi = (id: string) => {
  return {
    api() {
      return axios
        .get<SingleProductResponse>(
          `${BASE_URL}/landing-page-product/single/${id}`,
        )
        .then(({ data }) => data);
    },
    getKey() {
      return [`getLandingPageSingleProductDetailsApi`, id];
    },
  };
};
