import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import {
  deleteModal,
  maximumQuantityModal,
  minimumQuantityModal,
} from "@/Components/utils/commonModal";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleLocalCartItem } from "@/Types/localCartTypes";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { BiTrash } from "react-icons/bi";
import { FaChevronRight } from "react-icons/fa";
interface Props {
  setOpenCartSlider: (e: any) => void;
}

const NoUserRightSideCartComponent = ({ setOpenCartSlider }: Props) => {
  const router = useRouter();
  const {
    localCartItems,
    handleAddToLocalCheckout,
    handleUpdateSingleCartItem,
    handleDeleteItemFromLocalCart,
  }: ContextDataType = useContext(LocalDataContext);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  useEffect(() => {
    let total = 0;
    if (localCartItems?.length) {
      localCartItems.map(
        (single: SingleLocalCartItem) =>
          (total += single.discountPrice * single.quantity),
      );
    }
    setTotalAmount(total);
    if (localCartItems?.length) {
      const data = {
        event: "view_cart",
        value: total,
        user_id: "",
        user_name: "",
        user_phone_number: "",
        user_email: "",
        user_zip: "",
        user_district: "",
        user_log_in_status: "not logged in",
        items: localCartItems
          ? localCartItems.map((single: SingleLocalCartItem) => {
              return {
                item_id: single?.id,
                item_name: single?.name,
                price: single?.discountPrice,
                item_brand: single?.brand,
                item_category: single?.productCategory,
                slug: single?.slug,
                sku: single?.id?.replace("GMK-", ""),
              };
            })
          : null,
      };
      gtmDataLayerDataPass(data);
    }
  }, [localCartItems]);

  const handleDeleteFromCart = (id: string) => {
    deleteModal("Cart", () => {
      handleDeleteItemFromLocalCart(id);
      toast.success("Item Deleted From Cart");
    });
  };

  const handleCheckout = () => {
    handleAddToLocalCheckout(localCartItems);
    toast.success("Added for checkout");
    setOpenCartSlider(false);
    router.push(ROUTES.CHECKOUT);
  };

  return (
    <div>
      <div className="h-[82vh] overflow-y-auto px-4">
        {localCartItems?.length > 0 ? (
          <>
            {localCartItems?.map((single: SingleLocalCartItem) => (
              <div key={single.id} className="mb-2">
                <div className="grid grid-cols-12 items-start  gap-2 rounded-t-lg bg-white p-2">
                  <div className="col-span-2">
                    <Image
                      height={100}
                      width={100}
                      src={generateProductImage(single.mainImageUrl)}
                      alt="image"
                      className="h-[60px] w-[60px] rounded"
                    />
                  </div>
                  <div className="col-span-10  items-start">
                    <div className="flex items-start justify-between gap-4">
                      <span className="text-xs md:text-sm">{single.name}</span>
                      <button
                        onClick={() => handleDeleteFromCart(single.id)}
                        className="hover:text-red-500"
                      >
                        <BiTrash className="cursor-pointer hover:text-red-500" />
                      </button>
                    </div>
                    <div className="mt-2 flex items-center justify-between">
                      <div className="flex items-end gap-2">
                        <span className="text-sm font-semibold">
                          ৳ {single.discountPrice}
                        </span>
                        <span className="text-xs font-normal line-through">
                          ৳ {single.price}
                        </span>
                      </div>
                      <div>
                        <div className="flex w-[100px] items-center justify-between rounded-lg border-2 border-primary md:w-[120px]">
                          <button
                            className="rounded-l-lg bg-gray-200 px-2 md:px-4"
                            onClick={() => {
                              if (single?.quantity > 1) {
                                handleUpdateSingleCartItem(
                                  single?.id,
                                  single?.quantity - 1,
                                );
                              } else {
                                minimumQuantityModal();
                              }
                            }}
                          >
                            -
                          </button>
                          <span className="mx-1 text-xs md:text-sm">
                            {single.quantity}
                          </span>
                          <button
                            className="rounded-r-lg bg-gray-200 px-2 md:px-4"
                            onClick={() => {
                              if (single.quantity < 5) {
                                handleUpdateSingleCartItem(
                                  single?.id,
                                  single?.quantity + 1,
                                );
                              } else {
                                maximumQuantityModal();
                              }
                            }}
                          >
                            +
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-1 w-full rounded-b-lg bg-white px-2 text-right">
                  <span className="text-xs font-semibold md:text-sm">
                    Subtotal : ৳ {single.discountPrice * single.quantity}
                  </span>
                </div>
              </div>
            ))}
          </>
        ) : (
          <span>No Items Found</span>
        )}
      </div>
      <div className="mt-4 flex w-full items-center justify-between bg-white px-4 py-2">
        <div>
          <p className="text-xs font-semibold md:text-sm">Cart Total</p>
          <p className="text-xs font-semibold text-[#092143] md:text-sm">
            ৳ {totalAmount}
          </p>
        </div>
        <div>
          <button
            className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white disabled:bg-gray-400"
            onClick={() => handleCheckout()}
            disabled={localCartItems?.length === 0}
          >
            <p className="text-xs md:text-sm">Proceed</p>
            <FaChevronRight />
          </button>
        </div>
      </div>
    </div>
  );
};

export default NoUserRightSideCartComponent;
