import { FaOpencart } from "react-icons/fa";

interface Props {
  handleClick: () => void; // Specify the type for handleClick
}

const OrderButtonTwo = ({ handleClick }: Props) => {
  return (
    <button
      className=" flex items-center rounded-full border-2 border-dashed border-[#092143] bg-white px-8 py-4 text-2xl font-bold text-[#092143] transition-all duration-200 hover:scale-105 sm:text-3xl"
      onClick={handleClick}
      type="button"
    >
      <FaOpencart />
      <span className="ml-3">অর্ডার করুন</span>
    </button>
  );
};

export default OrderButtonTwo;
