"use client";
import ProductsListSkeleton from "@/Components/ReusableComponents/Loaders/ProductListSkeleton";
import Modal from "@/Components/ReusableComponents/Modal/Modal";
import { NoProductsFound } from "@/Components/ReusableComponents/NoResultFound/NoResultFound";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { discountPercentage } from "@/Components/utils/PriceCalculator";
import { notLoggedInModal } from "@/Components/utils/commonModal";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleProductOfList } from "@/Types/productTypes";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { addToCartApi } from "@/services/cartServices";
import { addToWishList<PERSON><PERSON> } from "@/services/wishlistServices";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useContext, useState } from "react";
import { toast } from "react-hot-toast";
import { AiFillHeart, AiOutlineHeart } from "react-icons/ai";
import { IoCartOutline } from "react-icons/io5";
import AddToCartModal from "../AddToCartModal/AddToCartModal";

interface Props {
  productList?: SingleProductOfList[] | undefined;
  loading: boolean;
  accessToken: string;
  xxl: number;
  xl: number;
  lg: number;
  md: number;
  sm: number;
}
const ProductsListViewer = ({
  productList,
  loading,
  accessToken,
  xxl,
  xl,
  lg,
  md,
  sm,
}: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { wishlist, handleAddToLocalCart, userHeaderDetails }: ContextDataType =
    useContext(LocalDataContext);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [selectedProductInfo, setSelectedProductInfo] =
    useState<SingleProductOfList>();
  console.log(xxl);


  const { api: AddToWishlistApi } = addToWishListApi();

  const { mutateAsync: addToWishlistMutateAsync } = useMutation(
    AddToWishlistApi,
    {
      onSuccess: () => {
        queryClient.invalidateQueries();
      },
    },
  );

  const handleAddToWishlist = async (product: SingleProductOfList) => {
    const productInfo = {
      productId: product._id,
      ...product,
    };

    if (accessToken) {
      toast.promise(
        addToWishlistMutateAsync(productInfo),
        {
          success: "Product Added to Wishlist Successful",
          error: "Error Adding Product to Wishlist",
          loading: "Adding Product to Wishlist",
        },
        {
          id: "add-to-wishlist-product",
        },
      );
    } else {
      notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
    }
  };

  const { api: AddToCartApi } = addToCartApi();

  const { mutateAsync: addToCartMutateAsync } = useMutation(AddToCartApi, {
    onSuccess: () => {
      queryClient.invalidateQueries();
    },
  });

  const handleAddToCart = async (product: SingleProductOfList) => {
    const productInfo = {
      productId: product?._id || "",
      quantity: 1,
      price: product?.discountPrice,
    };

    const addToCartDataLayer = {
      event: "add_to_cart",
      value: product.discountPrice,
      user_log_in_status: userHeaderDetails?.userDetails?.name
        ? "logged in"
        : "not logged in",
      user_id: userHeaderDetails?.userDetails?._id ?? "",
      user_name: userHeaderDetails?.userDetails?.name ?? "",
      user_phone_number: userHeaderDetails?.userDetails?.mobileNumber ?? "",
      user_email: userHeaderDetails?.userDetails?.email ?? "",
      user_zip: userHeaderDetails?.userDetails?.zipCode ?? "",
      user_district: userHeaderDetails?.userDetails?.district ?? "",
      items: [
        {
          item_id: product.id,
          item_name: product.name,
          price: product.discountPrice,
          item_brand: product.brand,
          slug: product.slug,
          sku: product?.id?.replace("GMK-", ""),
          item_category: product.productCategory,
        },
      ],
    };
    gtmDataLayerDataPass(addToCartDataLayer);

    if (accessToken) {
      toast.promise(
        addToCartMutateAsync(productInfo),
        {
          success: "Product Added to cart Successful",
          error: "Error Adding Product to cart",
          loading: "Adding Product to cart",
        },
        {
          id: "add-to-cart-product",
        },
      );
    } else {
      handleAddToLocalCart({ ...product, quantity: 1 });
      toast.success("Product Added to cart Successful");
    }
  };

  return (
    <div>
      {!loading ? (
        <>
          {productList?.length ? (
            <div
              className={`grid gap-4 2xl:gap-3 2xl:grid-cols-5 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-3 sm:grid-cols-2 grid-cols-2 `}
            >
              {productList?.map((product: SingleProductOfList) => {
                const isProductInWishlist = wishlist?.some(
                  (wishlistProduct: any) =>
                    wishlistProduct.productId === product.id,
                );
                return (
                  <Link
                    key={product.id}
                    href={ROUTES.PRODUCTS.VIEW(product?.slug)}
                  >
                    <div className="shadow rounded-lg border border-[#dbdbdb]">
                      <div className="relative  rounded-t-lg border-gray-200">
                        <div className="m-3 h-[180px] sm:h-[272px] md:h-[206px] lg:h-[250px] xl:h-[290px] 2xl:h-[350px] bg-[#F3F4F7] px-2 py-4 rounded-lg shadow-[inset_0_4px_8px_rgba(0,0,0,0.1)]">
                          <Image
                            src={generateProductImage(product.mainImageUrl)}
                            // src="https://d2vvo9h1swbzur.cloudfront.net/products/Cosrx%20Advanced%20Snail%20Radiance%20Dual%20Essence%20%E2%80%93%2080ml.webp"
                            alt={product.name}
                            width={300}
                            height={300}
                            className="h-full w-full"
                          />
                        </div>
                        {discountPercentage(
                          product.price,
                          product.discountPrice,
                        ) > 0 ? (
                          <p className="absolute left-[18px] top-[7px] rounded-full bg-[#35AFA0] px-2 py-1 text-[10px] text-white">
                            -
                            <span className="pl-[2px] pr-[2px]">
                              {discountPercentage(
                                product.price,
                                product.discountPrice,
                              )}
                            </span>
                            %
                          </p>
                        ) : (
                          ""
                        )}

                        <div className="absolute right-[18px] top-[5px]">
                          <button
                            aria-label="wishlist"
                            className="rounded-lg bg-white p-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              handleAddToWishlist(product);
                            }}
                          >
                            {isProductInWishlist ? (
                              <AiFillHeart
                                className={`text-2xl ${isProductInWishlist
                                  ? "text-red-500"
                                  : "text-black"
                                  }`}
                              />
                            ) : (
                              <AiOutlineHeart
                                className={`text-2xl ${isProductInWishlist
                                  ? "text-red-500"
                                  : "text-black"
                                  }`}
                              />
                            )}
                          </button>
                        </div>
                      </div>
                      <div className="pb-2">
                        <div className="px-3 mt-2">
                          <p className="h-[3rem] overflow-hidden text-[14px] font-semibold text-black hover:cursor-pointer hover:underline md:h-10">
                            {product?.name}
                          </p>
                          <div className="pb-0 pt-1">
                            <div className="mt-4 flex items-center justify-between md:mt-0">
                              <h2 className="text-lg font-semibold text-secondary md:text-xl">
                                ৳{product.discountPrice}{" "}
                                {product.price !== product.discountPrice ? (
                                  <span className="text-xs text-slate-500 line-through md:text-sm">
                                    ৳{product.price}
                                  </span>
                                ) : (
                                  ""
                                )}
                              </h2>
                              {/* <p className="text-xs text-slate-500">
                              <span>{product?.totalSold}</span> Sold
                            </p> */}
                            </div>

                            {/* Add to Cart Button */}
                          </div>
                        </div>
                        <div className="mb-2 mt-2 flex gap-1 px-3 md:mt-4">
                          <button
                            className="rounded-full bg-[#092143] px-2 py-2 text-xs font-normal text-white delay-75 hover:bg-[#031e45] disabled:bg-slate-400 md:text-sm md:font-semibold"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              // setSelectedProductInfo(product);
                              // setShowModal(true);
                              handleAddToCart(product);
                            }}
                            aria-label="cart"
                            /* onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              sendGAEvent({
                                event: "buttonClicked",
                                value: "xyz",
                              });
                            }} */
                            disabled={!product.isAvailable}
                          >
                            <IoCartOutline className="text-lg" />
                          </button>
                          <button
                            className="w-full rounded bg-[#092143] py-2 text-xs font-normal text-white delay-75 hover:bg-[#031e45] disabled:bg-slate-400 md:text-sm md:font-semibold uppercase"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              // router.push(ROUTES.PRODUCTS.BUY(product?.slug));
                              setSelectedProductInfo(product);
                              setShowModal(true);
                            }}
                            aria-label="buy-now"
                            disabled={!product.isAvailable}
                          >
                            Buy Now
                          </button>
                        </div>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          ) : (
            <NoProductsFound />
          )}
        </>
      ) : (
        <ProductsListSkeleton />
      )}
      <Modal showModal={showModal} setShowModal={setShowModal}>
        <div className="flex w-full items-center justify-center">
          <AddToCartModal
            product={selectedProductInfo!}
            handleClose={() => setShowModal(false)}
            accessToken={accessToken}
          />
        </div>
      </Modal>
    </div>
  );
};

export default ProductsListViewer;
