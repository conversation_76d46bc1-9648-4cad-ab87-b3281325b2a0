import React from "react";
interface Props {
  totalQuantity: number;
  totalProductPrice: number;
  totalDiscount: number;
  deliveryCharge: number;
  formik: any;
  isOrderCreating: boolean;
}

const CartSummery = ({
  totalQuantity,
  totalProductPrice,
  totalDiscount,
  deliveryCharge,
  formik,
  isOrderCreating,
}: Props) => {
  return (
    <div className="h-1/2 rounded-lg border-2 p-4 shadow-lg md:w-1/3">
      <h2 className="mb-4 text-center text-2xl font-semibold">Cart Summary</h2>
      <table className="w-full ">
        <tbody>
          <tr style={{ backgroundColor: "#E1E3E7" }}>
            <td className="p-2 text-sm font-normal">Quantity</td>
            <td className="p-2 text-sm font-normal">:</td>
            <td className="p-2 text-sm font-normal">{totalQuantity}</td>
          </tr>
          <tr style={{ backgroundColor: "white" }}>
            <td className="p-2 text-sm font-normal">Product Price</td>
            <td className="p-2 text-sm font-normal">:</td>
            <td className="p-2 text-sm font-normal">৳ {totalProductPrice}</td>
          </tr>
          <tr style={{ backgroundColor: "#E1E3E7" }}>
            <td className="p-2 text-sm font-normal">Discount</td>
            <td className="p-2 text-sm font-normal">:</td>
            <td className="p-2 text-sm font-normal">৳ {totalDiscount}</td>
          </tr>
          <tr style={{ backgroundColor: "white" }}>
            <td className="p-2 text-sm font-normal">Subtotal Price</td>
            <td className="p-2 text-sm font-normal">:</td>
            <td className="p-2 text-sm font-normal">
              ৳ {totalProductPrice - totalDiscount}
            </td>
          </tr>
          <tr style={{ backgroundColor: "#E1E3E7" }}>
            <td className="p-2 text-sm font-normal">Delivery Charge</td>
            <td className="p-2 text-sm font-normal">:</td>
            <td className="p-2 text-sm font-normal">৳ {deliveryCharge}</td>
          </tr>
          <tr style={{ backgroundColor: "white" }}>
            <td className="p-2 text-sm font-normal">Total</td>
            <td className="p-2 text-sm font-normal">:</td>
            <td className="p-2 text-sm font-normal">
              ৳ {totalProductPrice - totalDiscount + deliveryCharge}
            </td>
          </tr>
        </tbody>
      </table>

      <div className="flex w-full items-center justify-center px-2">
        <button
          className="bg-primary mt-4 w-full rounded-lg px-4 py-2 font-normal text-white disabled:bg-gray-400"
          disabled={isOrderCreating ? true : false}
          onClick={() => formik.handleSubmit()}
          id="button two"
          type="button"
        >
          {isOrderCreating ? (
            <Spinner />
          ) : (
            <span>
              অর্ডার করুন (৳{totalProductPrice - totalDiscount + deliveryCharge}
              )
            </span>
          )}
        </button>
      </div>
    </div>
  );
};

export default CartSummery;

const Spinner = () => {
  return (
    <div
      className="flex items-center justify-center"
      style={{ height: "20px" }}
    >
      <div className="h-[20px] w-[20px] animate-spin rounded-full border-t-4 border-white"></div>
    </div>
  );
};
