"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleProductDetailsType } from "@/Types/productTypes";
import { addToCartApi } from "@/services/cartServices";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import { useContext } from "react";
import toast from "react-hot-toast";

interface Props {
  productDetails: SingleProductDetailsType;
  accessToken: string;
}
const LinkedProductPart = ({ productDetails, accessToken }: Props) => {
  const queryClient = useQueryClient();
  const { handleAddToLocalCart }: ContextDataType =
    useContext(LocalDataContext);

  const { api: AddToCartApi } = addToCartApi();

  const { mutateAsync: addToCartMutateAsync } = useMutation(AddToCartApi, {
    onSuccess: () => {
      handleAddSecondProductToCart();
    },
  });

  const handleAddCurrentProductToCart = async () => {
    const productInfo = {
      productId: productDetails?.id || "",
      quantity: 1,
      price: productDetails?.discountPrice,
    };

    if (accessToken) {
      addToCartMutateAsync(productInfo);
    } else {
      const data = {
        id: productDetails?.id,
        name: productDetails?.name,
        size: productDetails?.size,
        price: productDetails?.price,
        discountPrice: productDetails?.discountPrice,
        deal: productDetails?.deal,
        mainImageUrl: productDetails?.mainImageUrl,
        rating: productDetails?.rating,
        brand: productDetails?.brand,
        isAvailable: productDetails?.isAvailable,
        quantity: 1,
      };
      handleAddToLocalCart(data);
      handleAddSecondProductToCart();
    }
  };

  const { mutateAsync: addToCartSecondMutateAsync } = useMutation(
    AddToCartApi,
    {
      onSuccess: () => {
        queryClient.invalidateQueries();
      },
    },
  );
  const handleAddSecondProductToCart = async () => {
    const productInfo = {
      productId: productDetails?.linkedProduct?.id || "",
      quantity: 1,
      price: productDetails?.linkedProduct?.discountPrice,
    };

    if (accessToken) {
      toast.promise(
        addToCartSecondMutateAsync(productInfo),
        {
          success: "Product Added to cart Successful",
          error: "Error Adding Product to cart",
          loading: "Adding Product to cart",
        },
        {
          id: "add-to-cart-product",
        },
      );
    } else {
      const linkedProductDetails = {
        id: productDetails?.linkedProduct?.id ?? "",
        name: productDetails?.linkedProduct?.name ?? "",
        size: productDetails?.linkedProduct?.size ?? "",
        price: productDetails?.linkedProduct?.price ?? 0,
        discountPrice: productDetails?.linkedProduct?.discountPrice ?? 0,
        deal: productDetails?.linkedProduct?.deal ?? "",
        mainImageUrl: productDetails?.linkedProduct?.mainImageUrl ?? "",
        rating: productDetails?.linkedProduct?.rating ?? 0,
        brand: productDetails?.linkedProduct?.brand ?? "",
        isAvailable: productDetails?.linkedProduct?.isAvailable ?? false,
        quantity: 1,
      };
      handleAddToLocalCart(linkedProductDetails);
      toast.success("Product Added to cart Successful");
    }
  };
  return (
    <div className="rounded-xl border border-primary px-4 py-4">
      {productDetails?.linkedProduct ? (
        <>
          <h2 className="pb-4 text-xl font-bold">Frequently bought together</h2>
          <div className="mb-2 flex flex-wrap items-center gap-8">
            <div className="flex items-center gap-6">
              <div>
                <Image
                  src={generateProductImage(productDetails?.mainImageUrl)}
                  alt="prod image"
                  height={100}
                  width={100}
                  className="h-[100px]"
                />
              </div>
              <div className="text-3xl font-semibold">+</div>
              <div>
                <Link
                  href={ROUTES.PRODUCTS.VIEW(
                    productDetails?.linkedProduct?.slug!,
                  )}
                >
                  <Image
                    src={generateProductImage(
                      productDetails?.linkedProduct?.mainImageUrl!,
                    )}
                    alt="prod image"
                    height={100}
                    width={100}
                    className="h-[100px]"
                  />
                </Link>
              </div>
            </div>
            <div>
              <h2>
                Total Price:
                <span className="ml-2 font-bold text-primary">
                  ৳
                  {Number(productDetails?.discountPrice) +
                    Number(productDetails?.linkedProduct?.discountPrice)}
                </span>
              </h2>
              <button
                className="mt-2 rounded-lg bg-primary px-4 py-2 text-white"
                onClick={() => handleAddCurrentProductToCart()}
              >
                Add Both To Cart
              </button>
            </div>
          </div>
          <div>
            <div>
              <span>1. {productDetails?.name}</span>
              <span className="ml-4 font-bold text-primary">
                ৳{productDetails?.discountPrice}
              </span>
              {productDetails?.price > productDetails?.discountPrice ? (
                <span className="ml-4 line-through">
                  ৳{productDetails?.price}
                </span>
              ) : (
                ""
              )}
            </div>
            <Link
              href={ROUTES.PRODUCTS.VIEW(productDetails?.linkedProduct?.slug!)}
              className="cursor-pointer hover:text-[#092143]"
            >
              <span>2. {productDetails?.linkedProduct?.name}</span>
              <span className="ml-4 font-bold text-primary">
                ৳{productDetails?.linkedProduct?.discountPrice}
              </span>
              <span className="ml-4 line-through">
                ৳{productDetails?.linkedProduct?.price}
              </span>
            </Link>
          </div>
        </>
      ) : (
        <div
          className="flex items-center justify-center"
          style={{ height: "30vh" }}
        >
          <div className="h-16 w-16 animate-spin rounded-full border-t-4 border-blue-500"></div>
        </div>
      )}
    </div>
  );
};

export default LinkedProductPart;
