import { SingleCategory } from "@/services/bannerBrandCategoryServices";
import { SingleBanner } from "@/services/bannerServices";
import { SingleProductOfList } from "@/Types/productTypes";

export interface HomePageContentResponse {
  status: boolean;
  message: string;
  results: Results;
}

export interface Results {
  banners: SingleBanner[];
  homeProducts: SingleProductOfList[];
  newArrivalProducts: SingleProductOfList[];
  // bestSellerProducts: SingleProductOfList[];
  offerProducts: SingleProductOfList[];
  categories: SingleCategory[];
}
