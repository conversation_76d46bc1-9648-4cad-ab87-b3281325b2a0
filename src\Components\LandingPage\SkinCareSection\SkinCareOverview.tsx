import Link from "next/link";

const categories = [
  { label: "Body Lotion", href: "#" },
  { label: "Face Cream", href: "#" },
  { label: "Sunscreen", href: "#" },
  { label: "Serum", href: "#" },
  { label: "Cleanser", href: "#" },
  { label: "Moisturizer", href: "#" },
  { label: "Toner", href: "#" },
  { label: "Eye Cream", href: "#" },
  { label: "Scrub", href: "#" },
  { label: "Mask", href: "#" },
  { label: "Night Cream", href: "#" },
  { label: "Acne Treatment", href: "#" },
  { label: "Lip Balm", href: "#" },
  { label: "Hand Cream", href: "#" },
];

const SkinCareOverview = () => {
  return (
    <div
      className="bg-cover bg-center bg-no-repeat p-8 rounded-md"
      style={{ backgroundImage: "url('https://i.ibb.co/JwdfCVs8/skin-care-bg.png')" }}
    >
      <div className="w-full md:w-[65%]">
        <h3 className="text-2xl font-bold text-gray-800 uppercase mb-3">Skin Care</h3>
        <div className="flex flex-wrap gap-2">
          {categories.map((item, index) => (
            <Link key={index} href={item.href} className="pt-2 text-sm text-gray-500">
              <span className="border-r border-gray-500 pr-2">{item.label}</span>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SkinCareOverview;
