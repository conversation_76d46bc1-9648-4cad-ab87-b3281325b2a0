import ScrollRevealWrapper from "@/Components/ReusableComponents/ScrollRevealWrapper/ScrollRevealWrapper";
import { Option, skinConcernsList } from "@/Components/utils/filterOptionData";
import { ROUTES } from "@/Routes";
import Image from "next/image";
import Link from "next/link";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

const SkinConcern = () => {
  return (
    <ScrollRevealWrapper>
      <Swiper
        effect="coverflow"
        grabCursor={true}
        centeredSlides={false}
        slidesPerView={4}
        spaceBetween={6}
        coverflowEffect={{
          rotate: 0,
          stretch: 10,
          depth: 100,
          modifier: 1,
          slideShadows: true,
        }}
        pagination={{ clickable: true }}
        className="mySwiper"
        autoplay={{
          delay: 3000,
          disableOnInteraction: false,
          reverseDirection: false,
        }}
        loop={true}
        breakpoints={{
          1024: {
            slidesPerView: 7,
          },
          768: {
            slidesPerView: 3,
          },
        }}
        modules={[Autoplay]}
      >
        {skinConcernsList.map((card: Option) => (
          <SwiperSlide
            key={card.label}
            style={{
              display: card.label.toLowerCase() === "none" ? "none" : "",
            }}
          >
            <Link href={ROUTES.PRODUCTS.SKIN_CONCERN(card.value)}>
              <div className="flex flex-col items-center justify-center border rounded-lg p-2  cursor-pointer  shadow-lg">
                <Image
                  height={100}
                  width={100}
                  src={
                    card?.image ??
                    "https://t4.ftcdn.net/jpg/04/73/25/49/360_F_473254957_bxG9yf4ly7OBO5I0O5KABlN930GwaMQz.jpg"
                  }
                  alt={card.label}
                  className="w-[50px] h-[50px] md:w-[100px] md:h-[100px] object-cover rounded-lg mb-4 transition-transform transform hover:scale-110"
                />
                <h3 className="text-[10px] md:text-[14px] font-semibold text-center h-[30px] overflow-ellipsis">
                  {card.label}
                </h3>
              </div>
            </Link>
          </SwiperSlide>
        ))}
      </Swiper>
    </ScrollRevealWrapper>
  );
};

export default SkinConcern;
