"use client";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleWishlistItem } from "@/Types/wishlistTypes";
import { removeFromWishlist } from "@/services/wishlistServices";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useContext } from "react";
import { toast } from "react-hot-toast";
import { NoWishlistFound } from "../ReusableComponents/NoResultFound/NoResultFound";
import SpinnerLoader from "../ReusableComponents/SpinnerLoader/SpinnerLoader";
import TitleViewer from "../ReusableComponents/TitleViewer/TitleViewer";
import { deleteModal, notLoggedInModal } from "../utils/commonModal";
import { generateProductImage } from "../utils/GenerateProductImage";
import { addToCartApi } from "@/services/cartServices";

interface Props {
  accessToken: string;
}
const WishlistPageOverview = ({ accessToken }: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { wishlist, isWishlistLoading }: ContextDataType =
    useContext(LocalDataContext);

  const { api: AddToCartApi } = addToCartApi();

  const { mutateAsync: addToCartMutateAsync } = useMutation(AddToCartApi, {
    onSuccess: () => {
      queryClient.invalidateQueries();
    },
  });

  const handleAddToCart = async (id: string) => {
    const productInfo = {
      productId: id || "",
      quantity: 1,
    };

    if (accessToken) {
      toast.promise(
        addToCartMutateAsync(productInfo),
        {
          success: "Product Added to cart Successful",
          error: "Error Adding Product to cart",
          loading: "Adding Product to cart",
        },
        {
          id: "add-to-cart-product",
        },
      );
    }
  };

  const { api: DeleteFromWishlistApi } = removeFromWishlist();

  const { mutateAsync: removeFromWishlistMutateAsync } = useMutation(
    DeleteFromWishlistApi,
    {
      onSuccess: () => {
        queryClient.invalidateQueries();
      },
    },
  );

  const handleDeleteFromWishlist = (id: string) => {
    if (accessToken) {
      deleteModal("wishlist", () =>
        toast.promise(
          removeFromWishlistMutateAsync(id),
          {
            success: "Product removed from wishlist Successful",
            error: "Error removing Product from wishlist",
            loading: "Removing Product from wishlist",
          },
          {
            id: "remove-from-checkout",
          },
        ),
      );
    } else {
      notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
    }
  };
  return (
    <div className="container mx-auto py-4 md:py-8">
      <TitleViewer titleTwo="Wishlist" titleOne="" seeAllButton={false} />
      {!isWishlistLoading ? (
        <>
          {wishlist?.length ? (
            <div className="grid  grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2">
              {wishlist?.map((product: SingleWishlistItem) => (
                <div
                  key={product._id}
                  className="flex items-center gap-4 rounded-xl border bg-white p-2 shadow-lg md:p-4"
                >
                  <div className="w-1/2 md:w-1/2">
                    <Image
                      src={generateProductImage(
                        product?.productId?.mainImageUrl,
                      )}
                      alt={product?.productId?.name}
                      className="h-32 rounded-md  object-fill md:h-40"
                      height={100}
                      width={100}
                      style={{ width: "100%" }}
                    />
                  </div>
                  <div className="w-full md:w-full">
                    <h3 className="mb-2 text-xs font-semibold md:text-lg">
                      {product?.productId?.name}
                    </h3>
                    <div className="my-3 flex gap-4">
                      <h2 className="text-sm font-semibold md:text-2xl">
                        Price :
                      </h2>
                      <h2 className="text-sm font-semibold md:text-2xl">
                        ৳ {product?.productId?.discountPrice}{" "}
                        {product?.productId?.price !==
                        product?.productId?.discountPrice ? (
                          <span className="text-xs text-slate-500 line-through md:text-sm">
                            ৳{product?.productId?.price}
                          </span>
                        ) : (
                          ""
                        )}
                      </h2>
                    </div>
                    <button
                      className="mr-2 rounded-md bg-primary px-4 py-2 text-sm font-normal text-white"
                      onClick={() => handleAddToCart(product?.productId?._id)}
                    >
                      Add to Cart
                    </button>
                    <button
                      className="rounded-md bg-red-400 px-4 py-2 text-sm font-normal text-white"
                      onClick={() => handleDeleteFromWishlist(product._id)}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <NoWishlistFound />
          )}
        </>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
};

export default WishlistPageOverview;
