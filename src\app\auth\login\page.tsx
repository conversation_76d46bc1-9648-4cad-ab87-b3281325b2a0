import LoginPageOverview from "@/Components/Authentication/LoginPageComponents/LoginPageOverview";
import ShopLayout from "@/Layouts/ShopLayout";
import { ROUTES } from "@/Routes";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import React from "react";

export const metadata: Metadata = {
  title: "Login",
};
const LoginPage: React.FC = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;
  if (accessToken) {
    redirect(ROUTES.HOME);
  }
  return (
    <ShopLayout>
      <LoginPageOverview />
    </ShopLayout>
  );
};

export default LoginPage;
