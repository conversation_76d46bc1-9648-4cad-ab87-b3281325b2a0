export type TrackingResponse = [Root2, Root3, Root4, Root5, TrackingDetails[]];

export interface Root2 {
  created_at: string;
  id: number;
}

export interface Root3 {
  b_name: string;
}

export interface Root4 {
  name: string;
  phone: string;
}

export interface Root5 {
  name: string;
  phone: string;
}

export interface TrackingDetails {
  id: number;
  consignment_id: number;
  sender_id?: number;
  tracking_type: number;
  icon: any;
  text: string;
  user_type: any;
  user_id: any;
  deliveryman_id?: number;
  admin_id?: number;
  updated_by: any;
  confirm_status: number;
  status: number;
  hide_sender: number;
  hide_rec: number;
  created_at: string;
  updated_at: string;
  admin?: Admin;
  deliveryman?: Deliveryman;
}

export interface Admin {
  id: number;
}

export interface Deliveryman {
  id: number;
  name: string;
  email: any;
  username: string;
  type: number;
  pending_limit: any;
  pending_capacity: any;
  cancel_disable: any;
  phone: string;
  alt_phone: any;
  address: string;
  nid_number: string;
  latitude?: string;
  longitude?: string;
  position_updated_at?: string;
  hub_id: number;
  secondary_hub_id?: number;
  status: string;
  created_at: string;
  updated_at: string;
}
