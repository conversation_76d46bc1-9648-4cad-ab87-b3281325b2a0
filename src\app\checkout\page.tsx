import CheckoutPageOverview from "@/Components/CheckoutPageComponents/CheckoutPageOverview";
import NoUserCheckoutOverview from "@/Components/NoUserComponents/NoUserCheckoutComponents/NoUserCheckoutOverview";
import ShopLayout from "@/Layouts/ShopLayout";
import { Metadata } from "next";
import { cookies } from "next/headers";

export const metadata: Metadata = {
  title: "Checkout | INNI",
};

const CheckoutPage = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;

  return (
    <ShopLayout>
      {accessToken ? (
        <CheckoutPageOverview accessToken={accessToken ? accessToken : ""} />
      ) : (
        <NoUserCheckoutOverview />
      )}
    </ShopLayout>
  );
};

export default CheckoutPage;
