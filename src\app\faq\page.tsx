import React from "react";
import TitleViewer from "@/Components/ReusableComponents/TitleViewer/TitleViewer";
import { Metadata } from "next";
import ShopLayout from "@/Layouts/ShopLayout";
import FaqItem from "@/Components/FooterComponents/FaqItem";

export const metadata: Metadata = {
  title: "FAQ | INNI",
};
const FaqPage = () => {
  return (
    <ShopLayout>
      <div className="container mx-auto mt-8">
        <TitleViewer
          titleOne="Frequently Asked Question"
          titleTwo=""
          seeAllButton={false}
        />
        <div className="space-y-4">
          <FaqItem
            question="What payment methods do you accept?"
            answer="We accept credit cards, PayPal, and bank transfers."
          />
          <FaqItem
            question="How long does shipping take?"
            answer="Shipping usually takes 3-5 business days within the country."
          />
          <FaqItem
            question="Do you offer international shipping?"
            answer="Yes, we offer international shipping to selected countries."
          />
          {/* Add more FAQ items as needed */}
        </div>
      </div>
    </ShopLayout>
  );
};

export default FaqPage;
