"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { LandingPageProductDetails } from "@/Types/landingPageProductType";
import { convertToBanglaNumber } from "@/utils/EnglishToBanglaConvert";
import Image from "next/image";
import { FaCheckSquare, FaStar } from "react-icons/fa";
import { VscDebugBreakpointLogUnverified } from "react-icons/vsc";
import Iframe from "react-iframe";
import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import { A11y, Autoplay, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import logo from "../../../../src/dummyImages/logo.png";
import Accordion from "../Accordion";
import DirectOrderForm from "../DirectOrderForm";
import { OrderButtonVersionFour } from "../LangingPageDesignThree/CommonOrderNowButton";

interface Props {
  productDetails: LandingPageProductDetails;
}

const LandingPageFiveOverview = ({ productDetails }: Props) => {
  const handleScrollToOrderForm = () => {
    const orderFormDiv = document?.getElementById("order-form");
    if (orderFormDiv) {
      orderFormDiv?.scrollIntoView({ behavior: "smooth" });
    }
  };
  return (
    <div className="landing-page-two overflow-hidden">
      <section className="hero-section bg-gradient-to-t from-[#EEFFF4] to-[#6EDDBF] pb-16">
        <div className="flex justify-center">
          <div className="mt-[-50px] rounded-[100px] bg-white px-10 pt-10">
            <div className="flex justify-center pb-2 pt-4">
              <Image src={logo} alt="logo" width={200} height={100} />
            </div>
          </div>
        </div>
        <div className="container mx-auto px-4">
          <div className="flex justify-center">
            <div className="max-w-full sm:max-w-6xl">
              <h1 className="mt-8 px-0 text-center text-3xl font-bold leading-[1.4em] text-[#A2449A] sm:px-20 sm:text-5xl sm:leading-[1.5em]">
                {productDetails?.title}
              </h1>
              <div className="mt-3 text-center text-xl sm:text-2xl">
                <div
                  className="dangerouslyHtml"
                  dangerouslySetInnerHTML={{
                    __html: productDetails?.punchLine
                      ? productDetails?.punchLine
                      : "<p>No answer</p>",
                  }}
                />{" "}
                মাএ{" "}
                <span className="text-gray-600 line-through">
                  {convertToBanglaNumber(productDetails?.productDetails?.price)}
                </span>{" "}
                <span className="text-3xl font-semibold text-white">
                  {convertToBanglaNumber(
                    productDetails?.productDetails?.discountPrice,
                  )}
                </span>{" "}
                টাকায়
              </div>
              <div className="mt-8 flex justify-center">
                <OrderButtonVersionFour handleClick={handleScrollToOrderForm} />
              </div>
              <div className="main-video mt-8">
                <div className="mx-auto max-w-full rounded-2xl bg-[#A2449A] p-2 sm:max-w-6xl">
                  {productDetails?.youtubeUrl ? (
                    <Iframe
                      className="md:min-h-96 aspect-video w-full self-stretch rounded-lg"
                      url={productDetails?.youtubeUrl}
                      // frameBorder="0"
                      title="Product Overview Video"
                      aria-hidden="true"
                    />
                  ) : (
                    <div>
                      <div className="h-[450px] max-w-6xl rounded-xl border-2 border-[#A2449A] bg-[#A2449A] p-2 shadow-2xl md:h-[550px]">
                        <video
                          src={
                            productDetails?.video
                              ? generateProductImage(productDetails?.video)
                              : "https://beautysiaa.com/wp-content/uploads/2023/11/bb-cream-jahsbf.mp4"
                          }
                          className="h-full w-full rounded-lg"
                          controls
                          autoPlay
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="points mt-8">
                <div className="flex items-center justify-center">
                  <div className="max-w-full sm:max-w-3xl">
                    <ul className="ml-0 flex list-none flex-col items-start space-y-4 sm:items-center">
                      <li className="flex items-center gap-2 text-xl sm:text-2xl">
                        <VscDebugBreakpointLogUnverified className="text-[#A2449A]" />
                        জুনিয়র মনানিক্স‌ ০৬-৫ বছর‌ বয়সী শিশুদের‌
                      </li>
                      <li className="flex items-center gap-2 text-xl sm:text-2xl">
                        <VscDebugBreakpointLogUnverified className="text-[#A2449A]" />
                        মনানিক্স‌ মাল্টিভিটামিন ৫- ১২ বছর‌ বয়সী শিশুদের‌
                      </li>
                      <li className="flex items-center gap-2 text-xl sm:text-2xl">
                        <VscDebugBreakpointLogUnverified className="text-[#A2449A]" />
                        বাংলাদেশ ঔষধ প্রসাশন অধিদপ্তর অনুমোদিত ‌
                      </li>
                      <li className="flex items-center gap-2 text-xl sm:text-2xl">
                        <VscDebugBreakpointLogUnverified className="text-[#A2449A]" />
                        ISO সনদ প্রাপ্ত ‌
                      </li>
                      <li className="flex items-center gap-2 text-xl sm:text-2xl">
                        <VscDebugBreakpointLogUnverified className="text-[#A2449A]" />
                        ১০০% অথেন্টিক ‌
                      </li>
                      <li className="flex items-center gap-2 text-xl sm:text-2xl">
                        <VscDebugBreakpointLogUnverified className="text-[#A2449A]" />
                        সম্পূর্ণ ক্যাশ অন ডেলিভারি
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="des mt-10">
                <div className="flex items-center justify-center">
                  <div className="max-w-full sm:max-w-5xl">
                    <p className="text-center text-xl italic sm:text-2xl">
                      প্রোডাক্টটি হাতে পাওয়ার পরে অবশ্যই আমাদের কন্টাক্ট
                      নাম্বারে আমাদের সাথে যোগাযোগ করে পুষ্টিবিদের সাথে কথা বলে
                      খাবার নিয়ম টা জেনে নিবেন যাতে আপনার শিশুর বেড়ে ওঠার
                      জার্নিটা আরো সহজ এবং সুন্দর হয়।
                    </p>
                    <div className="mt-8 flex justify-center">
                      <OrderButtonVersionFour
                        handleClick={handleScrollToOrderForm}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="bg-[#F8F6F8] py-14">
        <div className="container mx-auto px-4">
          <div className="flex justify-center">
            <div className="max-w-full sm:max-w-6xl">
              <h1 className="mb-10 text-center text-3xl font-bold text-gray-800 sm:text-4xl">
                মনানিক্স‌ মাল্টিভিটামিন এর‌ উপাকারিতা
              </h1>
              <ul className="ml-0">
                <li className="flex items-center gap-2 border-b border-dashed border-gray-800 pb-3 text-xl font-semibold sm:text-2xl">
                  <FaCheckSquare className="flex-shrink-0 text-[#1C4B13]" />
                  <span>
                    শিশুর‌ ক্ষুধা ও খাবারে‌ রুচি বাড়ায় রোগ প্রতিরোধ ক্ষমতা
                    বৃদ্ধি করে।
                  </span>
                </li>
                <li className="flex items-center gap-2 border-b border-dashed border-gray-800 pb-3 text-xl font-semibold sm:text-2xl">
                  <FaCheckSquare className="flex-shrink-0 text-[#1C4B13]" />
                  <span>
                    মানানিক্স‌ মাল্টিভিটামিন শিশুর‌ সুস্বাস্থ্য গঠনে কার্যকরী
                    ভূমিকা রাখে‌।
                  </span>
                </li>
                <li className="flex items-center gap-2 border-b border-dashed border-gray-800 pb-3 text-xl font-semibold sm:text-2xl">
                  <FaCheckSquare className="flex-shrink-0 text-[#1C4B13]" />
                  <span>
                    মনানিক্স মাল্টিভিটামিন শিশুর শরীরে সকল ধরনের ভিটামিনের ঘাটতি
                    পূরণ করে।
                  </span>
                </li>
                <li className="flex items-center gap-2 border-b border-dashed border-gray-800 pb-3 text-xl font-semibold sm:text-2xl">
                  <FaCheckSquare className="flex-shrink-0 text-[#1C4B13]" />
                  <span>
                    শিশুর রক্তস্বল্পতা রোধ‌ করে শিশুর‌ শারীরিক ও মানসিক বিকাশ
                    হয়।
                  </span>
                </li>
                <li className="flex items-center gap-2 border-b border-dashed border-gray-800 pb-3 text-xl font-semibold sm:text-2xl">
                  <FaCheckSquare className="flex-shrink-0 text-[#1C4B13]" />
                  <span>
                    শিশুর রক্তস্বল্পতা রোধ‌ করে শিশুর‌ শারীরিক ও মানসিক বিকাশ
                    হয়।
                  </span>
                </li>
                <li className="flex items-center gap-2 border-b border-dashed border-gray-800 pb-3 text-xl font-semibold sm:text-2xl">
                  <FaCheckSquare className="flex-shrink-0 text-[#1C4B13]" />
                  <span>সন্তানের বুদ্ধি ও মেধাবিকাশে মনানিক্স অন্যতম।</span>
                </li>
              </ul>
              <div className="mt-8 flex justify-center">
                <OrderButtonVersionFour handleClick={handleScrollToOrderForm} />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* {productDetails?.sections?.map((section: WhyBest, index: number) => {
                return index % 2 === 0 ? (
                    <section className="why-buy mt-14 sm:mt-16 md:mt-20 xl:mt-24">
                        <div className="container mx-auto px-4">
                            <div className="rounded-lg bg-[#FA0566] py-4">
                                <h1 className="text-center text-3xl font-bold text-white sm:text-4xl">
                                    {section?.title}
                                </h1>
                            </div>
                            <div className="mt-10 grid grid-cols-1 items-center gap-8 sm:grid-cols-2 xl:grid-cols-3">
                                <div className="col-1 space-y-4 sm:space-y-7">
                                    {section?.options
                                        ?.slice(0, Math.ceil(section?.options?.length / 2))
                                        .map((single: string) => (
                                            <div
                                                className="usefullnessCard flex items-center"
                                                key={single}
                                            >
                                                <div className="icon rounded-full bg-[#FA0566] p-2">
                                                    <GiStarShuriken className="text-3xl text-white" />
                                                </div>
                                                <div className="card-text ml-3">
                                                    <h3>{single}</h3>
                                                </div>
                                            </div>
                                        ))}
                                </div>
                                <div className="col-2 hidden xl:block">
                                    <div className="w-full relative pb-[100%] overflow-hidden border-2 border-[#FA0566] rounded-md">
                                        <Image
                                            src={generateProductImage(section?.image)}
                                            // src={'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQwN8U653OvsAZLlAPY_kXfCufC4kZTJvkv6Q&s'}
                                            alt="product"
                                            width={570}
                                            height={570}
                                            className="max-w-full absolute top-0 bottom-0 left-0 right-0 m-auto rounded-md object-cover"
                                        />
                                    </div>
                                </div>
                                <div className="col-3 rtl-md space-y-4 sm:space-y-7">
                                    {section?.options
                                        ?.slice(
                                            Math.ceil(section?.options?.length / 2),
                                            Number(section?.options?.length),
                                        )
                                        .map((single: string) => (
                                            <div
                                                className="usefullnessCard flex items-center"
                                                key={single}
                                            >
                                                <div className="icon rounded-full bg-[#FA0566] p-2">
                                                    <GiStarShuriken className="text-3xl text-white" />
                                                </div>
                                                <div className="card-text ml-3">
                                                    <h3>{single}</h3>
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </div>
                            <div className="mt-8 flex justify-center">
                                <OrderButtonVersionFour handleClick={handleScrollToOrderForm} />
                            </div>
                        </div>
                    </section>
                ) : (
                    <section className="how-to-use mt-14 sm:mt-16 md:mt-20 xl:mt-24">
                        <div className="container mx-auto px-4">
                            <div className="grid grid-cols-1 lg:grid-cols-2">
                                <div className="img order-2 lg:order-1">
                                    <Image
                                        src={generateProductImage(section?.image)}
                                        alt="logo"
                                        width={100}
                                        height={100}
                                        className="h-auto max-h-[600px] w-full rounded-tr-[40px] rounded-br-[40px] rounded-bl-[40px] lg:rounded-bl-md rounded-tl-[40px] lg:rounded-tl-md border-2 border-[#FA0566]"
                                    />
                                </div>
                                <div className="text bg-gradient-to-b from-[#FA0566] to-[#E8035F] my-0 lg:my-[60px] rounded-bl-[40px] lg:rounded-bl-none rounded-tl-[40px] lg:rounded-tl-none rounded-tr-[40px] rounded-br-[40px] order-1 lg:order-2">
                                    <h1 className="text-center text-2xl font-bold text-white sm:text-3xl mt-3">{section?.title}</h1>
                                    <div className="space-y-3 pt-5 xl:space-y-5 xl:pt-10 pl-5">
                                        {section?.options?.map(
                                            (single: string, index: number) => (
                                                <div
                                                    className="step-card flex items-center"
                                                    key={single}
                                                >
                                                    <div className="point ml-3">
                                                        <p className="text-white">{single}</p>
                                                    </div>
                                                </div>
                                            ),
                                        )}
                                    </div>
                                    <div className="ml-8 mt-3 mb-3 flex justify-center xl:mt-8">
                                        <OrderButtonVersionFour
                                            handleClick={handleScrollToOrderForm}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                );
            })} */}

      <section className="review mt-10 sm:mt-20">
        <div className="container mx-auto px-4">
          <div>
            <h2 className="mb-10 text-center text-3xl font-bold text-gray-800 sm:text-4xl">
              Customer Reviews
            </h2>
          </div>
          <Swiper
            modules={[Navigation, Pagination, A11y, Autoplay]}
            spaceBetween={20}
            slidesPerView={3}
            pagination={{ clickable: true }}
            autoplay={{
              delay: 3500,
              disableOnInteraction: false,
            }}
            loop={true}
            breakpoints={{
              320: {
                slidesPerView: 1,
              },
              640: {
                slidesPerView: 2,
              },
              1024: {
                slidesPerView: 3,
              },
            }}
            onSwiper={(swiper) => console.log(swiper)}
            onSlideChange={() => console.log("slide change")}
          >
            <SwiperSlide>
              <div className="review-card h-full rounded-2xl bg-gray-100 p-5">
                <div className="flex items-center gap-3">
                  <div className="h-[60px] w-[60px] rounded-full border border-gray-200">
                    <Image
                      src="https://i.pinimg.com/736x/8b/16/7a/8b167af653c2399dd93b952a48740620.jpg"
                      alt="logo"
                      width={20}
                      height={20}
                      className="h-full w-full rounded-full object-cover"
                    />
                  </div>
                  <div>
                    <h5 className="text-2xl font-semibold">Hasibur Rahman</h5>
                    <p>Pro Developer</p>
                  </div>
                </div>
                <p className="mt-3">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit.
                  Accusamus, quisquam optio eveniet ipsa, asperiores dolore
                  animi libero minus illum, dolorum voluptatum! Vero quibusdam
                  eaque optio, laudantium nihil ut accusamus dolorem.
                </p>
                <div className="mt-5 flex items-center gap-4">
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                </div>
              </div>
            </SwiperSlide>
            <SwiperSlide>
              <div className="review-card h-full rounded-2xl bg-gray-100 p-5">
                <div className="flex items-center gap-3">
                  <div className="h-[60px] w-[60px] rounded-full border border-gray-200">
                    <Image
                      src="https://i.pinimg.com/736x/8b/16/7a/8b167af653c2399dd93b952a48740620.jpg"
                      alt="logo"
                      width={20}
                      height={20}
                      className="h-full w-full rounded-full object-cover"
                    />
                  </div>
                  <div>
                    <h5 className="text-2xl font-semibold">Hasibur Rahman</h5>
                    <p>Pro Developer</p>
                  </div>
                </div>
                <p className="mt-3">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit.
                  Accusamus, quisquam optio eveniet ipsa, asperiores dolore
                  animi libero minus illum, dolorum voluptatum! Vero quibusdam
                  eaque optio, laudantium nihil ut accusamus dolorem.
                </p>
                <div className="mt-5 flex items-center gap-4">
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                </div>
              </div>
            </SwiperSlide>
            <SwiperSlide>
              <div className="review-card h-full rounded-2xl bg-gray-100 p-5">
                <div className="flex items-center gap-3">
                  <div className="h-[60px] w-[60px] rounded-full border border-gray-200">
                    <Image
                      src="https://i.pinimg.com/736x/8b/16/7a/8b167af653c2399dd93b952a48740620.jpg"
                      alt="logo"
                      width={20}
                      height={20}
                      className="h-full w-full rounded-full object-cover"
                    />
                  </div>
                  <div>
                    <h5 className="text-2xl font-semibold">Hasibur Rahman</h5>
                    <p>Pro Developer</p>
                  </div>
                </div>
                <p className="mt-3">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit.
                  Accusamus, quisquam optio eveniet ipsa, asperiores dolore
                  animi libero minus illum, dolorum voluptatum! Vero quibusdam
                  eaque optio, laudantium nihil ut accusamus dolorem.
                </p>
                <div className="mt-5 flex items-center gap-4">
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                </div>
              </div>
            </SwiperSlide>
            <SwiperSlide>
              <div className="review-card h-full rounded-2xl bg-gray-100 p-5">
                <div className="flex items-center gap-3">
                  <div className="h-[60px] w-[60px] rounded-full border border-gray-200">
                    <Image
                      src="https://i.pinimg.com/736x/8b/16/7a/8b167af653c2399dd93b952a48740620.jpg"
                      alt="logo"
                      width={20}
                      height={20}
                      className="h-full w-full rounded-full object-cover"
                    />
                  </div>
                  <div>
                    <h5 className="text-2xl font-semibold">Hasibur Rahman</h5>
                    <p>Pro Developer</p>
                  </div>
                </div>
                <p className="mt-3">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit.
                  Accusamus, quisquam optio eveniet ipsa, asperiores dolore
                  animi libero minus illum, dolorum voluptatum! Vero quibusdam
                  eaque optio, laudantium nihil ut accusamus dolorem.
                </p>
                <div className="mt-5 flex items-center gap-4">
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                  <FaStar className="text-yellow-500" />
                </div>
              </div>
            </SwiperSlide>
          </Swiper>
        </div>
      </section>

      <section className="discount mt-14 sm:mt-16 md:mt-20 xl:mt-24">
        <div className="container mx-auto px-4">
          <div className="rounded-xl bg-gradient-to-b from-[#A2449A] to-[#A2449A] py-10">
            <h3 className="mb-5 text-center text-3xl font-bold text-gray-800 line-through sm:text-4xl">
              প্রোডাক্টের রেগুলার মূল্য:{" "}
              {convertToBanglaNumber(productDetails?.productDetails?.price)}{" "}
              টাকা
            </h3>
            <h3 className="text-center text-3xl font-bold text-white sm:text-4xl">
              ডিস্কাউন্ট অফারে বর্তমান মূল্য:{" "}
              {convertToBanglaNumber(
                productDetails?.productDetails?.discountPrice,
              )}{" "}
              টাকা
            </h3>
            <div className="mt-5 flex justify-center">
              <OrderButtonVersionFour handleClick={handleScrollToOrderForm} />
            </div>
          </div>
        </div>
      </section>

      <div>
        <DirectOrderForm productDetails={productDetails?.productDetails} />
      </div>

      {productDetails?.generalQuestions?.length ? (
        <div className="px-2 py-6 text-start md:px-10 md:py-10 lg:px-20 xl:px-40">
          <div className="pb-4 text-center text-2xl font-bold">
            সাধারণ জিজ্ঞাসা
          </div>
          {productDetails?.generalQuestions?.map((single) => (
            <Accordion title={single?.question} key={single?.question}>
              <div
                dangerouslySetInnerHTML={{
                  __html: single?.answer ? single?.answer : "<p>No answer</p>",
                }}
              />
            </Accordion>
          ))}
        </div>
      ) : (
        ""
      )}
    </div>
  );
};

export default LandingPageFiveOverview;
