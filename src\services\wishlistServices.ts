import { WishlistResult } from "@/Types/wishlistTypes";
import { BASE_URL } from "@/environment/environment";
import axios from "axios";

export const addToWishListApi = () => {
  return {
    api(data: any) {
      return axios
        .post(`${BASE_URL}/wishlist/add-new`, data)
        .then(({ data }) => data);
    },
    getKey() {
      return ["addToWishList"];
    },
  };
};

export const getWishlistApi = () => {
  return {
    api() {
      return axios
        .get<WishlistResult>(`${BASE_URL}/wishlist/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getWishlistApi"];
    },
  };
};

export const removeFromWishlist = () => {
  return {
    api(id: string) {
      return axios
        .delete(`${BASE_URL}/wishlist/delete/${id}`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["removeFromWishlist"];
    },
  };
};
