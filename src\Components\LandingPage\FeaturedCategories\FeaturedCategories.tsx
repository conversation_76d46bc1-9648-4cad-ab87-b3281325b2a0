import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleCategory } from "@/services/bannerBrandCategoryServices";
import Image from "next/image";
import Link from "next/link";
import { useContext } from "react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

const FeaturedCategories = () => {
  const { categories, isCategoriesLoading }: ContextDataType =
    useContext(LocalDataContext);

  return (
    <Swiper
      effect="coverflow"
      grabCursor={true}
      centeredSlides={false}
      slidesPerView={4}
      spaceBetween={6}
      coverflowEffect={{
        rotate: 0,
        stretch: 10,
        depth: 100,
        modifier: 1,
        slideShadows: true,
      }}
      pagination={{
        clickable: true,
        el: ".swiper-pagination", // Specify the container for pagination
        type: "bullets", // Use bullets for pagination
      }}
      className="mySwiper"
      autoplay={{
        delay: 3000,
        disableOnInteraction: false,
        reverseDirection: false,
      }}
      loop={true}
      breakpoints={{
        1024: {
          slidesPerView: 8,
        },
        768: {
          slidesPerView: 3,
        },
      }}
      modules={[Autoplay]}
    >
      {!isCategoriesLoading &&
        categories?.map((card: SingleCategory) => (
          <SwiperSlide
            key={card.name}
            style={{
              display: card.name.toLowerCase() === "all" ? "none" : "",
            }}
          >
            <Link href={ROUTES.PRODUCTS.CATEGORY(card.slug)}>
              <div className="flex cursor-pointer flex-col items-center  justify-center rounded-lg  border border-gray-400 p-4 shadow">
                <Image
                  height={100}
                  width={100}
                  src={
                    generateProductImage(card?.imageUrl) ??
                    "https://t4.ftcdn.net/jpg/04/73/25/49/360_F_473254957_bxG9yf4ly7OBO5I0O5KABlN930GwaMQz.jpg"
                  }
                  /*  src={
                      generateProductImage(card?.imageUrl) ??
                      "https://t4.ftcdn.net/jpg/04/73/25/49/360_F_473254957_bxG9yf4ly7OBO5I0O5KABlN930GwaMQz.jpg"
                    } */
                  alt="feature"
                  className="mb-4 h-[40px] w-[40px] transform rounded-lg object-cover transition-transform hover:scale-110 md:h-[50px] md:w-[50px]"
                />
                <h3 className="h-[25px] text-center text-[8px] font-semibold md:text-xs">
                  {card.name}
                </h3>
              </div>
            </Link>
          </SwiperSlide>
        ))}
    </Swiper>
  );
};

export default FeaturedCategories;
