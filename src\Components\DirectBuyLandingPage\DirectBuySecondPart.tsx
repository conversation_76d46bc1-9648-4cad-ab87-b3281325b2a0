import { WhyBest } from "@/Types/landingPageProductType";
import Image from "next/image";
import { IoStar } from "react-icons/io5";
import { MdStars } from "react-icons/md";
import { generateProductImage } from "../utils/GenerateProductImage";
import OrderNowButton from "./OrderNowButton";
interface Props {
  isCombo: boolean;
  whyBest: WhyBest;
  whyBestTwo: WhyBest;
}

const DirectBuySecondPart = ({ isCombo, whyBest, whyBestTwo }: Props) => {
  return (
    <div className="py-8">
      <div className="flex items-center justify-center gap-4 text-2xl font-bold">
        <IoStar />
        <span>উপকারিতা</span>
        <IoStar />
      </div>
      {isCombo ? (
        <div className="mt-4 flex w-full flex-col items-center gap-8  px-2 py-2 sm:px-4  lg:px-20 xl:px-20">
          <div className="flex w-full flex-wrap  gap-8 sm:flex-nowrap">
            <div className="flex w-full flex-col gap-6 rounded-xl border-2 border-[#A2784E] px-4 py-4">
              <div className="flex flex-col items-center gap-4">
                <Image
                  height={100}
                  width={100}
                  src={generateProductImage(whyBest?.image)}
                  alt="why buy image"
                  className="h-[250px] w-full rounded-xl  border-2 border-[#A2784E] lg:h-[200px] lg:w-[200px]"
                />
                <OrderNowButton />
              </div>
              <div>
                {whyBest?.options?.map((single: string) => (
                  <div className="my-1 flex items-start gap-2" key={single}>
                    <div>
                      <MdStars className="text-xl md:text-2xl" />
                    </div>
                    <span className="text-start text-sm md:text-lg">
                      {single}
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex w-full flex-col gap-6 rounded-xl border-2 border-[#A2784E] px-4 py-4">
              <div className="flex flex-col items-center gap-4">
                <Image
                  height={100}
                  width={100}
                  src={generateProductImage(whyBestTwo?.image)}
                  alt="why buy image"
                  className="h-[250px] w-full rounded-xl  border-2 border-[#A2784E] lg:h-[200px] lg:w-[200px]"
                />
                <OrderNowButton />
              </div>
              <div>
                {whyBestTwo?.options?.map((single: string) => (
                  <div className="my-1 flex items-start gap-2" key={single}>
                    <div>
                      <MdStars className="text-xl md:text-2xl" />
                    </div>
                    <span className="text-start text-sm md:text-lg">
                      {single}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="mt-4 flex w-full flex-col items-center px-2 py-2 sm:px-4  lg:px-20 xl:px-60">
          <div className="flex w-full flex-wrap items-center gap-2 rounded-xl border-2 border-[#A2784E] bg-[#F5F5F5] p-2 md:flex-nowrap md:px-8 md:py-4">
            <div className="flex w-full flex-col items-center gap-4">
              <Image
                height={100}
                width={100}
                src={generateProductImage(whyBest?.image)}
                alt="why buy image"
                className="h-[250px] w-full rounded-xl  border-2 border-[#A2784E] lg:h-[300px] lg:w-[300px]"
              />
              <OrderNowButton />
            </div>
            <div className="flex h-full w-full flex-col justify-between">
              {whyBest?.options?.map((single: string) => (
                <div className="my-1 flex items-start gap-2" key={single}>
                  <div>
                    <MdStars className="text-xl md:text-2xl" />
                  </div>
                  <span className="text-start text-sm md:text-lg">
                    {single}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DirectBuySecondPart;
