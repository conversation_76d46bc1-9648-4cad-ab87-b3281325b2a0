import SearchProductList from "@/Components/SearchProductsList/SearchProductList";
import ShopLayout from "@/Layouts/ShopLayout";
import { Metadata } from "next";
import { cookies } from "next/headers";
import React from "react";

export const metadata: Metadata = {
  title: "Search Product | INNI",
};

const Products: React.FC = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;
  return (
    <ShopLayout>
      <div>
        <SearchProductList accessToken={accessToken ? accessToken : ""} />
      </div>
    </ShopLayout>
  );
};

export default Products;
