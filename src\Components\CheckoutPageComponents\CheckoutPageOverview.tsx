"use client";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleProductOfOrderOrCheckout } from "@/Types/public-types";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { getCheckoutApi } from "@/services/checkoutServices";
import { createNewOrderApi } from "@/services/orderServices";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import * as Yup from "yup";
import CheckoutOrOrderedProducts from "../ReusableComponents/CheckoutOrOrderedProducts/CheckoutOrOrderedProducts";
import CartSummery from "../ReusableComponents/CheckoutPageComponents/CartSummery";
import DeliveryDetailsForm from "../ReusableComponents/CheckoutPageComponents/DeliveryDetailsForm";
import { divisionsWithDistrict } from "../utils/divisionData";
interface Props {
  accessToken: string;
}

const CheckoutPageOverview = ({ accessToken }: Props) => {
  const { userHeaderDetails }: ContextDataType = useContext(LocalDataContext);
  const router = useRouter();
  const queryClient = useQueryClient();
  const [districts, setDistricts] = useState<string[]>([]);
  const [deliveryCharge, setDeliveryCharge] = useState<number>(150);
  const [totalQuantity, setTotalQuantity] = useState<number>(0);
  const [totalProductPrice, setTotalProductPrice] = useState<number>(0);
  const [totalDiscount, setTotalDiscount] = useState<number>(0);
  const [delivery, setDelivery] = useState<string>("inside dhaka");
  const [paymentMethod, setPaymentMethod] = useState<string>("COD");
  const [isCreatingOrder, setIsCreatingOrder] = useState<boolean>(false);

  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    email: Yup.string().email("Invalid email"),
    phone: Yup.string().required("Phone number is required").min(11).max(11),
    zipCode: Yup.string(),
    deliveryAddress: Yup.string().required("Address is required"),
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      email: "",
      phone: "",
      division: "",
      district: "",
      zipCode: "",
      deliveryAddress: "",
      note: "",
      bkashAccount: "",
      paidAmount: 0,
      transactionId: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      setIsCreatingOrder(true);
      const orderData = {
        name: values.name,
        phoneNumber: values.phone,
        email: values.email,
        division: userHeaderDetails?.userDetails?.division,
        district: userHeaderDetails?.userDetails?.district,
        deliveryAddress: values.deliveryAddress,
        delivery: delivery,
        paymentMethod: paymentMethod,
        bkashNumber: values.bkashAccount,
        transactionId: values.transactionId,
        orderStatus: "Pending",
        orderType: "auto",
        createdBy: "user",
        createdFrom: "website",
        zipCode: values.zipCode,
        userIpAddress: localStorage.getItem("ip_address") ?? "not-found",
        customerSource: localStorage.getItem("utm_source") ?? "not-found",
        trackingHistory: [
          { time: new Date(), message: "Order Created Successfully" },
        ],
        notes: [
          {
            name: values.name,
            image: userHeaderDetails?.userDetails?.profilePicture,
            time: new Date(),
            message: values.note,
          },
        ],
        products: checkout?.result.products.map((product: any) => {
          return {
            productId: product?.productId,
            quantity: product.quantity,
          };
        }),
        userSubmittedAmount: values.paidAmount ?? 0,
      };

      toast.promise(
        createNewOrderMutedAsync(orderData),
        {
          success: "Order Created Successful",
          error: "Error Placing order",
          loading: "Creating New order",
        },
        {
          id: "add-new-order",
        },
      );
    },
  });

  const { api: CreateOrderApi } = createNewOrderApi();

  const { mutateAsync: createNewOrderMutedAsync } = useMutation(
    CreateOrderApi,
    {
      onSuccess: (data) => {
        setIsCreatingOrder(false);
        queryClient.invalidateQueries();
        router.push(ROUTES.SUCCESS(data?.result?.orderId));
      },
    },
  );

  const { api, getKey } = getCheckoutApi();
  const { data: checkout, isLoading } = useQuery(getKey(), api, {
    refetchOnWindowFocus: false,
    enabled: accessToken ? true : false,
    onSuccess(data) {
      let quantity = 0;
      let price = 0;
      let discount = 0;
      data.result?.products?.map(
        (singleItem: SingleProductOfOrderOrCheckout) => {
          (quantity += singleItem.quantity),
            (price += singleItem.price * singleItem.quantity);
          discount +=
            (singleItem.price - singleItem.discountPrice) * singleItem.quantity;
        },
      );
      setTotalQuantity(quantity);
      setTotalProductPrice(price);
      setTotalDiscount(discount);
      if (data?.result?.products?.length) {
        const fakeData = {
          event: "initiate_checkout",
          url: window.location.href,
          value: price - discount,
          user_log_in_status: userHeaderDetails?.userDetails?.name
            ? "logged in"
            : "not logged in",
          user_id: userHeaderDetails?.userDetails?._id ?? "",
          user_name: userHeaderDetails?.userDetails?.name ?? "",
          user_phone_number: userHeaderDetails?.userDetails?.mobileNumber ?? "",
          user_email: userHeaderDetails?.userDetails?.email ?? "",
          user_zip: userHeaderDetails?.userDetails?.zipCode ?? "",
          user_district: userHeaderDetails?.userDetails?.district ?? "",
          items: data?.result?.products
            ? data?.result?.products?.map(
                (single: SingleProductOfOrderOrCheckout) => {
                  return {
                    item_id: single?.productId,
                    item_name: single?.name,
                    price: single?.discountPrice,
                    item_brand: single?.brand,
                    item_category: single?.productCategory,
                    item_quantity: single?.quantity,
                    // slug: single?.slug,
                    sku: single?.productId?.replace("GMK-", ""),
                  };
                },
              )
            : null,
        };
        gtmDataLayerDataPass(fakeData);
      }
    },
  });

  useEffect(() => {
    const val = divisionsWithDistrict.find(
      (si) => si.division === formik.values.division,
    )?.districts;

    setDistricts(val || []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik?.values.division]);

  useEffect(() => {
    if (delivery === "inside dhaka") {
      setDeliveryCharge(80);
    } else {
      setDeliveryCharge(150);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [delivery]);

  useEffect(() => {
    if (userHeaderDetails?.userDetails) {
      formik.setFieldValue("name", userHeaderDetails?.userDetails?.name);
      formik.setFieldValue(
        "phone",
        userHeaderDetails?.userDetails?.mobileNumber,
      );
      formik.setFieldValue("email", userHeaderDetails?.userDetails?.email);
      formik.setFieldValue(
        "deliveryAddress",
        userHeaderDetails?.userDetails?.deliveryAddress,
      );
      formik.setFieldValue("zipCode", userHeaderDetails?.userDetails?.zipCode);
      formik.setFieldValue(
        "district",
        userHeaderDetails?.userDetails?.district,
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userHeaderDetails?.userDetails]);

  return (
    <div className="mt-2">
      <div className="flex flex-col gap-4 md:flex-row">
        <DeliveryDetailsForm
          delivery={delivery}
          setDelivery={setDelivery}
          paymentMethod={paymentMethod}
          setPaymentMethod={setPaymentMethod}
          deliveryCharge={deliveryCharge}
          formik={formik}
          isOrderCreating={isCreatingOrder}
          totalProductPrice={totalProductPrice}
          totalDiscount={totalDiscount}
        />
        <CartSummery
          totalQuantity={totalQuantity}
          totalProductPrice={totalProductPrice}
          totalDiscount={totalDiscount}
          deliveryCharge={deliveryCharge}
          formik={formik}
          isOrderCreating={isCreatingOrder}
        />
      </div>
      <div>
        <CheckoutOrOrderedProducts products={checkout?.result?.products} />
      </div>
    </div>
  );
};

export default CheckoutPageOverview;
