"use client";
import { ROUTES } from "@/Routes";
import { SingleProductOfOrderOrCheckout } from "@/Types/public-types";
import { SingleOrderDetails } from "@/Types/orderTypes";
import { getOrdersListApi } from "@/services/orderServices";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";

interface Props {
  accessToken: string;
}

const OrdersPageOverview = ({ accessToken }: Props) => {
  const { api, getKey } = getOrdersListApi();
  const { data: orders, isLoading } = useQuery(getKey(), api, {
    refetchOnWindowFocus: false,
    enabled: accessToken ? true : false,
    onSuccess(data) {
      // setSelectedForCheckout(data.results);
    },
  });

  const getTotalProducts = (products: any) => products.length;
  const getTotalQuantity = (products: any) =>
    products.reduce((total: any, product: any) => total + product.quantity, 0);
  const getTotalPrice = (products: any) =>
    products.reduce(
      (total: any, product: any) => total + product.quantity * product.price,
      0
    );
  return (
    <>
      <div className="hidden w-full md:block">
        <table className="w-full border-collapse border border-gray-300">
          <thead className="bg-gray-100">
            <tr>
              <th className="border border-gray-300 p-2 text-center text-sm font-normal">
                Order No
              </th>
              <th className="border border-gray-300 p-2 text-center text-sm font-normal">
                Date
              </th>
              {/* <th className="p-2 border border-gray-300 text-sm font-normal text-center">
                Products
              </th> */}
              <th className="border border-gray-300 p-2 text-center text-sm font-normal">
                Items
              </th>
              <th className="border border-gray-300 p-2 text-center text-sm font-normal">
                Quantity
              </th>
              <th className="border border-gray-300 p-2 text-center text-sm font-normal">
                Total Price
              </th>
              <th className="border border-gray-300 p-2 text-center text-sm font-normal">
                Status
              </th>
              <th className="border border-gray-300 p-2 text-center text-sm font-normal">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {orders?.results?.map(
              (order: SingleOrderDetails, index: number) => (
                <tr key={index} className={index % 2 === 0 ? "bg-gray-50" : ""}>
                  <td className="border border-gray-300 p-2 text-center text-sm font-normal">
                    {order.orderId}
                  </td>
                  <td className="border border-gray-300 p-2 text-center text-sm font-normal">
                    {new Intl.DateTimeFormat("en-US", {
                      day: "2-digit",
                      month: "short",
                      year: "numeric",
                    }).format(new Date(order.createdAt))}
                    <br />
                    {new Intl.DateTimeFormat("en-US", {
                      hour: "2-digit",
                      minute: "2-digit",
                    }).format(new Date(order.createdAt))}
                  </td>
                  {/* <td className="p-2 border border-gray-300 text-sm font-normal text-center">
                    {order.products.map(
                      (
                        product: SingleProductOfOrderOrCheckout,
                        idx: number
                      ) => (
                        <>
                          <div
                            key={idx}
                            className="flex items-center justify-between"
                          >
                            <p>
                              {product.name.slice(0, 70)} - {product.size}
                            </p>
                            <p>
                              ({product.quantity} x ৳{product.discountPrice})
                            </p>
                          </div>
                          <hr
                            style={{
                              display:
                                idx === order.products.length - 1
                                  ? "none"
                                  : "block",
                              color: "black",
                              height: "2px",
                            }}
                          />
                        </>
                      )
                    )}
                  </td> */}
                  <td className="border border-gray-300 p-2 text-center text-sm font-normal">
                    {getTotalProducts(order.products)}
                  </td>
                  <td className="border border-gray-300 p-2 text-center text-sm font-normal">
                    {getTotalQuantity(order.products)}
                  </td>
                  <td className="border border-gray-300 p-2 text-center text-sm font-normal">
                    ৳{order.totalAmount.toFixed(2)}
                  </td>
                  <td className="border border-gray-300 p-2 text-center text-sm font-normal">
                    {order.orderStatus}
                  </td>
                  <td className="border border-gray-300 p-2 text-center text-sm font-normal">
                    <Link href={ROUTES.DASHBOARD.ORDERS.VIEW(order.orderId)}>
                      <button className="bg-primary rounded px-3 py-1 text-white hover:bg-orange-400">
                        See
                      </button>
                    </Link>
                  </td>
                </tr>
              ),
            )}
          </tbody>
        </table>
      </div>

      {/* for small screen */}
      <div className="block md:hidden">
        {orders?.results.map((singleOrder: SingleOrderDetails) => (
          <div
            className="card mb-4 rounded-2xl border-2 p-2 shadow-lg"
            key={singleOrder._id}
          >
            <div className="flex items-center justify-between">
              <p className="font-semibold">Order No : {singleOrder.orderId}</p>
              <p className="font-semibold">{singleOrder.orderStatus}</p>
            </div>
            {/* <div className="text-sm font-normal mt-2">
              {singleOrder.products.map(
                (product: SingleProductOfOrderOrCheckout, idx: number) => (
                  <>
                    <div
                      key={idx}
                      className="flex items-center justify-between"
                    >
                      <p>
                        {product.name.slice(0, 40)} - {product.size}
                      </p>
                      <p>
                        ({product.quantity} x ৳{product.discountPrice})
                      </p>
                    </div>
                    <hr
                      style={{
                        display:
                          idx === singleOrder.products.length - 1
                            ? "none"
                            : "block",
                        color: "black",
                        height: "2px",
                      }}
                    />
                  </>
                )
              )}
            </div> */}
            <hr
              style={{
                color: "black",
                height: "2px",
              }}
            />
            <div>
              <div className="font-semibold ">
                Total Amount : ৳ {singleOrder.totalAmount}
              </div>
              <div className=" font-semibold ">
                Total Items : {getTotalProducts(singleOrder.products)}
              </div>
              <div className="font-semibold">
                Total Quantity : {getTotalQuantity(singleOrder.products)}
              </div>
            </div>

            <hr
              style={{
                color: "black",
                height: "2px",
              }}
            />
            <div>
              <p className="text-sm font-normal">
                Order Date :
                {new Intl.DateTimeFormat("en-US", {
                  day: "2-digit",
                  month: "short",
                  year: "numeric",
                }).format(new Date())}
                {"  "}
                {new Intl.DateTimeFormat("en-US", {
                  hour: "2-digit",
                  minute: "2-digit",
                }).format(new Date())}
              </p>
            </div>
            <div className="mt-2 flex items-center justify-center text-sm font-normal">
              <Link href={ROUTES.DASHBOARD.ORDERS.VIEW(singleOrder.orderId)}>
                <button className="bg-primary w-[100px] rounded px-3 py-1 text-white hover:bg-orange-400">
                  See Details
                </button>
              </Link>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default OrdersPageOverview;
