// pages/return-refund.tsx
import React from "react";
import Head from "next/head";
import { Metadata } from "next";
import TitleViewer from "@/Components/ReusableComponents/TitleViewer/TitleViewer";
import ShopLayout from "@/Layouts/ShopLayout";
import { BASE_URL } from "@/environment/environment";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
  title: "Return And Refund | INNI",
};

const ReturnAndRefundPage = async () => {
  const data = await getData();
  return (
    <ShopLayout>
      <div
        dangerouslySetInnerHTML={{
          __html: data?.result?.content
            ? data?.result?.content
            : "<p>Not Fount</p>",
        }}
      />
    </ShopLayout>
  );
};

export default ReturnAndRefundPage;

async function getData() {
  const res = await fetch(`${BASE_URL}/content/return`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
  }

  return res.json();
}
