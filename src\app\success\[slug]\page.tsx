import OrderSuccessOverview from "@/Components/ReusableComponents/OrderSuccessComponent/OrderSuccessOverview";
import ShopLayout from "@/Layouts/ShopLayout";
import { OrdersDetailsResponse } from "@/Types/orderTypes";
import { BASE_URL } from "@/environment/environment";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
  title: "Order Success | INNI",
};

interface Props {
  params: Promise<{ slug: string }>;
}

const SuccessPage = async ({ params }: Props) => {
  const accessToken = (await cookies()).get("GMK")?.value;
  const { slug } = await params;
  const data: OrdersDetailsResponse = await getData(slug);
  return (
    <ShopLayout>
      <OrderSuccessOverview
        orderDetails={data.result}
        accessToken={accessToken}
      />
    </ShopLayout>
  );
};

export default SuccessPage;

async function getData(orderId: string) {
  const res = await fetch(`${BASE_URL}/orders/single/${orderId}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}