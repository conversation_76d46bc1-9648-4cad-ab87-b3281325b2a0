import RightSideCartIcon from "@/Components/ReusableComponents/RightSideCartIcon/RightSideCartIcon";
import FooterModern from "@/Components/Shared/Footer/FooterModern";
import LowerNavbar from "@/Components/Shared/Header/LowerNavbar";
import Navbar from "@/Components/Shared/Header/Navbar";
import UpperNavbar from "@/Components/Shared/Header/UpperNavbar";
import GetCustomerTrackUTMSource from "@/utils/CustomerPlatformSourceTrack";
import { cookies } from "next/headers";

export default async function ShopLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const accessToken = (await cookies()).get("GMK")?.value;
  return (
    <section>
      <div style={{ position: "fixed", width: "100%", zIndex: 99 }}>
        <UpperNavbar />
        <Navbar />
        <LowerNavbar />
      </div>
      <div
        className="fixed right-0 top-[300px] md:top-[400px]"
        style={{ zIndex: 99 }}
      >
        <RightSideCartIcon accessToken={accessToken} />
      </div>
      <div
        className="w-full mx-auto min-h-[95vh] px-2 pt-[120px]  text-black sm:px-6 sm:pt-[170px] md:px-6 lg:pt-[200px]  xl:pt-[180px] lg:px-20 xl:px-28 2xl:px-32"
        style={{
          background:
            "linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(253,222,222,0.2) 50%, rgba(255,255,255,1) 100%)",
        }}
      >
        {children}
      </div>
      <div>
        <FooterModern />
      </div>

      {/* <div className="hidden md:block">
        <FacebookMessenger />
      </div> */}
      <GetCustomerTrackUTMSource />
    </section>
  );
}
