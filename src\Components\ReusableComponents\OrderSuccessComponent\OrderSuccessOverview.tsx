"use client";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleOrderDetails } from "@/Types/orderTypes";
import { SingleProductOfOrderOrCheckout } from "@/Types/public-types";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { pdf } from "@react-pdf/renderer";
import Link from "next/link";
import { useContext, useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import InvoicePdf from "./InvoicePdf";

interface Props {
  orderDetails: SingleOrderDetails;
  accessToken?: string;
}

const OrderSuccessOverview = ({ orderDetails, accessToken }: Props) => {
  const { userHeaderDetails }: ContextDataType = useContext(LocalDataContext);
  const containerRef = useRef<HTMLElement | null>(null);
  const [pdfDownloadButtonLoader, setPdfDownloadButtonLoader] = useState(false);

  useEffect(() => {
    if (orderDetails?.products?.length) {
      const fakeData = {
        event: "purchase",
        url: window.location.href,
        value: orderDetails?.totalAmount,
        order_id: orderDetails?.orderId,
        user_log_in_status: userHeaderDetails?.userDetails?.name
          ? "logged in"
          : "not logged in",
        user_id: orderDetails?.userId ?? "",
        user_name: orderDetails?.name ?? "",
        user_phone_number: orderDetails?.phoneNumber ?? "",
        user_email: orderDetails?.email ?? "",
        user_zip: orderDetails?.zipCode ?? "",
        user_district: orderDetails?.district ?? "",
        items: orderDetails?.products
          ? orderDetails?.products?.map(
              (single: SingleProductOfOrderOrCheckout) => {
                return {
                  item_id: single?.productId,
                  item_name: single?.name,
                  price: single?.discountPrice,
                  item_brand: single?.brand,
                  item_category: single?.productCategory,
                  item_quantity: single?.quantity,
                  // slug: single?.slug,
                  sku: single?.productId?.replace("GMK-", ""),
                };
              },
            )
          : null,
      };
      gtmDataLayerDataPass(fakeData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderDetails]);

  const generatePdf = async () => {
    //generates pdf after api call and download the pdf
    setPdfDownloadButtonLoader(true);
    try {
      if (orderDetails) {
        const blob = await pdf(
          <InvoicePdf orderDetails={orderDetails} />,
        ).toBlob();

        const url = URL.createObjectURL(blob);
        // downloads the pdf
        const link = document.createElement("a");
        link.href = url;
        link.download = `INNI_ORDER_${orderDetails?.orderId}.pdf`;

        containerRef.current?.appendChild(link);
        link.click();

        window.URL.revokeObjectURL(url);
        containerRef.current?.removeChild(link);
        setPdfDownloadButtonLoader(false);
      }
    } catch (error) {
      setPdfDownloadButtonLoader(false);
      console.log(error);

      toast.error("an error occured while downloading pdf");
    }
  };
  return (
    <div className="flex min-h-[80vh] items-center justify-center">
      <div className="flex flex-col items-center justify-center gap-[30px]">
        <SuccessIcon />
        <p className="text-[24px] font-black md:text-[48px]">
          Order Placed Successfully
        </p>
        <p>Thank you for Order on our website</p>
        <div className="flex items-center gap-4 md:gap-10">
          <button
            className="bg-primary w-[170px] rounded-2xl px-4 py-2 font-semibold  text-white md:w-[200px] md:font-bold"
            onClick={() => generatePdf()}
          >
            {pdfDownloadButtonLoader ? "Downloading.." : "Download Invoice"}
          </button>
          {accessToken ? (
            <Link href={ROUTES.DASHBOARD.ORDERS.HOME}>
              <button className="bg-primary w-[160px] rounded-2xl px-4 py-2 font-semibold  text-white md:w-[200px] md:font-bold">
                Go To Orders
              </button>
            </Link>
          ) : (
            ""
          )}
        </div>
        <div>Your Order Id is {orderDetails?.orderId}</div>
        <div>
          <p className="mb-2 border-b border-black">Order Summery</p>
          <table className="w-[350px] border border-black">
            <tbody>
              <tr style={{ backgroundColor: "#E1E3E7" }}>
                <td className="p-2 text-sm font-normal">Order ID</td>
                <td className="p-2 text-sm font-normal">:</td>
                <td className="p-2 text-sm font-normal">
                  {orderDetails?.orderId}
                </td>
              </tr>
              <tr style={{ backgroundColor: "white" }}>
                <td className="p-2 text-sm font-normal">Product Price</td>
                <td className="p-2 text-sm font-normal">:</td>
                <td className="p-2 text-sm font-normal">
                  ৳ {orderDetails?.productPrice}
                </td>
              </tr>
              <tr style={{ backgroundColor: "#E1E3E7" }}>
                <td className="p-2 text-sm font-normal">Discount</td>
                <td className="p-2 text-sm font-normal">:</td>
                <td className="p-2 text-sm font-normal">
                  ৳ {orderDetails?.discount}
                </td>
              </tr>
              <tr style={{ backgroundColor: "white" }}>
                <td className="p-2 text-sm font-normal">Delivery Charge</td>
                <td className="p-2 text-sm font-normal">:</td>
                <td className="p-2 text-sm font-normal">
                  ৳ {orderDetails?.deliveryCharge}
                </td>
              </tr>
              <tr style={{ backgroundColor: "#E1E3E7" }}>
                <td className="p-2 text-sm font-normal">Total Amount</td>
                <td className="p-2 text-sm font-normal">:</td>
                <td className="p-2 text-sm font-normal">
                  ৳ {orderDetails?.totalAmount}
                </td>
              </tr>
              <tr style={{ backgroundColor: "white" }}>
                <td className="p-2 text-sm font-normal">Paid Amount</td>
                <td className="p-2 text-sm font-normal">:</td>
                <td className="p-2 text-sm font-normal">
                  ৳ {orderDetails?.paidAmount}
                </td>
              </tr>
              <tr style={{ backgroundColor: "#E1E3E7" }}>
                <td className="p-2 text-sm font-normal">Due Amount</td>
                <td className="p-2 text-sm font-normal">:</td>
                <td className="p-2 text-sm font-normal">
                  ৳ {orderDetails?.dueAmount}
                </td>
              </tr>
              <tr style={{ backgroundColor: "white" }}>
                <td className="p-2 text-sm font-normal">Payment Method</td>
                <td className="p-2 text-sm font-normal">:</td>
                <td className="p-2 text-sm font-normal">
                  ৳ {orderDetails?.paymentMethod}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        {/* {orderDetails ? (
          <InvoicePdf orderDetails={orderDetails} />
        ) : (
          ""
        )} */}
      </div>
    </div>
  );
};

export default OrderSuccessOverview;

const SuccessIcon = () => {
  return (
    <svg
      width="167"
      height="167"
      viewBox="0 0 167 167"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_105_2682)">
        <rect
          x="30"
          y="20"
          width="107"
          height="107"
          rx="53.5"
          fill="url(#paint0_linear_105_2682)"
        />
      </g>
      <path
        d="M67.1523 74.243L78.5459 85.3888L101.333 63.0972"
        stroke="white"
        strokeWidth="6"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <defs>
        <filter
          id="filter0_d_105_2682"
          x="0"
          y="0"
          width="167"
          height="167"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="10" />
          <feGaussianBlur stdDeviation="15" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.964706 0 0 0 0 0.870588 0 0 0 0 0.552941 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_105_2682"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_105_2682"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_105_2682"
          x1="30"
          y1="73.5"
          x2="137.067"
          y2="74.0391"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#092143" />
          <stop offset="1" stop-color="#0C1E32" />
        </linearGradient>
      </defs>
    </svg>
  );
};
