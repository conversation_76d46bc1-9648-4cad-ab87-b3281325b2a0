server {
    listen 80;

    # Server name can be your domain or IP
    server_name koreantrendymall.com;

    # Location for reverse proxying
    location / {
        # Backend server address
        proxy_pass http://127.0.0.1:3000;  # Replace with your backend address

        # Additional settings for WebSocket support (optional)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Set headers to pass client information to the backend
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}