"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { SingleBanner } from "@/services/bannerServices";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useContext } from "react";
import { Carousel } from "react-responsive-carousel";
import "react-responsive-carousel/lib/styles/carousel.min.css";

const LandingPageBanner = () => {
  const router = useRouter();
  const { banners, isBannersLoading }: ContextDataType =
    useContext(LocalDataContext);
  /* const [rightBanners, setRightBanners] = useState<SingleBanner[]>();
  useEffect(() => {
    if (banners) {
      const right = banners.filter(
        (single: SingleBanner) => single.bannerType === "right",
      );
      setRightBanners(right);
    }
  }, [banners]); */

  return (
    <>
      <div className="grid grid-cols-12 gap-4">
        <div className="col col-span-12">
          {!isBannersLoading && banners?.length ? (
            <div className="">
              {/* rounded-lg border border-pink-600 */}
              <Carousel
                autoPlay={true}
                showThumbs={false}
                showStatus={false}
                infiniteLoop={true}
                interval={4000}
                transitionTime={2000}
              >
                {banners &&
                  banners
                    ?.filter((sin: SingleBanner) => sin.bannerType === "main")
                    ?.map((banner: SingleBanner) => (
                      <div
                        key={banner._id}
                        className="h-full"
                        onClick={() =>
                          router.push(
                            `/${banner.redirectType}/${banner.redirectSlug}`,
                          )
                        }
                      >
                        <Image
                          height={100}
                          width={100}
                          // src="https://i.ibb.co.com/RTCzhb0/bg-img.png"
                          src={generateProductImage(banner.imageUrl)}
                          alt="Carousel Image 1"
                          className="h-[22vh] sm:h-[35vh] md:h-[50vh] lg:h-[70vh]"
                          priority={true}
                        />
                      </div>
                    ))}
              </Carousel>
            </div>
          ) : (
            ""
          )}
        </div>
        {/* <div className="col hidden md:col-span-3 md:block">
          {!isBannersLoading && banners?.length ? (
            <div className="mb-4 mt-0">
              <Link
                href={
                  rightBanners?.length
                    ? `/${rightBanners[0]?.redirectType}/${rightBanners[0]?.redirectSlug}`
                    : ""
                }
              >
                <Image
                  height={100}
                  width={100}
                  src={generateProductImage(
                    rightBanners?.length ? rightBanners[0]?.imageUrl : "",
                  )}
                  alt="Carousel Image 1"
                  className="h-[10vh] w-full rounded-lg border border-pink-600 md:h-[10vh] lg:h-[22vh]"
                  priority={true}
                />
              </Link>
              <Link
                href={
                  rightBanners?.length
                    ? `/${rightBanners[1]?.redirectType}/${rightBanners[1]?.redirectSlug}`
                    : ""
                }
              >
                <Image
                  height={100}
                  width={100}
                  src={generateProductImage(
                    rightBanners?.length ? rightBanners[1]?.imageUrl : "",
                  )}
                  alt="Carousel Image 1"
                  className="mt-2 h-[10vh] w-full rounded-lg border border-pink-600 md:h-[10vh] lg:h-[22vh]"
                  priority={true}
                />
              </Link>
            </div>
          ) : (
            ""
          )}
        </div> */}
      </div>
    </>
  );
};

export default LandingPageBanner;
