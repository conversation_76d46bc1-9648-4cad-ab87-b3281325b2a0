import { Usage } from "@/Types/landingPageProductType";
import Image from "next/image";
import React from "react";
import { generateProductImage } from "../utils/GenerateProductImage";
import OrderNowButton from "./OrderNowButton";
import { MdStars } from "react-icons/md";
import { IoStar } from "react-icons/io5";

interface Props {
  usage: Usage;
}

const DirectBuyThirdPart = ({ usage }: Props) => {
  return (
    <div
      className="py-8"
      style={{
        background: "linear-gradient(73.98deg, #FFFFFF 0%, #FFE0B9 99.42%)",
      }}
    >
      <div className="flex items-center justify-center gap-4 text-2xl font-bold">
        <IoStar />
        <span> ব্যবহার করার নিয়ম।</span>
        <IoStar />
      </div>
      <div className="mt-4 flex w-full flex-col items-center px-2 py-2 sm:px-4  lg:px-20 xl:px-60">
        <div className="flex w-full flex-wrap items-center gap-2 rounded-xl border-2 border-[#A2784E] bg-[#F5F5F5] p-2 md:flex-nowrap md:px-8 md:py-4">
          <div className="order-2 flex h-full w-full flex-col justify-between md:order-1">
            {usage?.options?.map((single: string) => (
              <div className="my-1 flex items-start gap-2" key={single}>
                <div>
                  <MdStars className="text-xl md:text-2xl" />
                </div>
                <span className="text-start text-sm md:text-lg">{single}</span>
              </div>
            ))}
          </div>
          <div className="order-1 flex w-full flex-col items-center gap-4 md:order-2">
            <Image
              height={100}
              width={100}
              src={generateProductImage(usage?.image)}
              alt="why buy image"
              className="h-[250px] w-full rounded-xl  border-2 border-[#A2784E] lg:h-[300px] lg:w-[300px]"
            />
            <OrderNowButton />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DirectBuyThirdPart;
