"use client";
import {
  maximumQuantityModal,
  minimumQuantityModal,
  notLoggedInModal,
} from "@/Components/utils/commonModal";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleProductDetailsType } from "@/Types/productTypes";
import { SingleWishlistItem } from "@/Types/wishlistTypes";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { addToCartApi } from "@/services/cartServices";
import { addToCheckoutApi } from "@/services/checkoutServices";
import { addToWishListApi } from "@/services/wishlistServices";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { BsCartCheck } from "react-icons/bs";

interface Props {
  productDetails: SingleProductDetailsType;
  accessToken: string;
}

const ProductDetailsWithCartAndOtherIcons = ({
  productDetails,
  accessToken,
}: Props) => {
  const {
    name,
    details,
    id,
    brand,
    brandSlug,
    origin,
    size,
    productCategory,
    categorySlug,
    isAvailable,
    price,
    discountPrice,
    deal,
    mainImageUrl,
    rating,
    slug,
    _id,
  } = productDetails;

  const queryClient = useQueryClient();
  const router = useRouter();
  const {
    handleAddToLocalCart,
    wishlist,
    handleAddToLocalCheckout,
    userHeaderDetails,
  }: ContextDataType = useContext(LocalDataContext);

  const [quantity, setQuantity] = useState<number>(1);
  const [isAlreadyInWishlist, setIsAlreadyInWishlist] =
    useState<boolean>(false);

  const handleChangeQuantity = (type: string) => {
    if (type === "increase") {
      if (quantity < 5) {
        setQuantity(quantity + 1);
      } else {
        maximumQuantityModal();
      }
    } else {
      if (quantity > 1) {
        setQuantity(quantity - 1);
      } else {
        minimumQuantityModal();
      }
    }
  };

  const { api: AddToCartApi } = addToCartApi();

  const { mutateAsync: addToCartMutateAsync } = useMutation(AddToCartApi, {
    onSuccess: () => {
      queryClient.invalidateQueries();
      setQuantity(1);
    },
  });

  const handleAddToCart = async () => {
    const productInfo = {
      productId: _id || "",
      quantity: quantity,
      price: discountPrice,
    };

    const addToCartDataLayer = {
      event: "add_to_cart",
      value: discountPrice,
      user_log_in_status: userHeaderDetails?.userDetails?.name
        ? "logged in"
        : "not logged in",
      user_id: userHeaderDetails?.userDetails?._id ?? "",
      user_name: userHeaderDetails?.userDetails?.name ?? "",
      user_phone_number: userHeaderDetails?.userDetails?.mobileNumber ?? "",
      user_email: userHeaderDetails?.userDetails?.email ?? "",
      user_zip: userHeaderDetails?.userDetails?.zipCode ?? "",
      user_district: userHeaderDetails?.userDetails?.district ?? "",
      items: [
        {
          item_id: id,
          item_name: name,
          price: discountPrice,
          item_brand: brand,
          slug: slug,
          item_category: productCategory,
          sku: id?.replace("GMK-", ""),
        },
      ],
    };
    gtmDataLayerDataPass(addToCartDataLayer);

    if (accessToken) {
      toast.promise(
        addToCartMutateAsync(productInfo),
        {
          success: "Product Added to cart Successful",
          error: "Error Adding Product to cart",
          loading: "Adding Product to cart",
        },
        {
          id: "add-to-cart-product",
        },
      );
    } else {
      const data = {
        id: id,
        name: name,
        size: size,
        price: price,
        discountPrice: discountPrice,
        deal: deal,
        mainImageUrl: mainImageUrl,
        rating: rating,
        brand: brand,
        isAvailable: isAvailable,
        quantity: quantity,
      };
      handleAddToLocalCart(data);
      toast.success("Product Added to cart Successful");
    }
  };

  const { api: AddToWishlistApi } = addToWishListApi();

  const { mutateAsync: addToWishlistMutateAsync } = useMutation(
    AddToWishlistApi,
    {
      onSuccess: () => {
        queryClient.invalidateQueries();
      },
    },
  );

  const handleAddToWishlist = async () => {
    const productInfo = {
      productId: _id,
      brand: brand,
      discountPrice: discountPrice,
      id: id,
      image: mainImageUrl,
      isAvailable: isAvailable,
      name: name,
      price: price,
      rating: rating,
      size: size,
    };

    if (accessToken) {
      toast.promise(
        addToWishlistMutateAsync(productInfo),
        {
          success: "Product Added to Wishlist Successful",
          error: "Error Adding Product to Wishlist",
          loading: "Adding Product to Wishlist",
        },
        {
          id: "add-to-wishlist-product",
        },
      );
    } else {
      notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
    }
  };

  const { api: AddToCheckoutApi } = addToCheckoutApi();

  const { mutateAsync: addToCheckoutMutateAsync } = useMutation(
    AddToCheckoutApi,
    {
      onSuccess: () => {
        router.push(ROUTES.CHECKOUT);
      },
    },
  );

  const handleCheckout = () => {
    const data = {
      products: [
        {
          productDetails: _id,
          quantity: quantity,
        },
      ],
    };
    if (accessToken) {
      toast.promise(
        addToCheckoutMutateAsync(data),
        {
          success: "Product Added to checkout Successful",
          error: "Error Adding Product to checkout",
          loading: "Adding Product to checkout",
        },
        {
          id: "add-to-checkout-product",
        },
      );
    } else {
      const products = [
        {
          id: id,
          name: name,
          brand: brand,
          size: size,
          image: mainImageUrl,
          discountPrice: discountPrice,
          price: price,
          quantity: quantity,
          productCategory: productCategory,
        },
      ];
      handleAddToLocalCheckout(products);
      toast.success("Added for checkout");
      router.push(ROUTES.CHECKOUT);
    }
  };

  useEffect(() => {
    if (productDetails && wishlist?.length) {
      const isExist = wishlist.some(
        (single: SingleWishlistItem) => single.productId.id === id,
      );
      setIsAlreadyInWishlist(isExist);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productDetails, wishlist]);

  return (
    <div className="flex-1 px-1 text-black md:px-2">
      <div className="md:flex  md:flex-col md:justify-between">
        <span className="mb-2 text-xl font-bold text-primary md:text-2xl">
          {name ?? ""}
        </span>
        <SmallScreenComponent
          discountPrice={discountPrice}
          price={price}
          handleAddToCart={handleAddToCart}
          handleChangeQuantity={handleChangeQuantity}
          handleCheckout={handleCheckout}
          quantity={quantity}
          handleAddToWishlist={handleAddToWishlist}
        />

        <div className="flex w-full items-center gap-4 md:w-full lg:w-full xl:w-full">
          <div>
            <p className="text-md my-2 text-[#333333]"><span className="font-bold">Product Code: </span> {id}</p>
            <p className="text-md my-2 text-[#333333]">
              <span className="font-bold">Brand: </span>
              <Link
                href={ROUTES.BRANDS.VIEW(brandSlug)}
                className="text-md my-2 font-normal hover:text-blue-400 hover:underline"
              >
                {brand}
              </Link>
            </p>
            <p className="text-md my-2 text-[#333333]"><span className="font-bold">Origin:</span> {origin ? origin : "--"}</p>
            <p className="text-md my-2 text-[#333333]"><span className="font-bold">Size:</span> {size}</p>
            <p className="text-md my-2 text-[#333333]">
              <span className="font-bold">Category: </span>
              <Link
                className="text-md my-2 font-normal hover:text-blue-400 hover:underline"
                href={ROUTES.PRODUCTS.CATEGORY(categorySlug)}
              >
                {productCategory}
              </Link>
            </p>
            <p className="text-md my-2 text-[#333333]">
              <span className="font-bold">Status: </span>
              {isAvailable ? (
                <span className="text-md my-2 font-bold text-green-600">In Stock</span>
              ) : (
                <span className="text-md my-2 font-bold text-red-600">
                  Out Of Stock
                </span>
              )}
            </p>
          </div>
        </div>
        <div className="mt-2 hidden items-center gap-4 md:block md:gap-8">
          <div className="flex items-center gap-5">
            <h2 className="text-2xl font-semibold md:text-3xl text-secondary">
              ৳ {discountPrice}{" "}
              {price !== discountPrice ? (
                <span className="text-sm text-slate-500 line-through">
                  ৳{price}
                </span>
              ) : (
                ""
              )}
            </h2>
          </div>
          <div className="flex w-[150px] items-center  justify-between rounded-lg border-2 border-[#3E445A] bg-[#D9D9D9] mt-4">
            <button
              className="rounded-l-[5px] bg-[#092143] text-white px-4 py-2"
              onClick={() => handleChangeQuantity("decrease")}
            >
              -
            </button>
            <span className="mx-2 font-semibold">{quantity}</span>
            <button
              className="rounded-r-[5px] bg-[#092143] text-white px-4 py-2"
              onClick={() => handleChangeQuantity("increase")}
            >
              +
            </button>
          </div>
        </div>
        <div className="mt-4 hidden items-center gap-4 md:flex md:gap-2">
          <div>
            <button
              className="bg-primary  rounded-lg px-4 py-3 text-sm font-normal text-white shadow-lg disabled:cursor-not-allowed disabled:bg-gray-400"
              onClick={() => handleCheckout()}
              disabled={!isAvailable}
            >
              BUY NOW
            </button>
          </div>
          <div>
            <button
              className="flex w-full items-center gap-2 rounded-lg border border-primary px-[26px] py-3 text-sm font-medium text-primary shadow-lg disabled:cursor-not-allowed disabled:bg-gray-400"
              onClick={() => handleAddToCart()}
              disabled={!isAvailable}
            >
              <BsCartCheck className="text-lg text-primary" />
              ADD TO CART
            </button>
          </div>
        </div>
        {/* <div className="hidden items-center gap-5 pt-2 md:flex">
          <button
            onClick={() => console.log("clicked")}
            className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
          >
            <IoMdShare className="text-lg" />
            <span>Share</span>
          </button>
          <button
            onClick={() => console.log("clicked")}
            className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
          >
            <FaFacebook className="text-lg" />
          </button>
          <button
            onClick={() => console.log("clicked")}
            className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
          >
            <BsInstagram className="text-lg" />
          </button>
          <button
            onClick={() => console.log("clicked")}
            className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
          >
            <BsTwitter className="text-lg" />
          </button>
          <button
            onClick={() => console.log("clicked")}
            className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
          >
            <BsPinterest className="text-lg" />
          </button>
        </div> */}
        <p className="my-2 text-sm font-normal">{details}</p>
      </div>
    </div>
  );
};

export default ProductDetailsWithCartAndOtherIcons;

interface Small {
  discountPrice: number;
  price: number;
  handleChangeQuantity: (type: string) => void;
  quantity: number;
  handleAddToCart: () => void;
  handleCheckout: () => void;
  handleAddToWishlist: () => void;
}

const SmallScreenComponent = ({
  discountPrice,
  price,
  handleChangeQuantity,
  quantity,
  handleAddToCart,
  handleCheckout,
  handleAddToWishlist,
}: Small) => {
  return (
    <div className="block md:hidden">
      <div className="flex items-center gap-4">
        <h2 className="text-2xl font-semibold text-[#092143] ">
          ৳ {discountPrice}{" "}
          {price !== discountPrice ? (
            <span className="text-sm text-slate-500 line-through">
              ৳{price}
            </span>
          ) : (
            ""
          )}
        </h2>
        <div className="flex items-center justify-between rounded-lg border-2 border-[#3E445A] bg-[#D9D9D9]">
          <button
            className="rounded-l-[5px] bg-[#092143] text-white px-4 py-2"
            onClick={() => handleChangeQuantity("decrease")}
          >
            -
          </button>
          <span className="mx-2 font-semibold">{quantity}</span>
          <button
            className="rounded-r-[5px] bg-[#092143] text-white px-4 py-2"
            onClick={() => handleChangeQuantity("increase")}
          >
            +
          </button>
        </div>
      </div>
      <div className="mt-2 flex items-center gap-4">
        <button
          onClick={() => handleCheckout()}
          className="bg-tertiary  rounded-lg px-4 py-3 text-sm font-normal text-white shadow-lg"
        >
          Buy Now
        </button>
        <button
          className="flex items-center gap-2 rounded-lg border border-primary text-primary px-4 py-3 text-sm font-medium shadow-lg"
          onClick={() => handleAddToCart()}
        >
          <BsCartCheck />
          Add to Cart
        </button>
      </div>
      {/* <div className="flex items-center gap-5 pt-2">
        <button
          onClick={() => console.log("clicked")}
          className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
        >
          <IoMdShare className="text-lg" />
          <span>Share</span>
        </button>
        <button
          onClick={() => console.log("clicked")}
          className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
        >
          <FaFacebook className="text-lg" />
        </button>
        <button
          onClick={() => console.log("clicked")}
          className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
        >
          <BsInstagram className="text-lg" />
        </button>
        <button
          onClick={() => console.log("clicked")}
          className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
        >
          <BsTwitter className="text-lg" />
        </button>
        <button
          onClick={() => console.log("clicked")}
          className="flex items-center gap-1 rounded-lg py-1 text-sm font-medium text-slate-600 "
        >
          <BsPinterest className="text-lg" />
        </button>
      </div> */}
    </div>
  );
};
