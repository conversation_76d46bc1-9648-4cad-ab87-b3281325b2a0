"use client";
import CustomDropdownForNavbar from "@/Components/ReusableComponents/CustomDropdown/CustomDropdownForNavbar";
import SpinnerLoader from "@/Components/ReusableComponents/SpinnerLoader/SpinnerLoader";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { Option } from "@/Components/utils/filterOptionData";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleProductOfList } from "@/Types/productTypes";
import { UserHeaderResponse } from "@/Types/userTypes";
import { SingleBrand } from "@/services/bannerBrandCategoryServices";
import { getSearchProductListApi } from "@/services/productsServices";
import { useQuery } from "@tanstack/react-query";
import { signOut } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { redirect, useRouter, useSearchParams } from "next/navigation";
import React, { useContext, useEffect, useState } from "react";
import {
  AiFillHome,
  AiOutlineHeart,
  AiOutlineShoppingCart,
} from "react-icons/ai";
import { BiMessageRounded } from "react-icons/bi";
import { BsHandbag, BsSearch } from "react-icons/bs";
import { CiUser } from "react-icons/ci";
import { FaUserCircle } from "react-icons/fa";
import { HiArrowsUpDown } from "react-icons/hi2";
import { IoMenu } from "react-icons/io5";
import { MdFavoriteBorder } from "react-icons/md";
import logo from "../../../../src/dummyImages/logo.png";
import Sidebar from "./Sidebar";

const Navbar = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [openFilterSlider, setOpenFilterSlider] = useState<boolean>(false);
  const {
    localCartItems,
    userHeaderDetails,
    brands,
    categories,
  }: ContextDataType = useContext(LocalDataContext);
  const [showBrandsModal, setShowBrandsModal] = React.useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [callSearchApi, setCallSearchApi] = useState(false);
  const [showSearchResultModal, setShowSearchResultModal] = useState(false);
  const [delayTime, setDelayTime] = useState(2000);
  const [products, setProducts] = useState<SingleProductOfList[]>([]);
  const [categoriesList, setCategoriesList] = useState<Option[]>();

  const category = searchParams.get("category") || "";

  const { api: searchResultApi, getKey: searchResultKey } =
    getSearchProductListApi(searchQuery);

  const { data: searchResults, isLoading: isSearchItemsLoading } = useQuery(
    searchResultKey(),
    searchResultApi, // Pass the search query to your API function
    {
      refetchOnWindowFocus: false,
      enabled: callSearchApi && searchQuery.length ? true : false,
      onSuccess(data) {
        setShowSearchResultModal(true);
        setDelayTime(2000);
        setProducts(data.results);
      },
    },
  );
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchQuery = event.target.value;
    setSearchQuery(newSearchQuery);
    setTimeout(() => {
      setCallSearchApi(true);
    }, delayTime);
  };

  useEffect(() => {
    if (categories) {
      const brandsData = categories.map((single: SingleBrand) => {
        return {
          value: single.slug,
          label: single.name,
        };
      });
      setCategoriesList(brandsData);
    }
  }, [categories]);

  return (
    <>
      <nav className="bg-white px-4 py-1 md:px-6  lg:px-20 xl:px-28">
        <div className="hidden flex-row items-center justify-between md:flex">
          {/* Left side */}
          <div className="flex items-center">
            <div className="mr-4 text-white">
              <Link href={ROUTES.HOME}>
                <Image
                  src={logo}
                  alt="INNI"
                  height={100}
                  width={100}
                  className="h-[55px] w-[220px]"
                />
              </Link>
            </div>
          </div>

          {/* Middle (Search bar) */}
          <div className="flex flex-grow items-center justify-end md:justify-center">
            <div className="relative flex items-center rounded">
              <input
                type="text"
                placeholder="Search"
                className="w-[150px] rounded-l-lg border-none bg-[#F3F4F7]  px-4 py-2 text-black focus:outline-none md:w-[160px] xl:w-[320px]"
                onChange={(e) => {
                  setDelayTime(2000);
                  setShowSearchResultModal(true);
                  setCallSearchApi(false);
                  handleInputChange(e);
                }}
              />
              <div className="w-[155px] bg-[#F3F4F7] py-[10px]">
                <CustomDropdownForNavbar
                  options={categoriesList?.length ? categoriesList : []}
                  selectedValue={category ? category : ""}
                  onSelect={(e) => router.push(ROUTES.PRODUCTS.CATEGORY(e))}
                  width="155px"
                  placeholder="Select Category"
                />
              </div>
              <button
                name="searchButton"
                className="h-100 cursor-pointer rounded-r-[8px] bg-[#F28F4C] px-4 py-3 font-black text-white"
                disabled={searchQuery?.length ? false : true}
                onClick={() => {
                  setShowSearchResultModal(false);
                  router.push(ROUTES.SEARCH(searchQuery));
                }}
                aria-label="search-button"
              >
                <BsSearch />
              </button>
              {showSearchResultModal && searchQuery.length ? (
                <SearchProductsList
                  isSearchItemsLoading={isSearchItemsLoading}
                  products={products}
                  handleClose={() => {
                    setShowSearchResultModal(false);
                    setSearchQuery("");
                  }}
                />
              ) : (
                ""
              )}
            </div>
          </div>

          {/* Right side */}
          <div className="mt-4 hidden items-center gap-3 md:mt-0 md:flex xl:gap-4">
            {/* <Link href={ROUTES.WISHLIST}>
              <button
                className="relative mr-2 text-white"
                name="wishlistButton"
              >
                <AiOutlineHeart className="text-2xl text-[#092143]" />
                {userHeaderDetails?.wishlistCount ? (
                  <span className="bg-primary absolute right-[-15px] top-[-10px] rounded-full px-2 py-1 text-xs">
                    {userHeaderDetails?.wishlistCount}
                  </span>
                ) : (
                  ""
                )}
              </button>
            </Link> */}

            {userHeaderDetails?.userDetails ? (
              <div
                className="relative ml-2 lg:ml-0"
                onMouseEnter={() => setShowBrandsModal(true)}
                onMouseLeave={() => setShowBrandsModal(false)}
                onClick={() => setShowBrandsModal(true)}
                aria-label="details"
              >
                <button className="text-white " aria-label="search-button">
                  {userHeaderDetails?.userDetails?.profilePicture ? (
                    <Image
                      src={
                        userHeaderDetails?.userDetails?.profilePicture.includes(
                          "http",
                        )
                          ? userHeaderDetails?.userDetails?.profilePicture
                          : (generateProductImage(
                              userHeaderDetails?.userDetails?.profilePicture,
                            ) ?? "")
                      }
                      alt="profile"
                      className="rounded-full"
                      height={100}
                      width={100}
                      style={{ height: "30px", width: "30px" }}
                    />
                  ) : (
                    <>
                      {userHeaderDetails?.userDetails?.name ? (
                        <span className="flex h-8 w-8 items-center justify-center rounded-full border border-primary bg-white text-base font-bold text-black">
                          {userHeaderDetails?.userDetails?.name.slice(0, 1)}
                        </span>
                      ) : (
                        <FaUserCircle className="text-red-500" />
                      )}
                    </>
                  )}
                </button>
                {showBrandsModal && (
                  <UserMenuDropdown userDetails={userHeaderDetails} />
                )}
              </div>
            ) : (
              <Link href={ROUTES.LOG_IN("")} className="ml-2 lg:ml-0">
                <button
                  className="flex items-center gap-2"
                  aria-label="sign-in"
                >
                  <span className="rounded-full border border-primary px-2 py-2">
                    <CiUser className="text-lg text-primary" />
                  </span>
                </button>
              </Link>
            )}
            <div className="flex items-center gap-4">
              <Link href={ROUTES.WISHLIST}>
                <button
                  className="relative flex h-[35px] w-[35px] items-center justify-center rounded-full bg-[#E0EFFF] "
                  name="cartButton"
                  aria-label="cart"
                >
                  <MdFavoriteBorder className="text-lg text-[#23A6F0]" />
                  <span className="absolute right-[-8px] top-[-8px] flex h-5 w-5 items-center justify-center rounded-full bg-red-600 text-xs text-white">
                    {userHeaderDetails?.wishlistCount ?? 0}
                  </span>
                </button>
              </Link>
              <Link href={ROUTES.CART}>
                <button
                  className="relative flex h-[35px] w-[35px] items-center justify-center rounded-full bg-[#FFF1EE] "
                  name="wishButton"
                  aria-label="wish"
                >
                  <BsHandbag className="text-lg text-[#EA2B0F]" />
                  <span className="absolute right-[-8px] top-[-8px] flex h-5 w-5 items-center justify-center rounded-full bg-red-600 text-xs text-white">
                    {userHeaderDetails?.cartCount ??
                      localCartItems?.length ??
                      0}
                  </span>
                </button>
              </Link>
            </div>
            {/* <div className="flex items-center justify-center gap-2 rounded-lg bg-[#B91B8C] px-4 py-2 text-white">
              <button>
                <FaFacebook />
              </button>
              <button>
                <BsInstagram />
              </button>
              <button>
                <BsYoutube />
              </button>
            </div> */}
          </div>
        </div>
        <div className="block md:hidden">
          <div className="flex flex-row items-center justify-between md:hidden">
            {/* Left side */}
            <div className="flex w-full items-center">
              <div className="mr-4 text-white">
                <Link href={ROUTES.HOME}>
                  <Image
                    src={logo}
                    alt="INNI"
                    height={100}
                    width={100}
                    className="h-[45px] w-[180px]"
                  />
                </Link>
              </div>
            </div>
            <div className="flex w-full items-center justify-center">
              <div className="relative flex w-[95%] items-center rounded border border-primary">
                <input
                  type="text"
                  placeholder="Search"
                  className="w-full rounded-l-lg border-none bg-transparent px-4 py-1 text-black focus:outline-none  md:bg-[#F6F8FA]"
                  onChange={(e) => {
                    setDelayTime(2000);
                    setShowSearchResultModal(true);
                    setCallSearchApi(false);
                    handleInputChange(e);
                  }}
                />
                <button
                  name="searchButton"
                  aria-label="search"
                  className="h-100 rounded-r-[1px] bg-primary px-4 py-2 font-black text-white"
                  disabled={searchQuery?.length ? false : true}
                  onClick={() => {
                    setShowSearchResultModal(false);
                    router.push(ROUTES.SEARCH(searchQuery));
                  }}
                >
                  <BsSearch />
                </button>
                {showSearchResultModal && searchQuery.length ? (
                  <SearchProductsList
                    isSearchItemsLoading={isSearchItemsLoading}
                    products={products}
                    handleClose={() => {
                      setShowSearchResultModal(false);
                      setSearchQuery("");
                    }}
                  />
                ) : (
                  ""
                )}
              </div>
            </div>
            {/* <div className="flex w-full items-center">
              <button onClick={() => setOpenFilterSlider(true)}>
                <IoMenu size={30} />
              </button>
            </div> */}
            <div className="flex items-center justify-end">
              <button
                onClick={() => setOpenFilterSlider(true)}
                aria-label="menu"
              >
                <IoMenu size={30} />
              </button>
            </div>

            {/* Middle (Search bar) */}
          </div>
        </div>
      </nav>
      {/* bottom bar */}
      <div className="fixed bottom-[-3px] w-full border-t border-t-primary md:hidden">
        <div className="w-full bg-white px-4 pb-1 pt-2 shadow-lg">
          <div className="relative flex items-center justify-between">
            <Link href={ROUTES.HOME}>
              <button className="flex flex-col items-center" aria-label="home">
                <AiFillHome className="text-2xl" />
                <span className="text-[10px]">Home</span>
              </button>
            </Link>
            <Link href={ROUTES.WISHLIST}>
              <div className="flex translate-x-[-20px] flex-col items-center">
                <button className="relative" aria-label="wish">
                  <AiOutlineHeart className="text-2xl" />
                </button>
                <span className="text-[10px]">
                  Wishlist
                  {userHeaderDetails?.wishlistCount ? (
                    <span className="text-black">
                      ({userHeaderDetails?.wishlistCount})
                    </span>
                  ) : (
                    ""
                  )}
                </span>
              </div>
            </Link>
            <Link
              href={ROUTES.CART}
              className="absolute bottom-[14px] left-[50%] flex h-14 w-14 translate-x-[-50%] items-center justify-center rounded-full border-2 border-primary bg-secondary"
            >
              <div className="flex flex-col items-center">
                <button className="relative" aria-label="cart">
                  <AiOutlineShoppingCart className="text-2xl text-white" />
                </button>
                <span className="text-[10px] text-white">
                  Cart
                  {userHeaderDetails?.cartCount ? (
                    <span className=" text-gray-200">
                      ({userHeaderDetails?.cartCount})
                    </span>
                  ) : !userHeaderDetails && localCartItems?.length ? (
                    <span className="text-gray-200">
                      ({localCartItems?.length})
                    </span>
                  ) : (
                    ""
                  )}
                </span>
              </div>
            </Link>
            <Link href="http://m.me/INNI.com.bd">
              <button
                className="flex translate-x-[20px] flex-col items-center"
                aria-label="message"
              >
                <BiMessageRounded className="text-2xl" />
                <span className="text-[10px]">Message</span>
              </button>
            </Link>
            {/* this has hydration error */}
            <Link href="#">
              <button
                className="relative"
                onMouseEnter={() => setShowBrandsModal(true)}
                onMouseLeave={() => setShowBrandsModal(false)}
                onClick={() => {
                  if (userHeaderDetails?.userDetails?.name) {
                    setShowBrandsModal(!showBrandsModal);
                  } else {
                    router.push(ROUTES.LOG_IN(""));
                  }
                }}
                aria-label="log-in"
              >
                <div className="text-white ">
                  {userHeaderDetails?.userDetails?.profilePicture ? (
                    <Image
                      src={userHeaderDetails?.userDetails?.profilePicture ?? ""}
                      alt="profile"
                      className="rounded-full"
                      height={100}
                      width={100}
                      style={{ height: "30px", width: "30px" }}
                    />
                  ) : (
                    <>
                      {userHeaderDetails?.userDetails?.name ? (
                        <div className="flex flex-col items-center">
                          <span className="rounded-full bg-black px-2 py-1 text-xs font-bold text-white">
                            {userHeaderDetails?.userDetails?.name.slice(0, 1)}
                          </span>
                          <span className="text-[10px] text-black">
                            {
                              userHeaderDetails?.userDetails?.name?.split(
                                " ",
                              )[0]
                            }
                          </span>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center">
                          <FaUserCircle className="text-2xl text-black" />
                          <span className="text-[10px] text-black">User</span>
                        </div>
                      )}
                    </>
                  )}
                </div>
                {showBrandsModal && (
                  <UserMenuDropdownSmall userDetails={userHeaderDetails} />
                )}
              </button>
            </Link>
          </div>
        </div>
        <Sidebar
          openFilterSlider={openFilterSlider}
          setOpenFilterSlider={setOpenFilterSlider}
        />
      </div>
    </>
  );
};

export default Navbar;

const UserMenuDropdown = ({
  userDetails,
}: {
  userDetails: UserHeaderResponse;
}) => {
  return (
    <div className="absolute  w-40 border border-gray-300 bg-white p-4 shadow md:right-0 md:top-full">
      <div>
        <button className="mb-2 w-full border-b-2" aria-label="dashboard">
          <Link href={ROUTES.DASHBOARD.HOME}>Dashboard</Link>
        </button>
        <button className="mb-2 w-full border-b-2" aria-label="orders">
          <Link href={ROUTES.DASHBOARD.ORDERS.HOME}>Orders</Link>
        </button>
        <button className="mb-2 w-full border-b-2" aria-label="profile">
          <Link href={ROUTES.DASHBOARD.PROFILE}>Profile</Link>
        </button>
        {/* <button className="mb-2 w-full border-b-2">
          <Link href={ROUTES.DASHBOARD.SUPPORT.HOME}>Support</Link>
        </button> */}
        <button aria-label="log-out">
          {userDetails?.userDetails?.email ? (
            <button
              onClick={() => {
                document.cookie = `GMK=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                signOut();
                redirect(ROUTES.HOME);
              }}
              aria-label="log-out"
            >
              Log Out
            </button>
          ) : (
            <Link href={ROUTES.LOG_IN("")}>Log In</Link>
          )}
        </button>
      </div>
    </div>
  );
};

const UserMenuDropdownSmall = ({ userDetails }: { userDetails: any }) => {
  return (
    <div className="absolute bottom-[40px]  right-[-10px]   w-40 border border-gray-300 bg-white p-4 shadow">
      <div>
        <button className="mb-2 w-full border-b-2" aria-label="dashboard">
          <Link href={ROUTES.DASHBOARD.HOME}>Dashboard</Link>
        </button>
        <button className="mb-2 w-full border-b-2" aria-label="orders">
          <Link href={ROUTES.DASHBOARD.ORDERS.HOME}>Orders</Link>
        </button>
        <button className="mb-2 w-full border-b-2" aria-label="profile">
          <Link href={ROUTES.DASHBOARD.PROFILE}>Profile</Link>
        </button>
        {/* <button className="mb-2 w-full border-b-2">
          <Link href={ROUTES.DASHBOARD.SUPPORT.HOME}>Support</Link>
        </button> */}
        <button aria-label="log-out">
          {userDetails?.userDetails?.email ? (
            <button
              onClick={() => {
                document.cookie = `GMK=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                signOut();
                redirect(ROUTES.HOME);
              }}
              aria-label="log-out"
            >
              Log Out
            </button>
          ) : (
            <Link href={ROUTES.LOG_IN("")}>Log In</Link>
          )}
        </button>
      </div>
    </div>
  );
};

interface SearchListParams {
  isSearchItemsLoading: boolean;
  products: SingleProductOfList[];
  handleClose: () => void;
}

const SearchProductsList = ({
  isSearchItemsLoading,
  products,
  handleClose,
}: SearchListParams) => {
  return (
    <div
      className="absolute left-[-150px] top-[35px] z-50 w-[75vw]  rounded-lg border border-black bg-white p-2 shadow
     sm:left-[-179px] sm:w-[455px] md:left-[-100px] md:top-full md:w-[600px]  "
    >
      <div className="max-h-[500px] overflow-y-auto">
        {!isSearchItemsLoading ? (
          <>
            {products?.length ? (
              <div>
                {products?.map((singleProduct: SingleProductOfList) => (
                  <Link
                    href={ROUTES.PRODUCTS.VIEW(singleProduct?.slug)}
                    key={singleProduct.id}
                  >
                    <div
                      className="mb-1 flex gap-2 border-b-2 py-2 hover:bg-gray-200"
                      key={singleProduct.id}
                    >
                      <div>
                        <Image
                          src={generateProductImage(singleProduct.mainImageUrl)}
                          alt="profile"
                          height={100}
                          width={100}
                          style={{ height: "100px", width: "100px" }}
                        />
                      </div>
                      <div>
                        <p className="mb-2 text-sm font-semibold text-black">
                          {singleProduct.name}
                        </p>
                        <div className="flex items-center gap-4 py-1 font-semibold">
                          <p className="text-sm font-semibold">
                            Brand : {singleProduct.brand}
                          </p>
                          <p className="text-sm font-semibold">
                            Status :{" "}
                            {singleProduct.isAvailable
                              ? "In Stock"
                              : "out of stock"}
                          </p>
                        </div>
                        <div className="flex items-center gap-4">
                          <h2 className="text-sm font-semibold md:text-lg">
                            ৳ {singleProduct.discountPrice}{" "}
                            {singleProduct.price !==
                              singleProduct.discountPrice ? (
                              <span className="text-xs text-slate-500 line-through">
                                ৳{singleProduct.price}
                              </span>
                            ) : (
                              ""
                            )}
                          </h2>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div>
                <p>No result Found</p>
              </div>
            )}
          </>
        ) : (
          <SpinnerLoader />
        )}
      </div>
      <div className="p-2 text-center">
        <button
          className="rounded bg-primary px-4 py-1 font-bold text-white"
          onClick={() => handleClose()}
          aria-label="close"
        >
          Close
        </button>
      </div>
    </div>
  );
};
