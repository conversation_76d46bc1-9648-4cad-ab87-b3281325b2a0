"use client";
import ProductsListViewer from "@/Components/ReusableComponents/ProductsListViewer/ProductsListViewer";
import VerticalProductListViewer from "@/Components/ReusableComponents/ProductsListViewer/VerticleProductListViewer";
import TitleViewer from "@/Components/ReusableComponents/TitleViewer/TitleViewer";
import TitleViwerModern from "@/Components/ReusableComponents/TitleViewer/TitleViwerModern";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { SingleProductResponse } from "@/Types/productTypes";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { getRelatedProductListApi } from "@/services/productsServices";
import { useQuery } from "@tanstack/react-query";
import { useContext, useEffect } from "react";
import ProductsListCarousel from "../../ReusableComponents/ProductsListViewer/ProductListCarousel";
import LinkedProductPart from "./LinkedProductPart";
import ProductDescriptionTabs from "./ProductDescriptionTabs";
import ProductDetailsWithCartAndOtherIcons from "./ProductDetailsWithCartAndOtherIcons";
import SingleProductImageViewer from "./SingleProductImageViewer";

interface Props {
  productId: string;
  accessToken: string;
  productDetails: SingleProductResponse;
}
const SingleProductDetailsOverview = ({
  accessToken,
  productDetails,
}: Props) => {
  const { userHeaderDetails }: ContextDataType = useContext(LocalDataContext);
  const { api, getKey } = getRelatedProductListApi({
    slug: productDetails?.result?.slug,
    category: productDetails?.result?.productCategory,
    brand: productDetails?.result?.brand,
  });
  const { data: relatedData, isLoading: isRelatedProductsLoading } = useQuery(
    getKey(),
    api,
    {
      enabled: productDetails?.result?.slug ? true : false,
      refetchOnWindowFocus: false,
    },
  );

  useEffect(() => {
    if (productDetails) {
      const props = {
        event: "view_content",
        value: productDetails?.result?.discountPrice,
        user_log_in_status: userHeaderDetails?.userDetails?.name
          ? "logged in"
          : "not logged in",
        user_id: userHeaderDetails?.userDetails?._id ?? "",
        user_name: userHeaderDetails?.userDetails?.name ?? "",
        user_phone_number: userHeaderDetails?.userDetails?.mobileNumber ?? "",
        user_email: userHeaderDetails?.userDetails?.email ?? "",
        user_zip: userHeaderDetails?.userDetails?.zipCode ?? "",
        user_district: userHeaderDetails?.userDetails?.district ?? "",
        items: [
          {
            item_id: productDetails?.result?.id,
            item_name: productDetails?.result?.name,
            price: productDetails?.result?.discountPrice,
            item_brand: productDetails?.result?.brand,
            item_category: productDetails?.result?.productCategory,
            slug: productDetails?.result?.slug,
            sku: productDetails?.result?.id?.replace("GMK-", ""),
          },
        ],
      };
      gtmDataLayerDataPass(props);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productDetails]);

  return (
    <div className="mt-1">
      <div className="pb-4 pt-0">
        {/* top part  */}
        <div className="grid grid-cols-12 gap-2">
          <div className=" col-span-12 rounded-lg border-2 px-2 py-2 shadow-lg md:px-3 md:py-4 lg:col-span-9">
            <div className="grid grid-cols-12 gap-2">
              {/* Left Part */}
              <div className="col-span-12 sm:col-span-5 md:col-span-5 lg:col-span-5 xl:col-span-5 2xl:col-span-5">
                <SingleProductImageViewer
                  productDetails={productDetails.result}
                />
              </div>
              {/* Right Part */}
              <div className="col-span-12 sm:col-span-7 md:col-span-7 lg:col-span-7 xl:col-span-7 2xl:col-span-7">
                <ProductDetailsWithCartAndOtherIcons
                  productDetails={productDetails?.result}
                  accessToken={accessToken}
                />
              </div>
            </div>
            <div className="mt-2">
              {productDetails?.result?.linkedProduct?.slug ? (
                <LinkedProductPart
                  productDetails={productDetails?.result}
                  accessToken={accessToken}
                />
              ) : (
                ""
              )}
            </div>
          </div>
          <div className="col-span-3 hidden lg:block">
            <div className="border-b border-primary mb-3">
              <div className="bg-primary px-2 py-1 inline-block rounded-tr-[10px]">
                <h2 className="uppercase text-sm text-white">Related Product</h2>
              </div>
            </div>
            <VerticalProductListViewer
              productList={
                productDetails?.result?.relatedProducts?.length
                  ? productDetails?.result?.relatedProducts
                  : relatedData?.result.relatedProducts
              }
              loading={
                productDetails?.result?.relatedProducts?.length
                  ? false
                  : isRelatedProductsLoading
              }
              accessToken={accessToken}
            />
          </div>
        </div>
        {/* Bottom Part - Tabs */}
        <ProductDescriptionTabs productDetails={productDetails} />
      </div>
      <div className="hidden md:block">
        <div className="py-4">
          <TitleViwerModern
            titleTwo="Same Brand"
            titleOne="From"
            seeAllButton={false}
          />
          <ProductsListViewer
            productList={relatedData?.result.sameBrandProducts}
            loading={isRelatedProductsLoading}
            accessToken={accessToken}
            xxl={6}
            xl={5}
            lg={4}
            md={3}
            sm={2}
          />
        </div>
      </div>
      <div className="block md:hidden">
        <div className="py-4">
          <TitleViewer
            titleTwo="Related Products"
            titleOne=""
            seeAllButton={false}
          />
          <ProductsListCarousel
            productList={
              productDetails?.result?.relatedProducts?.length
                ? productDetails?.result?.relatedProducts
                : relatedData?.result.relatedProducts
            }
            loading={
              productDetails?.result?.relatedProducts?.length
                ? false
                : isRelatedProductsLoading
            }
            accessToken={accessToken}
          />
        </div>
        <div className="py-4">
          <TitleViewer
            titleTwo="From Same Brand"
            titleOne=""
            seeAllButton={false}
          />
          <ProductsListCarousel
            productList={productDetails?.result?.relatedProducts}
            loading={isRelatedProductsLoading}
            accessToken={accessToken}
          />
        </div>
      </div>
    </div>
  );
};

export default SingleProductDetailsOverview;
