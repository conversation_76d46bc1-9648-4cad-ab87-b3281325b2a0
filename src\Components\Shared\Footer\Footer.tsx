// Components/Footer.js

import { ROUTES } from "@/Routes";
import Image from "next/image";
import Link from "next/link";
import { BiPhone } from "react-icons/bi";
import { BsInstagram, BsYoutube } from "react-icons/bs";
import { FaFacebook } from "react-icons/fa";
import { IoLocation } from "react-icons/io5";
import { MdEmail } from "react-icons/md";
import logo2 from "../../../../src/dummyImages/logo2.webp";
import { shopAddress } from "@/Components/utils/filterOptionData";

const Footer = () => {
  return (
    <footer className="bg-[#F7F7F7] pb-2 pt-4 md:pt-8">
      <div className=" w-full px-4 md:px-8">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
          {/* Section 1 */}
          <div className="text-black">
            <Image
              src={logo2}
              alt="INNI"
              height={100}
              width={100}
              // className="h-[80px] w-[180px]"
            />
            <div className="flex flex-col gap-[10px]">
              <div className="flex items-start gap-2">
                <span className="mt-4 text-sm font-semibold">
                  Skin Improves Confidence
                </span>
              </div>
            </div>
          </div>

          {/* Section 2 */}
          <div className="text-black">
            <h2 className="mb-2 w-[75%] border-b-2 pb-1 text-lg font-semibold">
              Most popular Categories
            </h2>
            <div className="flex flex-col gap-2">
              <Link
                href={ROUTES.HOME}
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
              >
                Home
              </Link>
              <Link
                href={ROUTES.PRODUCTS.HOME}
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
              >
                Products
              </Link>
              <Link
                href={ROUTES.BLOGS.HOME}
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
              >
                Blogs
              </Link>
              <Link
                href={ROUTES.SITEMAP.HOME}
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
              >
                Sitemap
              </Link>
              <Link
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
                href={ROUTES.FAQ}
              >
                FAQ
              </Link>
            </div>
          </div>

          {/* Section 3 */}
          <div className="text-black">
            <h2 className="mb-2 border-b-2 pb-1 text-lg font-semibold">
              Customer Services
            </h2>
            <div className="flex flex-col gap-2">
              <Link
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
                href={ROUTES.ABOUT_US}
              >
                About Us
              </Link>
              <Link
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
                href={ROUTES.CONTACT_US}
              >
                Contact Us
              </Link>

              <Link
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
                href={ROUTES.RETURN_AND_REFUND}
              >
                Return And Refund
              </Link>
              <Link
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
                href={ROUTES.SHIPPING_AND_DELIVERY}
              >
                Shipping And Delivery
              </Link>
              <Link
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
                href={ROUTES.TERMS_AND_CONDITIONS}
              >
                Terms And Conditions
              </Link>
              <Link
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
                href={ROUTES.PRIVACY_POLICY}
              >
                Privacy Policy
              </Link>
              <Link
                className="transform cursor-pointer transition-all duration-700 ease-in-out hover:scale-110 hover:text-black"
                href={ROUTES.STORE_LOCATION}
              >
                Store Location
              </Link>
            </div>
          </div>

          {/* Section 4 */}
          <div className="text-black">
            <h2 className="mb-2 border-b-2 pb-1 text-lg font-semibold">
              Contact Us
            </h2>
            <div className="flex flex-col gap-[10px]">
              <div className="flex items-center gap-2">
                <BiPhone className="text-xl" />
                <a
                  href="tel:+8801303779646"
                  target="_blank"
                  className="transform text-sm font-semibold transition-all duration-700 ease-in-out hover:scale-110 hover:text-black hover:underline"
                >
                  01303-779646
                </a>
              </div>
              <div className="flex items-center gap-2">
                <MdEmail className="text-xl" />
                <a
                  href="mailto:<EMAIL>"
                  target="_blank"
                  className="transform text-sm font-semibold transition-all duration-700 ease-in-out hover:scale-110 hover:text-black hover:underline"
                >
                  koreantrendymall.com
                </a>
              </div>
              <div className="flex items-start gap-2">
                <div>
                  <IoLocation className="text-xl" size={20} />
                </div>
                <span className="text-sm font-semibold">{shopAddress}</span>
              </div>
            </div>
          </div>
          <div className="text-black">
            <h2 className="mb-2 border-b-2 pb-1 text-lg font-semibold">
              Social
            </h2>
            <div className="flex items-center gap-4 py-4">
              <a
                href="https://www.facebook.com/koreanshopsBangladesh"
                target="_blank"
                className="transform no-underline transition-all duration-700 ease-in-out hover:scale-110"
              >
                <FaFacebook className="text-2xl" />
              </a>
              <a
                href="https://www.youtube.com/@koreanshopbangladesh"
                target="_blank"
                className="transform no-underline transition-all duration-700 ease-in-out hover:scale-110"
              >
                <BsYoutube className="text-2xl" />
              </a>
              <a
                href="https://www.instagram.com/koreanshopbd/?hl=en"
                target="_blank"
                className="transform no-underline transition-all duration-700 ease-in-out hover:scale-110"
              >
                <BsInstagram className="text-2xl" />
              </a>
            </div>
          </div>
        </div>
      </div>
      <hr />
      <div className="pt-4 text-center text-black">
        © {new Date().getFullYear()} All rights reserved. Korean Shop
        Bangladesh.
      </div>
      <div className="pb-12 pt-4 text-center text-black md:pb-0">
        <span>Developed By</span>
        <Link
          href="https://missiononlinebd.com/"
          target="_blank"
          className="ml-2 hover:text-blue-600 hover:underline"
        >
          Mission Online
        </Link>
      </div>
    </footer>
  );
};

export default Footer;

