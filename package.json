{"name": "ksbd-fontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.17", "@next/third-parties": "^14.2.3", "@react-pdf/renderer": "^3.3.5", "@tanstack/react-query": "^4.33.0", "@tanstack/react-query-devtools": "^4.22.4", "@tanstack/react-table": "^8.7.6", "@types/js-cookie": "^3.0.6", "@types/node": "20.4.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-inner-image-zoom": "^3.0.0", "autoprefixer": "10.4.14", "axios": "^1.4.0", "eslint": "8.45.0", "eslint-config-next": "^15.3.4", "formik": "^2.4.3", "framer-motion": "^10.15.0", "iron-session": "^6.3.1", "js-cookie": "^3.0.5", "next": "15.3.4", "next-auth": "^4.23.0", "postcss": "8.4.27", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.10.1", "react-iframe": "^1.8.5", "react-inner-image-zoom": "^3.0.2", "react-intersection-observer": "^9.5.2", "react-loading-skeleton": "^3.3.1", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-responsive-carousel": "^3.2.23", "sharp": "^0.33.5", "sweetalert2": "^11.7.22", "swiper": "^10.3.1", "tailwindcss": "3.3.3", "typescript": "5.1.6", "yup": "^1.2.0"}, "devDependencies": {"@types/swiper": "^6.0.0", "prettier": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11"}}