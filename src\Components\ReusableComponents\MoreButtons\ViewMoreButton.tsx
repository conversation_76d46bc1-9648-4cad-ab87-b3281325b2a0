import { FiArrowUpRight } from "react-icons/fi";

interface Props {
  handleClick?: () => void;
}

const ViewMoreButton = ({ handleClick }: Props) => {
  return (
    <div className="mt-4 flex items-center justify-center sm:mt-6 lg:mt-8">
      <button
        className=" hover:bg-secondary border-secondary hover:border-ternary flex items-center gap-2 rounded border-b-2 bg-white px-4 py-2 text-base font-semibold shadow transition-all delay-75 hover:text-white"
        onClick={handleClick}
      >
        <span>View All</span>
        <span>
          <FiArrowUpRight />
        </span>
      </button>
    </div>
  );
};

export default ViewMoreButton;
