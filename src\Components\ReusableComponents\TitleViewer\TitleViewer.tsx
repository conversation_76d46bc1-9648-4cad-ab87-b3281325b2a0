import { tenor } from "@/utils/fonts";
import { FaChevronRight } from "react-icons/fa";
import ScrollRevealWrapper from "../ScrollRevealWrapper/ScrollRevealWrapper";

interface Props {
  titleOne: string;
  titleTwo: string;
  seeAllButton: boolean;
  handleClick?: () => void;
}

const TitleViewer = ({
  titleOne,
  titleTwo,
  seeAllButton,
  handleClick,
}: Props) => {
  return (
    <ScrollRevealWrapper>
      {/* <div className="flex items-center justify-center text-center pb-4 md:pb-8">
        <div className="border-t border-slate-400 flex-grow mx-4"></div>
        <h2 className="text-xl md:text-3xl font-semibold ">{titleOne}</h2>
        <div className="border-t border-slate-400 flex-grow mx-4"></div>
      </div> */}
      <div className="flex items-center justify-between pb-4 text-center md:pb-8">
        <div className="flex gap-2 border-b-2 border-primary pb-1 pr-8">
          <h2
            className={`${tenor.className} text-sm font-bold md:text-xl lg:text-2xl xl:text-2xl`}
          >
            {titleOne}
          </h2>
          <h2
            className={`${tenor.className} text-sm font-semibold text-[#092143] md:text-xl lg:text-2xl xl:text-2xl`}
          >
            {titleTwo}
          </h2>
        </div>
        {seeAllButton ? (
          <button
            className="flex items-center gap-2 text-sm font-normal hover:text-[#092143]"
            onClick={handleClick}
          >
            <span>View All</span> <FaChevronRight className="text-[#092143]" />
          </button>
        ) : (
          ""
        )}
      </div>
    </ScrollRevealWrapper>
  );
};

export default TitleViewer;
