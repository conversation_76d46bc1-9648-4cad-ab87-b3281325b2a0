import BuyNowPage from "@/Components/BuyNowPage/BuyNowPage";
import SingleProductDetailsOverview from "@/Components/SingleProductDetails/SingleProductDetailsOverview/SingleProductDetailsOverview";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import ShopLayout from "@/Layouts/ShopLayout";
import { SingleProductResponse } from "@/Types/productTypes";
import { BASE_URL } from "@/environment/environment";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";
import React from "react";

interface Props {
  params: Promise<{ slug: string }>;
}

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug } = await params;
  const data: SingleProductResponse = await getData(slug);
  return {
    title: data?.result?.name,
    description: data?.result?.details,
    openGraph: {
      title: data?.result?.name,
      description: data?.result?.details,
      images: [generateProductImage(data?.result?.mainImageUrl)],
    },
  };
};

const ProductDetailsPage = async ({ params }: Props) => {
  const { slug } = await params;
  const data: SingleProductResponse = await getData(slug);
  const accessToken = (await cookies()).get("GMK")?.value;

  return (
    <ShopLayout>
      <BuyNowPage
        accessToken={accessToken ? accessToken : ""}
        productDetails={data?.result}
      />
    </ShopLayout>
  );
};

export default ProductDetailsPage;

async function getData(productId: string) {
  const res = await fetch(`${BASE_URL}/products/single/${productId}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}
