import { BASE_URL } from "@/environment/environment";
import ShopLayout from "@/Layouts/ShopLayout";
import { Metadata } from "next";
import { notFound } from "next/navigation";
export const metadata: Metadata = {
  title: "Privacy Policy | INNI",
};
const PrivacyPolicy = async () => {
  const data = await getData();
  return (
    <ShopLayout>
      <div
        dangerouslySetInnerHTML={{
          __html: data?.result?.content
            ? data?.result?.content
            : "<p>Not Fount</p>",
        }}
      />
    </ShopLayout>
  );
};

export default PrivacyPolicy;

async function getData() {
  const res = await fetch(`${BASE_URL}/content/privacy`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
  }

  return res.json();
}
