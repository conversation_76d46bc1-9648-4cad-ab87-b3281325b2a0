import {
  <PERSON><PERSON>,
  <PERSON>_<PERSON>_<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>_<PERSON>,
} from "next/font/google";

export const lato = Lato({
  subsets: ["latin"],
  weight: ["100", "300", "400", "700", "900"],
  display: "swap",
});

export const tenor = Tenor_Sans({
  subsets: ["latin"],
  weight: ["400"],
  display: "swap",
});

export const saint = Mrs_<PERSON>_Del<PERSON>ield({
  subsets: ["latin"],
  weight: ["400"],
  display: "swap",
});
export const poppins = Poppins({
  subsets: ["latin"],
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});
