"use client";
import { SingleProductResponse } from "@/Types/productTypes";
import { useState } from "react";
import AdditionalInfoTab from "../SingleProductDetailsWidgets/AdditionalInfoTab";
import DeliveryTab from "../SingleProductDetailsWidgets/DeliveryTab";
import DescriptionTab from "../SingleProductDetailsWidgets/DescriptionTab";
import ReviewTab from "../SingleProductDetailsWidgets/ReviewTab";

interface Props {
  productDetails: SingleProductResponse;
}

const ProductDescriptionTabs = ({ productDetails }: Props) => {
  const [selectedTab, setSelectedTab] = useState<string>("DESCRIPTION");
  return (
    <div className="mt-8 rounded-lg border-2 pt-4 text-black shadow-lg md:p-4">
      <div className="mb-4">
        <div className="flex flex-wrap items-center gap-2 border-b border-secondary">
          {[
            "DESCRIPTION",
            "INGREDIENTS",
            "HOW TO USE",
            // "BANGLA",
            "REVIEWS",
          ].map((singleTab: string, index: number) => (
            <button
              key={singleTab}
              className={`rounded-t-md px-2 py-2 text-[10px] font-semibold md:text-sm ${
                selectedTab === singleTab
                  ? "bg-secondary text-white"
                  : "hover:border-blue-500 hover:text-[#092143]"
              } ${index === 0 && "rounded-t-md"} ${index === 3 && ""}`}
              onClick={() => setSelectedTab(singleTab)}
            >
              {singleTab}
            </button>
          ))}
        </div>
      </div>

      <div className="px-2">
        {selectedTab === "DESCRIPTION" ? (
          <DescriptionTab
            description={productDetails?.result?.productDescription}
          />
        ) : selectedTab === "INGREDIENTS" ? (
          <AdditionalInfoTab productDetails={productDetails?.result} />
        ) : selectedTab === "HOW TO USE" ? (
          <div
            dangerouslySetInnerHTML={{
              __html: productDetails?.result?.howToUse
                ? productDetails?.result?.howToUse
                : "<p>No Description</p>",
            }}
          />
        ) : selectedTab === "BANGLA" ? (
          <div
            dangerouslySetInnerHTML={{
              __html: productDetails?.result?.banglaDescription
                ? productDetails?.result?.banglaDescription
                : "<p>No Description</p>",
            }}
          />
        ) : selectedTab === "REVIEWS" ? (
          <ReviewTab />
        ) : (
          <DeliveryTab />
        )}
      </div>
    </div>
  );
};

export default ProductDescriptionTabs;
