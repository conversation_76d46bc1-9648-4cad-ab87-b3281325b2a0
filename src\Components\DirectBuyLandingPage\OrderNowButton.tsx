"use client";
const OrderNowButton = () => {
  const handleClick = () => {
    const orderFormDiv = document?.getElementById("order-form");
    if (orderFormDiv) {
      orderFormDiv?.scrollIntoView({ behavior: "smooth" });
    }
  };
  return (
    <div>
      <button
        className="block w-full rounded bg-[#092143] px-4 py-2 text-xl font-bold text-white shadow-2xl sm:hidden lg:text-3xl"
        /* style={{
          background: "linear-gradient(135deg, #F8914D 0%, #580600 100%)",
        }} */
        onClick={handleClick}
        type="button"
      >
        অর্ডার করুন
      </button>
      <button
        className="hidden rounded bg-[#092143] px-4 py-2 text-xl font-bold text-white shadow-2xl sm:block lg:text-3xl"
        /* style={{
          background: "linear-gradient(135deg, #F8914D 0%, #580600 100%)",
        }} */
        onClick={handleClick}
        type="button"
      >
        অর্ডার করুন
      </button>
    </div>
  );
};
export default OrderNowButton;
