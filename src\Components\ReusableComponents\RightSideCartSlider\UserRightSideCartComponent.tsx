import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import {
  deleteModal,
  maximumQuantityModal,
  minimumQuantityModal,
  notLoggedInModal,
} from "@/Components/utils/commonModal";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleCartItem } from "@/Types/cartPageTypes";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { removeFromCart } from "@/services/cartServices";
import { addToCheckoutApi } from "@/services/checkoutServices";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { BiTrash } from "react-icons/bi";
import { FaChevronRight } from "react-icons/fa";

interface Props {
  setOpenCartSlider: (e: any) => void;
  accessToken: string;
}

const UserRightSideCartComponent = ({
  accessToken,
  setOpenCartSlider,
}: Props) => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const { cartItems, userHeaderDetails }: ContextDataType =
    useContext(LocalDataContext);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [updatedCartItems, setUpdatedCartItems] = useState<SingleCartItem[]>();
  const [changedCount, setChangedCount] = useState<number>(0);

  const handleUpdateQuantity = (
    product: SingleCartItem,
    newQuantity: number,
  ) => {
    if (updatedCartItems) {
      const arr = updatedCartItems;
      const ind = arr.indexOf(product);
      arr[ind].quantity = newQuantity;
      setUpdatedCartItems(arr);
      setChangedCount(changedCount + 1);
    }
  };

  useEffect(() => {
    if (cartItems?.length) {
      setUpdatedCartItems(cartItems);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cartItems]);

  useEffect(() => {
    let total = 0;
    if (updatedCartItems?.length) {
      updatedCartItems?.map(
        (single: SingleCartItem) =>
          (total += single.discountPrice * single.quantity),
      );
    }
    if (updatedCartItems?.length) {
      const data = {
        event: "view_cart",
        value: total,
        user_log_in_status: userHeaderDetails?.userDetails?.name
          ? "logged in"
          : "not logged in",
        user_id: userHeaderDetails?.userDetails?._id ?? "",
        user_name: userHeaderDetails?.userDetails?.name ?? "",
        user_phone_number: userHeaderDetails?.userDetails?.mobileNumber ?? "",
        user_email: userHeaderDetails?.userDetails?.email ?? "",
        user_zip: userHeaderDetails?.userDetails?.zipCode ?? "",
        user_district: userHeaderDetails?.userDetails?.district ?? "",
        items: updatedCartItems?.length
          ? updatedCartItems.map((single: SingleCartItem) => {
              return {
                item_id: single?.productId,
                item_name: single?.name,
                price: single?.discountPrice,
                item_brand: single?.brand,
                item_category: single?.productCategory,
                // slug: single?.slug,
                sku: single?.productId?.replace("GMK-", ""),
              };
            })
          : null,
      };
      gtmDataLayerDataPass(data);
    }
    setTotalAmount(total);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updatedCartItems, changedCount]);

  const { api: AddToCheckoutApi } = addToCheckoutApi();

  const { mutateAsync: addToCheckoutMutateAsync, isLoading } = useMutation(
    AddToCheckoutApi,
    {
      onSuccess: () => {
        setOpenCartSlider(false);
        router.push(ROUTES.CHECKOUT);
      },
    },
  );

  const handleCheckout = () => {
    if (updatedCartItems) {
      const data = {
        products: updatedCartItems?.map((single: SingleCartItem) => {
          return {
            productDetails: single?.productId,
            quantity: single?.quantity,
          };
        }),
      };
      if (accessToken) {
        toast.promise(
          addToCheckoutMutateAsync(data),
          {
            success: "Product Added to checkout Successful",
            error: "Error Adding Product to checkout",
            loading: "Adding Product to checkout",
          },
          {
            id: "add-to-checkout-product",
          },
        );
      } else {
        notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
      }
    }
  };

  const { api: DeleteFromCartApi } = removeFromCart();

  const { mutateAsync: removeFromCartMutateAsync } = useMutation(
    DeleteFromCartApi,
    {
      onSuccess: () => {
        queryClient.invalidateQueries();
      },
    },
  );
  const handleDeleteFromCart = (id: string) => {
    if (accessToken) {
      deleteModal("Cart", () =>
        toast.promise(
          removeFromCartMutateAsync(id),
          {
            success: "Product removed from cart Successful",
            error: "Error removing Product from cart",
            loading: "Removing Product from cart",
          },
          {
            id: "remove-from-checkout",
          },
        ),
      );
    } else {
      notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
    }
  };

  return (
    <div>
      <div className="h-[82vh] overflow-y-auto px-4">
        {updatedCartItems?.length ? (
          <>
            {updatedCartItems?.map((single: SingleCartItem) => (
              <div key={single.productId} className="mb-2">
                <div className="grid grid-cols-12 items-start  gap-2 rounded-t-lg bg-white p-2">
                  <div className="col-span-2">
                    <Image
                      height={100}
                      width={100}
                      src={generateProductImage(single.image)}
                      alt="image"
                      className="h-[60px] w-[60px] rounded"
                    />
                  </div>
                  <div className="col-span-10  items-start">
                    <div className="flex items-start justify-between gap-4">
                      <span className="text-xs md:text-sm">{single.name}</span>
                      <button
                        onClick={() => handleDeleteFromCart(single?._id)}
                        className="hover:text-red-500"
                      >
                        <BiTrash className="cursor-pointer hover:text-red-500" />
                      </button>
                    </div>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-sm font-semibold">
                        ৳ {single.discountPrice}
                      </span>
                      <div>
                        <div className="flex w-[100px] items-center justify-between rounded-lg border-2 border-primary md:w-[120px]">
                          <button
                            className="rounded-l-lg bg-gray-200 px-2 md:px-4"
                            onClick={() => {
                              if (single?.quantity > 1) {
                                handleUpdateQuantity(
                                  single,
                                  single?.quantity - 1,
                                );
                              } else {
                                minimumQuantityModal();
                              }
                            }}
                          >
                            -
                          </button>
                          <span className="mx-1 text-xs md:text-sm">
                            {single.quantity}
                          </span>
                          <button
                            className="rounded-r-lg bg-gray-200 px-2 md:px-4"
                            onClick={() => {
                              if (single?.quantity < 5) {
                                handleUpdateQuantity(
                                  single,
                                  single?.quantity + 1,
                                );
                              } else {
                                maximumQuantityModal();
                              }
                            }}
                          >
                            +
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-1 w-full rounded-b-lg bg-white px-2 text-right">
                  <span className="text-xs font-semibold md:text-sm">
                    Subtotal : ৳ {single.discountPrice * single.quantity}
                  </span>
                </div>
              </div>
            ))}
          </>
        ) : (
          <p>No Cart Item Found</p>
        )}
      </div>
      <div className="mt-4 flex w-full items-center justify-between bg-white px-4 py-2">
        <div>
          <p className="text-xs font-semibold md:text-sm">Cart Total</p>
          <p className="text-xs font-semibold text-[#092143] md:text-sm">
            ৳ {totalAmount}
          </p>
        </div>
        <div>
          <button
            className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white disabled:bg-gray-400"
            onClick={() => handleCheckout()}
            disabled={cartItems?.length === 0 || isLoading}
          >
            <p className="text-xs md:text-sm">Proceed</p>
            <FaChevronRight />
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserRightSideCartComponent;
