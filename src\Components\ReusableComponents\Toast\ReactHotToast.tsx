import classNames from "classnames";
import React from "react";
import { Toaster, ToastBar } from "react-hot-toast";
import {
  FiAlertCircle,
  FiAlertTriangle,
  FiCheckCircle,
  FiLoader,
} from "react-icons/fi";

const ReactHotToast = () => {
  return (
    <Toaster position="top-right">
      {(toast) => (
        <ToastBar
          toast={toast}
          style={{
            padding: 0,
            minWidth: 200,
            maxWidth: 412,
            boxShadow:
              "0px 10px 15px -3px rgba(0, 0, 0, 0.1), 0px 4px 6px -2px rgba(0, 0, 0, 0.05)",
          }}
        >
          {({ icon, message }) => (
            <div
              className={classNames(
                "bzbtoast",
                toast.type == "blank"
                  ? "toastblank"
                  : toast.type == "error"
                    ? "toasterror"
                    : toast.type == "success"
                      ? "toastsuccess"
                      : toast.type == "loading"
                        ? "toastloading"
                        : "",
              )}
            >
              {toast.type === "blank" ? (
                <span className="bzbtoastsize">
                  <FiAlertTriangle
                    className="bzbtoasticon"
                    aria-hidden="true"
                  />
                </span>
              ) : null}
              {toast.type === "success" ? (
                <span className="bzbtoastsize">
                  <FiCheckCircle className="bzbtoasticon" aria-hidden="true" />
                </span>
              ) : null}
              {toast.type === "error" ? (
                <span className="bzbtoastsize">
                  <FiAlertCircle className="bzbtoasticon" aria-hidden="true" />
                </span>
              ) : null}
              {toast.type === "loading" ? (
                <span className="bzbtoastsize">
                  <FiLoader className="toastloadingicon" aria-hidden="true" />
                </span>
              ) : null}
              {message}
            </div>
          )}
        </ToastBar>
      )}
    </Toaster>
  );
};

export default ReactHotToast;
