import RightSideCartIcon from "@/Components/ReusableComponents/RightSideCartIcon/RightSideCartIcon";
import FooterModern from "@/Components/Shared/Footer/FooterModern";
import LowerNavbar from "@/Components/Shared/Header/LowerNavbar";
import Navbar from "@/Components/Shared/Header/Navbar";
import UpperNavbar from "@/Components/Shared/Header/UpperNavbar";
import GetCustomerTrackUTMSource from "@/utils/CustomerPlatformSourceTrack";
import { cookies } from "next/headers";

export default async function HomepageLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = await cookies();
  const accessToken = cookieStore.get("GMK")?.value;
  return (
    <section>
      <div style={{ position: "fixed", width: "100%", zIndex: 99 }}>
        <UpperNavbar />
        <Navbar />
        <LowerNavbar />
      </div>
      <div
        className="fixed right-0 top-[300px] md:top-[400px]"
        style={{ zIndex: 99 }}
      >
        <RightSideCartIcon accessToken={accessToken} />
      </div>
      <div
        className="w-100 mx-auto min-h-[95vh] px-0 py-4 pt-[140px]  text-black sm:pt-[175px] md:pt-[210px]  lg:pt-[210px] xl:pt-[190px] bg-[#FAFAFA]"
      >
        {children}
      </div>
      <div>
        <FooterModern />
      </div>

      {/* <div className="hidden md:block">
        <FacebookMessenger />
      </div> */}
      <GetCustomerTrackUTMSource />
    </section>
  );
}
