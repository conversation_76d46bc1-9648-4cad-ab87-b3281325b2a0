import noProductFound from "@/dummyImages/no-product-found.svg";
import noFound from "@/dummyImages/not-found.svg";
import { ROUTES } from "@/Routes";
import Image from "next/image";
import { useRouter } from "next/navigation";

export const NoWishlistFound = () => {
  const router = useRouter();
  return (
    <div
      className="flex flex-col items-center justify-center"
      style={{ minHeight: "60vh" }}
    >
      <Image
        height={100}
        width={100}
        src={noFound}
        // src="https://behalacollege.in/display_board/assets/images/empty-wishlist.png"
        alt=" wishlist"
        className="h-[350px] w-3/4 md:h-[400px]"
      />
      <button
        className="mt-5 rounded-2xl bg-secondary p-4 text-white"
        onClick={() => router.push(ROUTES.PRODUCTS.HOME)}
      >
        Shop More
      </button>
    </div>
  );
};
export const NoCartItemFound = () => {
  const router = useRouter();
  return (
    <div
      className="flex flex-col items-center justify-center"
      style={{ minHeight: "60vh" }}
    >
      <Image
        height={100}
        width={100}
        src={noFound}
        // src="https://cdni.iconscout.com/illustration/premium/thumb/empty-cart-2130356-1800917.png"
        alt="cart"
        className="h-[350px] w-3/4 md:h-[400px]"
      />
      <button
        className="mt-5 rounded-2xl bg-secondary p-4 text-white"
        onClick={() => router.push(ROUTES.PRODUCTS.HOME)}
      >
        Shop More
      </button>
    </div>
  );
};

export const NoProductsFound = () => {
  return (
    <div
      className="flex flex-col items-center justify-center"
      // style={{ minHeight: "60vh", maxHeight: "70vh" }}
    >
      <Image
        height={100}
        width={100}
        src={noProductFound}
        // src="https://cdni.iconscout.com/illustration/premium/thumb/product-is-empty-8044861-6430770.png?f=webp"
        alt=""
        className="h-[300px] w-[300px] object-cover"
      />
      <p className="text-lg font-semibold text-red-600">No Products Found</p>
    </div>
  );
};
