"use client";
import { Option, productTypeList } from "@/Components/utils/filterOptionData";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import {
  SingleCategory,
  SingleSubCategory,
} from "@/services/bannerBrandCategoryServices";
import { Dialog, Transition } from "@headlessui/react";
import Link from "next/link";
import { Fragment, useContext, useState } from "react";
import { AiOutlineDown, AiOutlineRight } from "react-icons/ai";
import { MdClose } from "react-icons/md";
// import ProductFilterOptions from "../ProductFilterOptions/ProductFilterOptions";

const Sidebar = ({ openFilterSlider, setOpenFilterSlider }: any) => {
  return (
    <Transition.Root show={openFilterSlider} as={Fragment}>
      <Dialog
        as="div"
        className="relative"
        onClose={setOpenFilterSlider}
        style={{ zIndex: 999 }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-50 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0  flex max-w-[300px]">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-[-300px]"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-[-300px]"
              >
                <Dialog.Panel className="pointer-events-auto relative w-screen max-w-md">
                  <Transition.Child
                    as={Fragment}
                    enter="ease-in-out duration-500"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in-out duration-500"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <div />
                  </Transition.Child>
                  <div className="flex h-full flex-col overflow-y-auto bg-white py-6 shadow-xl">
                    <div className="px-2 sm:px-2">
                      <div className="flex items-center justify-between">
                        <Dialog.Title className="mb-4 text-base font-semibold leading-6 text-gray-900">
                          Menu
                        </Dialog.Title>
                        <div>
                          <button onClick={() => setOpenFilterSlider(false)}>
                            <MdClose />
                          </button>
                        </div>
                      </div>
                      <div>
                        <SidebarComponent
                          handleClose={() => setOpenFilterSlider(false)}
                        />
                      </div>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default Sidebar;

const SidebarComponent = ({ handleClose }: { handleClose: () => void }) => {
  const [expanded, setExpanded] = useState<string>("");
  const { categories }: ContextDataType = useContext(LocalDataContext);
  return (
    <div>
      {categories?.map((category: SingleCategory) => (
        <div key={category.name} className="mb-2">
          <Link
            href={ROUTES.PRODUCTS.CATEGORY(category.slug)}
            className="font-bold hover:text-[#092143] md:text-sm"
            onClick={handleClose}
          >
            {category.name}
          </Link>
          <div className="ml-4">
            {category.subCategories.map((subCategory: SingleSubCategory) => (
              <div key={subCategory._id}>
                <Link
                  href={ROUTES.PRODUCTS.SUB_CATEGORY(
                    category.slug,
                    subCategory.slug,
                  )}
                  className="text-xs hover:text-[#092143] md:text-sm"
                  onClick={handleClose}
                >
                  {subCategory.name}
                </Link>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

