import { CheckoutResponse } from "@/Types/checkoutTypes";
import { BASE_URL } from "@/environment/environment";
import axios from "axios";

interface CheckoutProductDetails {
  productDetails: string;
  quantity: number;
}
interface checkoutReq {
  products: CheckoutProductDetails[];
}

export const addToCheckoutApi = () => {
  return {
    api(data: checkoutReq) {
      return axios
        .post(`${BASE_URL}/checkout/add-new`, data)
        .then(({ data }) => data);
    },
    getKey() {
      return ["addToCheckoutApi"];
    },
  };
};

export const getCheckoutApi = () => {
  return {
    api() {
      return axios
        .get<CheckoutResponse>(`${BASE_URL}/checkout/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getCheckoutApi"];
    },
  };
};
