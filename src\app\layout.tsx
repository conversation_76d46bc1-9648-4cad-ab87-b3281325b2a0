import LocalDataProvider from "@/Context/LocalDataProvider";
import Providers from "@/hooks/Providers";
import poppins from "@/utils/fonts";
import type { Metadata } from "next";
import { DM_Serif_Display } from "next/font/google";
import "./globals.css";

const des = `INNI inspires Bangladeshis to transform their skincare habits by offering authentic Korean skincare products, expert tips, and easy-to-follow routines. We are dedicated to bringing the best and 100% genuine Korean skincare products to the people of Bangladesh at affordable prices.
Our mission is to ensure that everyone has access to original Korean skincare solutions that truly work. We are committed to delivering 100% customer satisfaction through reliable support and trusted service.
We offer a cash on delivery system within Dhaka city, making shopping easier and safer.
At INNI, our greatest strength is your trust and support.

`;

export const metadata: Metadata = {
  title: "INNI",
  description: des,
  openGraph: {
    images: ["https://i.ibb.co/G3phx3QF/korean-trendy-mall.jpg"],
  },
};

// const poppins = Poppins({
//   subsets: ["latin"],
//   display: "swap",
//   weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
//   variable: "--font-poppins",
// });

const dmSerifDisplay = DM_Serif_Display({
  subsets: ["latin"],
  display: "swap",
  weight: ["400"],
  variable: "--font-dm-serif-display",
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={poppins.className}>
      {/* <GoogleTagManager gtmId="GTM-TZ2KQCGH" /> */}
      <head>
        {/* <meta
          name="facebook-domain-verification"
          content="jceappac0a4icjpwgevp40nn46clks"
        />
        <meta
          name="google-site-verification"
          content="O0fl0zaCcsxDv2uEEhAq4ywcm2O9MfyN5VTpvkRJ8bE"
        /> */}

        {/* <script
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-FH78GGXT8Z"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-FH78GGXT8Z');
            `,
          }}
        /> */}
      </head>
      <body>
        <noscript>
          {/* <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-TZ2KQCGH"
            height="0"
            width="0"
            style={{ display: "none", visibility: "hidden" }}
          ></iframe> */}
        </noscript>
        {/* <Suspense>
          <Analytics />
        </Suspense> */}
        <Providers>
          <LocalDataProvider>
            <main>{children}</main>
          </LocalDataProvider>
        </Providers>
      </body>
      {/* <GoogleAnalytics gaId="G-G40YMNFX0F" /> */}
    </html>
  );
}

{
  /* <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-MCDKXTPBMJ');
            `,
          }}
        /> */
}
