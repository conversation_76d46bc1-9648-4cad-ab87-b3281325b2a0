import UserProfile from "@/Components/UserProfile/UserProfile";
import UserDashboardLayout from "@/Layouts/UserDashboardLayout";
import { ROUTES } from "@/Routes";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import React from "react";

export const metadata: Metadata = {
  title: "Profile | INNI",
};

const ProfilePage = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;

  if (!accessToken) {
    redirect(ROUTES.LOG_IN(ROUTES.DASHBOARD.ORDERS.HOME));
    return null;
  }
  return (
    <UserDashboardLayout>
      <UserProfile accessToken={accessToken ? accessToken : ""} />
    </UserDashboardLayout>
  );
};

export default ProfilePage;
