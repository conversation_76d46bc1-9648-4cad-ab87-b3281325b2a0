import { ROUTES } from "@/Routes";
import Link from "next/link";
import React from "react";

const NotFoundPage = () => {
  return (
    <div className="h-full flex flex-col justify-center items-center pt-20">
      <div className="flex flex-col items-center gap-10">
        <p className="text-[60px] font-bold">Page Not Found</p>
        <p className="text-[150px] font-black flex items-center gap-4">
          <span className=" text-gradient">4</span>
          <span className=" text-gradient">0</span>
          <span className=" text-gradient">4</span>
        </p>
        <p className="text-[48px] font-black">
          Oops! That page can’t be found.
        </p>
        <p>
          Something seems to have gone wrong! The page you’s requesting doesn’t
          exist. It may have been obsolete, deleted, or entered an invalid
          address in the address bar
        </p>
        <Link href={ROUTES.HOME}>
          <button className="w-[400px] rounded-2xl px-4 p-2 text-white font-bold gradient-background">
            Go To Home Page
          </button>
        </Link>
      </div>
    </div>
  );
};

export default NotFoundPage;
