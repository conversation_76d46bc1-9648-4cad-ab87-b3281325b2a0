import { SingleProductDetailsType } from "./productTypes";

export interface LandingPageProductDetailsResponse {
  success: boolean;
  message: string;
  result: LandingPageProductDetails;
}

export interface LandingPageProductDetails {
  _id: string;
  productDetails: SingleProductDetailsType;
  id: string;
  slug: string;
  title: string;
  punchLine: string;
  priceLine: string;
  whyBest: WhyBest;
  whyBestTwo: WhyBest;
  whyBuy: WhyBuy;
  awareness: WhyBuy;
  sections: WhyBest[];
  usage: Usage;
  video: string;
  youtubeUrl: string;
  generalQuestions: GeneralQuestion[];
  reviewImages: string[];
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}

export interface WhyBest {
  image: string;
  title: string;
  options: string[];
  _id: string;
}

export interface WhyBuy {
  image: string;
  title: string;
  options: string[];
  _id: string;
}

export interface Usage {
  image: string;
  title: string;
  options: string[];
  _id: string;
}

export interface GeneralQuestion {
  question: string;
  answer: string;
  _id: string;
}
