"use client";
import { ROUTES } from "@/Routes";
import { SingleBlogDetails, getBlogsApi } from "@/services/blogServices";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import Link from "next/link";
import SpinnerLoader from "../ReusableComponents/SpinnerLoader/SpinnerLoader";
import TitleViewer from "../ReusableComponents/TitleViewer/TitleViewer";
import { generateProductImage } from "../utils/GenerateProductImage";

const BlogsOverview = () => {
  const { api, getKey } = getBlogsApi();
  const { data, isLoading, refetch } = useQuery(getKey(), api, {
    refetchOnWindowFocus: false,
  });
  return (
    <div>
      <TitleViewer titleOne="" titleTwo="Blogs" seeAllButton={false} />
      {!isLoading ? (
        <>
          {data?.results?.length ? (
            <>
              <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                {data?.results?.map((single: SingleBlogDetails) => (
                  <Link href={ROUTES?.BLOGS.VIEW(single.id)} key={single?.id}>
                    <div className="rounded-b-2xl bg-slate-200">
                      <Image
                        src={generateProductImage(single.image)}
                        height={100}
                        width={100}
                        alt="Carousel Image 1"
                        className="h-[150px] w-full"
                      />
                      <div className="p-4">
                        <span>{single?.title}</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </>
          ) : (
            <p>No Blogs Found</p>
          )}
        </>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
};

export default BlogsOverview;
