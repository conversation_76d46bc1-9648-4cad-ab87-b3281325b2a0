import Image from "next/image";
import React from "react";
import OrderNowButton from "./OrderNowButton";
import { generateProductImage } from "../utils/GenerateProductImage";
interface Props {
  title: string;
  video: string;
  youtubeUrl: string;
  mainImageUrl: string;
  punchLine: string;
  priceLine: string;
}

const DirectBuyTopPart = ({
  title,
  video,
  mainImageUrl,
  punchLine,
  priceLine,
  youtubeUrl,
}: Props) => {
  return (
    <div
      className="grid grid-cols-12 gap-4 px-4 py-2 md:px-20 md:py-4"
      style={{
        background: "linear-gradient(73.98deg, #FFFFFF 0%, #FFE0B9 99.42%)",
      }}
    >
      <div className="col order-2 col-span-12 md:order-1 md:col-span-6">
        <div className="flex w-full flex-col  justify-between">
          <Image
            src="https://i.ibb.co/LSg6pnJ/logo.png"
            alt="glowmeko"
            height={100}
            width={100}
            className="hidden h-[50px] w-[120px] md:block md:h-[80px] md:w-[180px]"
          />
          <div className="mt-1 md:mt-8">
            <div
              className="text-md font-bold md:text-2xl"
              dangerouslySetInnerHTML={{
                __html: punchLine ? punchLine : "",
              }}
            />
            <div
              style={{
                fontFamily: "monospace",
              }}
              className="mt-2 text-xl font-bold md:text-4xl"
              dangerouslySetInnerHTML={{
                __html: priceLine ? priceLine : "",
              }}
            />
          </div>
          <div className="mt-1 flex items-center justify-center md:mt-6">
            <OrderNowButton />
          </div>
        </div>
      </div>
      <div className="col order-1 col-span-12 flex w-full flex-col items-center md:order-2 md:col-span-6">
        <Image
          src="https://i.ibb.co/LSg6pnJ/logo.png"
          alt="glowmeko"
          height={100}
          width={100}
          className="mb-4 block h-[50px] w-[120px] md:hidden md:h-[80px] md:w-[180px]"
        />
        <span className="font-bold">{title}</span>
        <div className="mt-2">
          {youtubeUrl ? (
            <iframe
              // width="560"
              // height="315"
              className="h-[250px] w-[90vw] lg:h-[315px] lg:w-[560px]"
              src={`${youtubeUrl}?autoplay=1`}
              title="YouTube video player"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            />
          ) : video ? (
            <video
              src={
                video
                  ? generateProductImage(video)
                  : "https://beautysiaa.com/wp-content/uploads/2023/11/bb-cream-jahsbf.mp4"
              }
              className="h-[350px] min-w-[350px] max-w-[400px] rounded-xl border-2 border-white bg-white p-4 shadow-2xl"
              controls
              autoPlay
            />
          ) : (
            <Image
              height={100}
              width={100}
              src={generateProductImage(mainImageUrl)}
              alt="why buy image"
              className="w-[350px] rounded-xl border-4 border-[#4E001F] lg:w-[350px]"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default DirectBuyTopPart;
