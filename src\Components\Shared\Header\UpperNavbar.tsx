import Link from "next/link";
import { FaInstagram } from "react-icons/fa";
import { FaSquareFacebook, FaXTwitter } from "react-icons/fa6";
import { IoLogoYoutube } from "react-icons/io5";
import { LuPhone } from "react-icons/lu";

const UpperNavbar = () => {
  return (
    <div className="bg-[#092143] py-2 text-white">
      <div className="px-4 md:px-6 lg:px-20 xl:px-28">
        <div className="flex items-center justify-between">
          <div className="left flex items-center gap-5">
            <div className="phone hidden items-center gap-2 sm:flex">
              <LuPhone className="text-[12px]" />
              <span className="text-[12px]">+8801375-950118</span>
            </div>
          </div>
          <div className="middle">
            <p className="text-[12px]">
              Follow Us and get a chance to win 80% off
            </p>
          </div>
          <div className="right hidden items-center gap-3 md:flex ">
            <p className="text-[12px]">Follow Us:</p>
            <div className="social flex items-center gap-2">
              <Link href="#">
                <FaInstagram className="text-[12px]" />
              </Link>
              <Link href="#">
                <IoLogoYoutube className="text-[12px]" />
              </Link>
              <Link href="#">
                <FaSquareFacebook className="text-[12px]" />
              </Link>
              <Link href="#">
                <FaXTwitter className="text-[12px]" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpperNavbar;
