import { SingleProductOfOrderOrCheckout } from "./public-types";

export interface OrdersListResponse {
  message: string;
  results: SingleOrderDetails[];
  success: boolean;
}
export interface OrdersDetailsResponse {
  message: string;
  result: SingleOrderDetails;
  success: boolean;
}

export interface SingleOrderDetails {
  _id: string;
  orderId: string;
  userId: string;
  name: string;
  phoneNumber: string;
  email: string;
  zipCode: string;
  division: string;
  district: string;
  deliveryAddress: string;
  paymentMethod: string;
  bkashNumber: string;
  transactionId: string;
  orderStatus: string;
  adminDiscount: number;
  trackingHistory: TrackingHistory[];
  notes: Note[];
  products: SingleProductOfOrderOrCheckout[];
  productPrice: number;
  deliveryCharge: number;
  discount: number;
  couponDiscount: number;
  totalAmount: number;
  paidAmount: number;
  userSubmittedAmount: number;
  dueAmount: number;
  createdAt: Date;
  updatedAt: Date;
  __v: number;
}

export interface TrackingHistory {
  time: string;
  message: string;
  _id: string;
}

export interface Note {
  name: string;
  time: string;
  message: string;
  _id: string;
}
