import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { SingleProductOfOrderOrCheckout } from "@/Types/public-types";
import Image from "next/image";

interface Props {
  products?: SingleProductOfOrderOrCheckout[];
}
const CheckoutOrOrderedProducts = ({ products }: Props) => {
  return (
    <div className="mt-5 w-full">
      <div className="hidden rounded-lg border p-4 shadow-sm md:block md:w-full">
        <div className="overflow-x-auto">
          <table className="w-full border-collapse overflow-x-auto">
            <thead>
              <tr>
                <th className="text-center">Product</th>
                <th className="text-center">Price</th>
                <th className="text-center">Quantity</th>
                <th className="text-center">Subtotal</th>
              </tr>
            </thead>
            <tbody>
              {products?.map(
                (product: SingleProductOfOrderOrCheckout, index: number) => (
                  <tr
                    key={product.productId}
                    className={`${
                      index !== products.length - 1 && "border-b"
                    } mb-4 pb-4`}
                  >
                    <td className="flex items-center px-4 py-2">
                      <Image
                        height={100}
                        width={100}
                        src={generateProductImage(
                          product.image ?? product.mainImageUrl,
                        )}
                        alt={product.name}
                        className="w-20 rounded-lg"
                      />
                      <h3 className="ml-4 text-sm font-normal">
                        {product.name}
                      </h3>
                    </td>
                    <td className="px-4 py-2 text-center text-lg font-semibold">
                      ৳ {product.discountPrice}{" "}
                      {product.price !== product.discountPrice ? (
                        <span className="text-sm font-normal text-slate-500  line-through">
                          ৳{product.price}
                        </span>
                      ) : (
                        ""
                      )}
                    </td>
                    <td className="px-4 py-2 text-center">
                      <span>{product.quantity}</span>
                    </td>
                    <td className="px-4 py-2 text-center">
                      <span className="text-center font-semibold">
                        ৳{product.discountPrice * product.quantity}
                      </span>
                    </td>
                  </tr>
                ),
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* for small screen */}
      <div className="block rounded-lg border p-2 shadow-md md:hidden md:w-2/3">
        {products?.map((product: SingleProductOfOrderOrCheckout) => (
          <div
            className="mb-4  flex border-b pb-4  md:hidden"
            key={product._id}
          >
            {/* Left side - Image */}
            <div className="mr-4 w-20 ">
              <Image
                height={100}
                width={100}
                src={generateProductImage(
                  product.image ?? product.mainImageUrl,
                )}
                alt={product.name}
                className="h-auto w-full rounded-md"
              />
            </div>

            {/* Right side - Product Details */}
            <div className="flex-1 ">
              <div className="flex items-start justify-between gap-1">
                <h3 className="text-sm font-normal">{product.name}</h3>
              </div>
              <div className="my-2 flex flex-col gap-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-normal text-gray-600">
                    Price :
                  </span>
                  <span className="text-lg font-semibold">
                    ৳ {product.discountPrice}{" "}
                    {product.price !== product.discountPrice ? (
                      <span className="text-sm text-slate-500 line-through">
                        ৳{product.price}
                      </span>
                    ) : (
                      ""
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-normal text-gray-600">
                    Quantity :
                  </span>
                  <div className="mb-4 mt-2 flex items-center justify-between ">
                    <span className="mx-1">{product?.quantity}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-normal text-gray-600">
                    Subtotal :
                  </span>
                  <span className="text-lg font-semibold">
                    ৳ {product.discountPrice * product?.quantity}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CheckoutOrOrderedProducts;
