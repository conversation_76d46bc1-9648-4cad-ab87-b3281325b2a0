import { FaOpencart } from "react-icons/fa";

interface Props {
  handleClick: () => void; // Specify the type for handleClick
}

const OrderButtonThree = ({ handleClick }: Props) => {
  return (
    <button
      className="flex items-center rounded-full border-2 border-dashed border-white bg-[#092143] px-8 py-4 text-2xl font-bold text-white transition-all duration-200 hover:scale-105 sm:text-3xl"
      onClick={handleClick}
      type="button"
    >
      <FaOpencart />
      <span className="ml-3 mr-0 md:mr-3">অর্ডার করুন</span>
    </button>
  );
};

export default OrderButtonThree;
