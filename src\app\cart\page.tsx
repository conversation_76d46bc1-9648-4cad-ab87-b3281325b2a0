import CartPageOverview from "@/Components/CartComponents/CartPageOverview";
import NoUserCartOverview from "@/Components/NoUserComponents/NoUserCartComponents/NoUserCartOverview";
import { BASE_URL } from "@/environment/environment";
import ShopLayout from "@/Layouts/ShopLayout";
import { Metadata } from "next";
import { cookies } from "next/headers";
import NotFound from "../not-found";
import { CartListType } from "@/Types/cartPageTypes";

export const metadata: Metadata = {
  title: "Cart | INNI",
};

const CartPage = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;

  const data: CartListType = await getData(accessToken ? accessToken : "");

  return (
    <ShopLayout>
      {accessToken ? (
        <CartPageOverview
          accessToken={accessToken ? accessToken : ""}
          cartItems={data?.results}
        />
      ) : (
        <NoUserCartOverview />
      )}
    </ShopLayout>
  );
};

export default CartPage;

async function getData(token: string) {
  const res = await fetch(`${BASE_URL}/cart/get-all`, {
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    next: { revalidate: 0 }, // For Next.js caching control
  });

  if (!res.ok) {
    NotFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}

