import { ROUTES } from "@/Routes";
import axios from "axios";
import { signOut } from "next-auth/react";
import { redirect } from "next/navigation";
import Cookies from "js-cookie";

export const readCookie = () => {
  const cookieValue = Cookies.get("GMK");
  /* let key = "GMK" + "=";
  let cookies = document?.cookie?.split(";");
  for (let i = 0; i < cookies.length; i++) {
    let cookie = cookies[i];
    while (cookie.charAt(0) === " ") {
      cookie = cookie.substring(1, cookie.length);
    }
    if (cookie.indexOf(key) === 0) {
      return cookie.substring(key.length, cookie.length);
    }
  }
  return false; */
  if (cookieValue) {
    return cookieValue;
  } else {
    return false;
  }
};

axios.interceptors.request.use(
  (config) => {
    let accessToken = readCookie();
    if (accessToken) {
      config.headers["Authorization"] = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response.data.error.message === "jwt expired") {
      document.cookie = `GMK=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      signOut();
      redirect(ROUTES.LOG_IN("/"));
    }
  },
);
