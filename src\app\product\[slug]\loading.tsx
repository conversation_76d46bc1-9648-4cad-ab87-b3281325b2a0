import SingleProductDetailsSkeleton from "@/Components/ReusableComponents/Loaders/SingleProductDetailsSkeleton";
import RightSideCartIcon from "@/Components/ReusableComponents/RightSideCartIcon/RightSideCartIcon";
import LowerNavbar from "@/Components/Shared/Header/LowerNavbar";
import Navbar from "@/Components/Shared/Header/Navbar";
import UpperNavbar from "@/Components/Shared/Header/UpperNavbar";
import { cookies } from "next/headers";

const SingleProductLoadingSuspense = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;
  return (
    <section>
      <div style={{ position: "fixed", width: "100%", zIndex: 99, top: "0px" }}>
        <UpperNavbar />
        <Navbar />
        <LowerNavbar />
      </div>
      <div
        className="fixed right-0 top-[300px] md:top-[400px]"
        style={{ zIndex: 99 }}
      >
        <RightSideCartIcon accessToken={accessToken} />
      </div>
      <div className="w-100 mx-auto mt-2 min-h-[95vh] px-2 py-2  pt-[105px] text-black sm:px-6 md:px-6 md:pt-[115px] lg:px-20 xl:px-28 2xl:px-32">
        <SingleProductDetailsSkeleton />;
      </div>
    </section>
  );
};

export default SingleProductLoadingSuspense;
