"use client";
import { Location } from "@/app/store-location/page";
import Image from "next/image";
import React from "react";
import { generateProductImage } from "../utils/GenerateProductImage";
import Link from "next/link";
interface Props {
  locations: Location[];
}

const ShopLocations = ({ locations }: Props) => {
  return (
    <div className="flex flex-col items-center justify-center gap-4">
      <h1 className="text-primary text-2xl font-bold md:text-4xl">
        Our Shop Addresses
      </h1>
      {locations?.length ? (
        <div>
          {locations?.map((location: Location) => (
            <div
              className="bg-primary flex w-full items-center justify-between gap-2 rounded px-4 py-2 text-white md:w-[800px]"
              key={location?._id}
            >
              <div>
                <Image
                  src={generateProductImage(location.imageUrl)}
                  height={100}
                  width={100}
                  alt="Carousel Image 1"
                  className="h-[100px] w-full"
                />
              </div>
              <div>
                <h3 className="mb-2 text-xl font-bold">
                  {location.branchName}
                </h3>
                <span>{location?.address}</span>
              </div>
              <div>
                <Link href={location?.mapLink} target="_blank">
                  <button className="min-w-[150px] rounded-2xl border-2 border-white px-4 py-2">
                    Google Map
                  </button>
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        "No Store Found"
      )}
    </div>
  );
};

export default ShopLocations;
