import { BASE_URL } from "@/environment/environment";
import ShopLayout from "@/Layouts/ShopLayout";
import { Metadata } from "next";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
  title: "About Us | INNI",
};
const AboutUsPage = async () => {
  const data = await getData();

  return (
    <ShopLayout>
      <div
        dangerouslySetInnerHTML={{
          __html: data?.result?.content
            ? data?.result?.content
            : "<p>Not Found</p>",
        }}
      />
    </ShopLayout>
  );
};

async function getData() {
  const res = await fetch(`${BASE_URL}/content/about`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
  }

  return res.json();
}

export default AboutUsPage;
