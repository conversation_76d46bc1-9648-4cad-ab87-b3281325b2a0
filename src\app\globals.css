@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  /* color: rgb(var(--foreground-rgb)); */
  /*  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb)); */
  font-weight: 400;
}

/* Define scrollbar styles */
/* ::-webkit-scrollbar {
  width: 8px;
} */

/* Track */
/* ::-webkit-scrollbar-track {
  background: #f1f1f1; 
} */

/* Handle */
/* ::-webkit-scrollbar-thumb {
  background: #092143; 
  border-radius: 6px;
} */

/* Handle on hover */
/* ::-webkit-scrollbar-thumb:hover {
  background: #875f3a; 
} */

ul {
  list-style-type: disc;
  margin-left: 40px;
  font-weight: normal;
}

li {
  margin-bottom: 0.5em;
  font-weight: normal;
}

p {
  font-weight: normal;
}

.bzbtoast {
  width: 100%;
  display: flex;
  align-items: center;
  color: white;
  padding: 0px;
  border-radius: 10px;
}

.bzbtoastsize {
  border-radius: 10px;
  display: flex;
  height: 36px;
  width: 36px;
  align-items: center;
  padding: 8px;
}

.bzbtoasticon {
  height: 20px;
  width: 20px;
  color: white;
}

.toastloading {
  background-color: #3949ab;
}

.toastloadingicon {
  height: 20px;
  width: 20px;
  transform: rotate(180deg);
}

.toastsuccess {
  background-color: #16a34a;
}

.toasterror {
  background-color: #e53935;
}

.toastloading {
  background-color: #3949ab;
}

.toastblank {
  background-color: #ffb300;
}

.iiz {
  height: 100% !important;
  width: 100% !important;
}

.iiz>div {
  height: 100% !important;
}

.iiz__img {
  height: 100% !important;
  width: 100% !important;
}

/* common styles */
.bg-primary {
  background-color: #092143;
}

.text-primary {
  color: #092143;
}

.bg-tertiary {
  background-color: #0C1E32;
}

.border-primary {
  border-color: #092143;
}

.gradient-background {
  background: radial-gradient(circle, rgba(162, 120, 78, 1) 50%, rgba(246, 222, 141, 1) 90%)
}

.text-gradient {
  background: radial-gradient(circle, rgba(162, 120, 78, 1) 15%, rgba(246, 222, 141, 1) 90%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@keyframes slide-down {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out forwards;
}


.description-tab h1,
.description-tab h2,
.description-tab h3,
.description-tab h4,
.description-tab h6 {
  font-weight: bold;
}

.description-tab h1 {
  font-size: 32px;
  line-height: 36px;
}

.description-tab h2 {
  font-size: 26px;
  line-height: 30px;
}

.description-tab h3 {
  font-size: 24px;
  line-height: 28px;
}

.description-tab h4 {
  font-size: 20px;
  line-height: 24px;
}

.description-tab h5 {
  font-size: 16px;
  line-height: 20px;
}

.description-tab h6 {
  font-size: 15px;
  line-height: 19px;
}

.description-tab p {
  font-size: 14px;
  line-height: 18px;
}

.description-tab a {
  color: #F48F4B;
  text-decoration: none;
  font-weight: 500;
}

.description-tab a:hover {
  text-decoration: underline;
}

.description-tab strong {
  font-weight: bold;
}