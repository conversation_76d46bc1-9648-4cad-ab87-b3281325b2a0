import ProductsPage from "@/Components/ProductsPage/ProductsPage";
import ShopLayout from "@/Layouts/ShopLayout";
import { BASE_URL } from "@/environment/environment";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
  title: "Products | INNI",
};

interface SearchParams {
  size: number;
  page: string;
  brand: string;
  productType: string;
  category: string;
  subCategory: string;
  skinType: string;
  skinConcern: string;
}

const Products = async ({
  searchParams,
}: {
  searchParams: Promise<SearchParams>;
}) => {
  const accessToken = (await cookies()).get("GMK")?.value;
  const {
    size,
    page,
    brand,
    productType,
    category,
    skinType,
    skinConcern,
    subCategory,
  } = await searchParams;
  const data = await getProductList({
    size,
    page,
    brand,
    productType,
    category,
    skinType,
    skinConcern,
    subCategory,
  });

  return (
    <ShopLayout>
      <ProductsPage
        accessToken={accessToken ? accessToken : ""}
        data={data}
        pageType="products"
      />
    </ShopLayout>
  );
};

export default Products;

async function getProductList({
  size,
  page,
  brand,
  productType,
  category,
  skinType,
  skinConcern,
  subCategory,
}: SearchParams) {
  const queryParams = new URLSearchParams();

  if (size) queryParams.append("size", size.toString());
  if (page) queryParams.append("page", page.toString());
  if (category) queryParams.append("productCategory", category);
  if (subCategory) queryParams.append("subCategory", subCategory);
  if (productType) queryParams.append("productType", productType);
  if (skinType) queryParams.append("skinType", skinType);
  if (brand) queryParams.append("brand", brand);
  if (skinConcern) queryParams.append("skinConcern", skinConcern);

  const filter = queryParams.toString();
  const res = await fetch(`${BASE_URL}/products/get-all?${filter}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}