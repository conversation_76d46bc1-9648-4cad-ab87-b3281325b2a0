import { TrackingResponse } from "@/Types/courierTrackingResponse";
import { OrdersDetailsResponse, OrdersListResponse } from "@/Types/orderTypes";
import { BASE_URL } from "@/environment/environment";
import axios from "axios";

export const createNewOrderApi = () => {
  return {
    api(data: any) {
      return axios
        .post(`${BASE_URL}/orders/add-new`, data)
        .then(({ data }) => data);
    },
    getKey() {
      return ["createNewOrderApi"];
    },
  };
};

export const createNewOrderWithoutUserApi = () => {
  return {
    api(data: any) {
      return axios
        .post(`${BASE_URL}/orders/add-new-without-user`, data)
        .then(({ data }) => data);
    },
    getKey() {
      return ["createNewOrderWithoutUserApi"];
    },
  };
};

export const getOrdersListApi = () => {
  return {
    api() {
      return axios
        .get<OrdersListResponse>(`${BASE_URL}/orders/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getOrdersListApi"];
    },
  };
};
export const getOrdersDetailsApi = (orderId: string) => {
  return {
    api() {
      return axios
        .get<OrdersDetailsResponse>(`${BASE_URL}/orders/single/${orderId}`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getOrdersDetailsApi"];
    },
  };
};

export const getCourierTrackingApi = (tracking: string) => {
  return {
    api() {
      return axios
        .get<TrackingResponse>(
          `https://steadfast.com.bd/track/consignment/${tracking}`
        )
        .then(({ data }) => data);
    },
    getKey() {
      return ["getCourierTrackingApi"];
    },
  };
};
