import offerImageOne from "@/dummyImages/offer-banner/offer-1.png";
import offerImagetwo from "@/dummyImages/offer-banner/offer-2.png";
import { getOfferProductListApi } from "@/services/productsServices";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";

const OfferSection = () => {
  const { api: offerApi, getKey: offerGetKey } = getOfferProductListApi();
  const { data: offer, isLoading: isOfferLoading } = useQuery(
    offerGetKey(),
    offerApi,
    {
      refetchOnWindowFocus: false,
      onSuccess(data) { },
    },
  );

  return (
    <div className="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2">
      <div className="relative">
        <Image
          src={offerImageOne}
          width={500}
          height={500}
          alt="offer"
          className="h-auto w-full rounded-2xl"
        />
        <div className="absolute left-[50%] top-[50%] translate-y-[-50%]">
          <div className="w-[99%]">
            <div className="hidden lg:block">
              <span className="rounded bg-white px-2 py-1 text-xs">
                SPECIAL OFFER
              </span>
            </div>
            <p className="mt-2 text-base font-semibold text-gray-800 sm:text-sm md:text-base xl:text-xl w-[98%] sm:w-[100%]">
              {offer?.results[0]?.name ?? "Product Name"}
            </p>
            <div className="mt-3 flex flex-col items-start gap-1 xl:flex-row xl:items-center xl:gap-4">
              <button className="rounded-lg border-secondary bg-secondary px-3 py-1 text-sm text-white md:text-base">
                Buy Now {offer?.results[0]?.discountPrice ?? 0} BDT
              </button>
              <button className="rounded-lg px-2 py-2 text-sm text-gray-800 hover:text-secondary md:text-base">
                View Details
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className="relative">
        <Image
          src={offerImagetwo}
          width={500}
          height={500}
          alt="offer"
          className="h-auto w-full rounded-2xl"
        />
        <div className="absolute left-[50%] top-[50%]  translate-y-[-50%]">
          <div className="w-[99%]">
            <div className="hidden lg:block">
              <span className=" rounded bg-white px-2 py-1 text-xs ">
                SPECIAL OFFER
              </span>
            </div>
            <p className="mt-2 text-base font-semibold text-gray-800 sm:text-sm md:text-base xl:text-xl w-[98%] sm:w-[100%]">
              {offer?.results[1]?.name ?? "Product Name"}
            </p>
            <div className="mt-3 flex flex-col items-start gap-1 xl:flex-row xl:items-center xl:gap-4">
              <button className="rounded-lg border-primary bg-primary px-3 py-1 text-sm text-white md:text-base">
                Buy Now {offer?.results[1]?.discountPrice ?? 0} BDT
              </button>
              <button className="rounded-lg px-2 py-2 text-sm text-gray-800 hover:text-primary md:text-base">
                View Details
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OfferSection;
