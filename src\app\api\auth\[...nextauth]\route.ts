import axios from "axios";
import NextAuth from "next-auth/next";
import GoogleProvider from "next-auth/providers/google";
import FacebookProvider from "next-auth/providers/facebook";
import CredentialsProvider from "next-auth/providers/credentials";
import { cookies } from "next/headers";
import { BASE_URL } from "@/environment/environment";

const handler = NextAuth({
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID ?? "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? "",
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID ?? "",
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET ?? "",
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const { email, password } = credentials as {
          email: string;
          password: string;
        };

        try {
          const isVerified = await axios.post(`${BASE_URL}/users/verify-user`, {
            email: email,
            password: password,
          });

          if (isVerified) {
            (await cookies()).set("GMK", isVerified.data.token, {
              expires: Date.now() + 30 * 24 * 60 * 60 * 1000,
              secure: true,
            });
            
            return isVerified.data.userData;
          } else {
            throw new Error("Invalid username or password");
          }
        } catch (error) {
          throw new Error("Invalid username or password");
        }
      },
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      return session;
    },
    async signIn({ profile, credentials }) {
      if (!credentials) {
        const data = {
          name: profile?.name,
          email: profile?.email,
          //@ts-ignore
          profilePicture: profile?.picture ?? "",
          role: "user",
          mobileNumber: " ",
          password: "googleUser",
          signUpMethod: "google",
          signUpBy: "self",
        };
        try {
          const res = await axios.post(`${BASE_URL}/users/add-new`, data);
          (await cookies()).set("GMK", res.data.token, {
            expires: Date.now() + 30 * 24 * 60 * 60 * 1000,
            secure: true,
          });
        } catch (error) {
          console.log(error);
        }
      }

      return true;
    },
  },
});

export { handler as GET, handler as POST };
