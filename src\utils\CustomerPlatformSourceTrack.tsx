"use client";
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";

const GetCustomerTrackUTMSource = () => {
  const searchParams = useSearchParams();

  useEffect(() => {
    if (typeof window !== "undefined") {
      // Get tracking parameters
      const utmSource = searchParams?.get("utm_source");
      const fbclid = searchParams?.get("fbclid"); // Facebook Click ID
      const gclid = searchParams?.get("gclid"); // Google Ads Click ID
      const ttclid = searchParams?.get("ttclid"); // TikTok Click ID
      const msclkid = searchParams?.get("msclkid"); // Microsoft Ads Click ID
      const twclid = searchParams?.get("twclid"); // Twitter (X) Click ID
      const lclid = searchParams?.get("lclid"); // LinkedIn Click ID
      const pclid = searchParams?.get("pclid"); // Pinterest Click ID
      const snapclid = searchParams?.get("snapclid"); // Snapchat Click ID

      if (utmSource) {
        localStorage.setItem("utm_source", utmSource);
      }

      // Detect platform based on URL parameters
      if (fbclid) {
        localStorage.setItem("utm_source", "facebook");
      } else if (gclid) {
        localStorage.setItem("utm_source", "google");
      } else if (ttclid) {
        localStorage.setItem("utm_source", "tiktok");
      } else if (msclkid) {
        localStorage.setItem("utm_source", "microsoft");
      } else if (twclid) {
        localStorage.setItem("utm_source", "twitter");
      } else if (lclid) {
        localStorage.setItem("utm_source", "linkedin");
      } else if (pclid) {
        localStorage.setItem("utm_source", "pinterest");
      } else if (snapclid) {
        localStorage.setItem("utm_source", "snapchat");
      }

      // Fallback: Detect from referrer
      let referrer = document?.referrer;
      if (referrer.includes("facebook.com")) {
        localStorage.setItem("utm_source", "facebook");
      } else if (referrer.includes("tiktok.com")) {
        localStorage.setItem("utm_source", "tiktok");
      } else if (referrer.includes("instagram.com")) {
        localStorage.setItem("utm_source", "instagram");
      } else if (
        referrer.includes("twitter.com") ||
        referrer.includes("x.com")
      ) {
        localStorage.setItem("utm_source", "twitter");
      } else if (referrer.includes("linkedin.com")) {
        localStorage.setItem("utm_source", "linkedin");
      } else if (referrer.includes("pinterest.com")) {
        localStorage.setItem("utm_source", "pinterest");
      } else if (referrer.includes("snapchat.com")) {
        localStorage.setItem("utm_source", "snapchat");
      } else if (referrer.includes("bing.com")) {
        localStorage.setItem("utm_source", "bing");
      } else if (referrer.includes("yahoo.com")) {
        localStorage.setItem("utm_source", "yahoo");
      } else if (referrer.includes("duckduckgo.com")) {
        localStorage.setItem("utm_source", "duckduckgo");
      }
    }
  }, [searchParams]);

  return null;
};

export default GetCustomerTrackUTMSource;
