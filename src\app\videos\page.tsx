import TitleViewer from "@/Components/ReusableComponents/TitleViewer/TitleViewer";
import ShopLayout from "@/Layouts/ShopLayout";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Videos | INNI",
};

const VideosPage = () => {
  return (
    <ShopLayout>
      <TitleViewer titleOne="" titleTwo="Videos" seeAllButton={false} />
      <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
        {[
          "https://www.youtube.com/embed/k6kNlL7NSjk?si=mFbLKtytMR6CduMh",
          "https://www.youtube.com/embed/O_qgaOUR3Fo?si=H-tIEmQtuePoq116",
          "https://www.youtube.com/embed/O_qgaOUR3Fo?si=ncN-jQ_oMwZxRI98",
          "https://www.youtube.com/embed/PYS3UZFPJWI?si=vjOga-5xkRSL9cgI",
          "https://www.youtube.com/embed/HhM0BYCHL00?si=2jHu5KFBsDkjD_WM",
        ].map((singleUrl: string) => (
          <div key={singleUrl}>
            <iframe
              src={singleUrl}
              title="YouTube video player"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              className="h-[150px] w-full rounded-xl border-2 border-[#092143] md:h-[200px] md:w-[300px]"
            />
          </div>
        ))}
      </div>
    </ShopLayout>
  );
};

export default VideosPage;
