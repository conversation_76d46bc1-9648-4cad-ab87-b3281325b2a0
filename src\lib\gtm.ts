export const gtmDataLayerDataPass = async (props: { [key: string]: any }) => {
  const browserInfo = getBrowserInfo();
  const username = splitName(props?.user_name);
  const ip_address = localStorage.getItem("ip_address")
    ? localStorage.getItem("ip_address")
    : await getIPAddress();

  //@ts-ignore
  return window.dataLayer?.push({
    event: props.event,
    currency: "BDT",
    url: window.location.href,
    user_browser_name: browserInfo?.name,
    user_browser_version: browserInfo?.version,
    user_device_type: browserInfo?.deviceType,
    user_ip_address: ip_address ?? "",
    user_country: "BD",
    user_country_name: "Bangladesh",
    user_first_name: username.firstName ?? "",
    user_last_name: username.lastName ?? "",
    ...props,
  });
};

export async function getIPAddress() {
  try {
    const response = await fetch("https://api.ipify.org?format=json");
    const data = await response.json();
    localStorage.setItem("ip_address", data?.ip);
    return data.ip;
  } catch (error) {
    console.error("Error fetching IP address:", error);
    return null;
  }
}

function getBrowserInfo() {
  const userAgent = navigator.userAgent;

  let browserName;
  let browserVersion;
  let deviceType;

  if (userAgent.indexOf("Firefox") > -1) {
    browserName = "Mozilla Firefox";
    //@ts-ignore
    browserVersion = userAgent?.match(/Firefox\/([0-9.]+)/)[1];
  } else if (userAgent.indexOf("Chrome") > -1) {
    browserName = "Google Chrome";
    //@ts-ignore
    browserVersion = userAgent?.match(/Chrome\/([0-9.]+)/)[1];
  } else if (userAgent.indexOf("Safari") > -1) {
    browserName = "Safari";
    //@ts-ignore
    browserVersion = userAgent?.match(/Version\/([0-9.]+)/)[1];
  } else if (
    userAgent.indexOf("MSIE") > -1 ||
    userAgent.indexOf("Trident/") > -1
  ) {
    browserName = "Internet Explorer";
    //@ts-ignore
    browserVersion = userAgent?.match(/(?:MSIE |rv:)(\d+(\.\d+)?)/)[1];
  } else {
    browserName = "Unknown";
    browserVersion = "Unknown";
  }

  if (/Mobile/.test(userAgent)) {
    deviceType = "Mobile";
  } else if (/Tablet/.test(userAgent)) {
    deviceType = "Tablet";
  } else {
    deviceType = "Desktop";
  }

  return {
    name: browserName,
    version: browserVersion,
    deviceType: deviceType,
  };
}

function splitName(name: string) {
  const nameParts = name?.trim()?.split(/\s+/);
  let firstName: any = "";
  let lastName: any = "";

  if (nameParts?.length === 1) {
    firstName = nameParts[0];
  } else {
    lastName = nameParts?.pop();
    firstName = nameParts?.join(" ");
  }

  return {
    firstName: firstName,
    lastName: lastName,
  };
}
