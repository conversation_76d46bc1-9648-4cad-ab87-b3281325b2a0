import UserDashboardMenu from "@/Components/ReusableComponents/UserDashboardMenu/UserDashboardMenu";
import FooterModern from "@/Components/Shared/Footer/FooterModern";
import LowerNavbar from "@/Components/Shared/Header/LowerNavbar";
import Navbar from "@/Components/Shared/Header/Navbar";
import UpperNavbar from "@/Components/Shared/Header/UpperNavbar";
import { cookies } from "next/headers";

export default async function UserDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const accessToken = (await cookies()).get("GMK")?.value;
  return (
    <section>
      <div style={{ position: "fixed", width: "100%", zIndex: 999 }}>
        <UpperNavbar />
        <Navbar />
        <LowerNavbar />
      </div>
      <div className="w-100 mx-auto min-h-[95vh] px-2 py-2 pt-[126px]  text-black sm:px-6 sm:pt-[130px] md:px-6  md:pt-[160px] lg:px-20 xl:px-28 2xl:px-32">
        <div className="mt-2 flex flex-col gap-4 md:flex-row">
          <div className="h-1/2">
            <UserDashboardMenu accessToken={accessToken} />
          </div>
          <div className="min-h-[35vh] w-full md:min-h-[80vh]">{children}</div>
        </div>
      </div>
      <div>
        <FooterModern />
      </div>
    </section>
  );
}
