import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ROUTES } from "@/Routes";
import { SingleCategory } from "@/services/bannerBrandCategoryServices";
import Image from "next/image";
import Link from "next/link";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

interface Props {
  categories: SingleCategory[];
}

const FeaterCategoriesServer = ({ categories }: Props) => {
  return (
    <Swiper
      effect="coverflow"
      grabCursor={true}
      centeredSlides={false}
      slidesPerView={7}
      spaceBetween={10}
      coverflowEffect={{
        rotate: 0,
        stretch: 10,
        depth: 100,
        modifier: 1,
        slideShadows: true,
      }}
      pagination={{
        clickable: true,
        el: ".swiper-pagination",
        type: "bullets",
      }}
      autoplay={{
        delay: 3000,
        disableOnInteraction: false,
      }}
      loop={true}
      breakpoints={{
        1920: {
          slidesPerView: 7,
        },
        1024: {
          slidesPerView: 6,
        },
        768: {
          slidesPerView: 4,
        },
        640: {
          slidesPerView: 4,
        },
        320: {
          slidesPerView: 3,
        },
      }}
      modules={[Autoplay]}
      className="mySwiper"
    >
      {categories?.length
        ? categories?.map((cat) => (
            <SwiperSlide key={cat.id}>
              <div className="flex flex-col items-center border-r border-[#E4E5EE]">
                <div className="">
                  <Link href={ROUTES.PRODUCTS.CATEGORY(cat.slug)}>
                    <Image
                      height={100}
                      width={100}
                      // src={cat.image}https://cdn-icons-png.freepik.com/256/9710/9710991.png?semt=ais_hybrid
                      // alt={cat.catName}
                      src={
                        cat?.imageUrl
                          ? generateProductImage(cat?.imageUrl)
                          : "https://cdn-icons-png.freepik.com/256/9710/9710991.png?semt=ais_hybrid"
                      }
                      alt={cat.name}
                      className="h-[50px] w-[50px] sm:h-[80px] sm:w-[80px]"
                    />
                  </Link>
                </div>
                <div className="mt-2">
                  <Link
                    href={ROUTES.PRODUCTS.CATEGORY(cat.slug)}
                    className="text-sm uppercase sm:text-base"
                  >
                    {cat.name}
                  </Link>
                </div>
              </div>
            </SwiperSlide>
          ))
        : ""}
    </Swiper>
  );
};

export default FeaterCategoriesServer;
