"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import InnerImageZoom from "react-inner-image-zoom";
import "react-inner-image-zoom/lib/InnerImageZoom/styles.css";

const ImageViewerWithHoverZoom = ({ imageUrl }: { imageUrl: string }) => {
  return (
    <InnerImageZoom
      src={generateProductImage(imageUrl)}
      zoomSrc={generateProductImage(imageUrl)}
      zoomType="hover"
      hideHint
      zoomScale={1.1}
      /* height={100}
      width={450}
      className="innerImageZoom" */
      className="rounded-xl"
    />
  );
};

export default ImageViewerWithHoverZoom;
