"use client";
import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

interface ScrollRevealWrapperProps {
  children: React.ReactNode;
}

const ScrollRevealWrapper: React.FC<ScrollRevealWrapperProps> = ({
  children,
}) => {
  const [ref, inView] = useInView({
    triggerOnce: false,
  });

  return (
    <div ref={ref}>
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          return (
            <motion.div
              key={index}
              initial={{ x: -50, opacity: 0 }}
              animate={inView ? { x: 0, opacity: 1 } : { x: -50, opacity: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              {child}
            </motion.div>
          );
        }
        return null;
      })}
    </div>
  );
};

export default ScrollRevealWrapper;
