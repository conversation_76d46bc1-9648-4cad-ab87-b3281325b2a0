"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { LandingPageProductDetails } from "@/Types/landingPageProductType";
import Image from "next/image";
import { GiMoebiusStar, GiTick } from "react-icons/gi";
import logo from "../../../../src/dummyImages/logo.webp";
import Accordion from "../Accordion";
import DirectOrderForm from "../DirectOrderForm";
import OrderButton from "./OrderButton";
import OrderButtonThree from "./OrderButtonThree";
import OrderButtonTwo from "./OrderButtonTwo";
import { convertToBanglaNumber } from "@/utils/EnglishToBanglaConvert";
import { TiTick } from "react-icons/ti";
import { FaRegHandPaper } from "react-icons/fa";

interface Props {
  productDetails: LandingPageProductDetails;
}

const LandingPageTwoOverview = ({ productDetails }: Props) => {
  const handleScrollToOrderForm = () => {
    const orderFormDiv = document?.getElementById("order-form");
    if (orderFormDiv) {
      orderFormDiv?.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <div className="landing-page-two">
      <section className="hero-section bg-gradient-to-b from-[#b9a9e0]  to-white">
        <div className="container mx-auto px-4 sm:px-0">
          <div className="flex justify-center">
            <div className="max-w-full sm:max-w-6xl">
              <div className="flex justify-center pb-5 pt-4">
                <Image
                  src={logo}
                  alt="INNI"
                  width={200}
                  height={100}
                />
              </div>
              <h1 className="text-center text-2xl font-bold leading-[1.4em] sm:text-5xl sm:leading-[1.5em]">
                {productDetails?.title}
              </h1>
              <div className="mt-3 text-center text-xl sm:text-2xl">
                <div
                  className="dangerouslyHtml"
                  dangerouslySetInnerHTML={{
                    __html: productDetails?.punchLine
                      ? productDetails?.punchLine
                      : "<p>No answer</p>",
                  }}
                />{" "}
                মাএ{" "}
                <span className="text-red-500 line-through">
                  {convertToBanglaNumber(productDetails?.productDetails?.price)}
                </span>{" "}
                <span className="text-3xl font-semibold text-green-700">
                  {convertToBanglaNumber(
                    productDetails?.productDetails?.discountPrice,
                  )}
                </span>{" "}
                টাকায়
              </div>
              <div className="mb-6 mt-5 flex justify-center">
                <OrderButton handleClick={handleScrollToOrderForm} />
              </div>
              {productDetails?.youtubeUrl ? (
                <iframe
                  className="md:min-h-96 aspect-video w-full self-stretch"
                  src={productDetails?.youtubeUrl}
                  frameBorder="0"
                  title={productDetails?.productDetails?.name}
                  aria-hidden="true"
                />
              ) : (
                <div>
                  <div className="h-[450px] max-w-6xl rounded-xl border-2 border-white bg-white p-4 shadow-2xl md:h-[550px]">
                    <video
                      src={
                        productDetails?.video
                          ? generateProductImage(productDetails?.video)
                          : "https://beautysiaa.com/wp-content/uploads/2023/11/bb-cream-jahsbf.mp4"
                      }
                      className="h-full w-full"
                      controls
                      autoPlay
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {productDetails?.productDetails?.isCombo ? (
        <section className="why-buy-2 mt-14 sm:mt-16 md:mt-20 xl:mt-24">
          <div className="container mx-auto px-4 sm:px-0">
            <div className="rounded-lg bg-[#092143] py-2 lg:py-4">
              <h1 className="text-center text-xl font-bold text-white sm:text-4xl lg:text-4xl">
                উপকারিতা
              </h1>
            </div>
            <div className="mt-8 flex justify-center">
              <div className="max-w-5xl">
                <div className="grid grid-cols-1 items-center gap-5 md:grid-cols-3">
                  <div className="order-2 space-y-4 md:order-1 md:col-span-2">
                    {productDetails?.whyBest?.options?.map((single: string) => (
                      <div
                        className="usefullnessCard flex items-center"
                        key={single}
                      >
                        <div className="icon rounded-full bg-[#092143] p-1">
                          <GiMoebiusStar className="text-xl text-white" />
                        </div>
                        <div className="card-text ml-3">
                          <h3>{single}</h3>
                        </div>
                      </div>
                    ))}
                    <div className="common-button flex justify-center md:justify-start">
                      <OrderButton handleClick={handleScrollToOrderForm} />
                    </div>
                  </div>
                  <div className="order-1 md:order-2">
                    <Image
                      src={generateProductImage(productDetails?.whyBest?.image)}
                      alt="logo"
                      width={100}
                      height={100}
                      className="h-auto w-full rounded-xl"
                    />
                  </div>
                </div>
                <div className="mt-14 grid grid-cols-1 items-center gap-5 md:grid-cols-3">
                  <div>
                    <Image
                      src={generateProductImage(
                        productDetails?.whyBestTwo?.image,
                      )}
                      alt="logo"
                      width={100}
                      height={100}
                      className="h-auto w-full rounded-xl"
                    />
                  </div>
                  <div className="rtl-md space-y-4 md:col-span-2">
                    {productDetails?.whyBestTwo?.options?.map(
                      (single: string) => (
                        <div
                          className="usefullnessCardThree flex items-center"
                          key={single}
                        >
                          <div className="icon rounded-full bg-[#092143] p-1">
                            <GiMoebiusStar className="text-xl text-white" />
                          </div>
                          <div className="card-text ml-3 mr-0 md:ml-0 md:mr-3">
                            <h3>{single}</h3>
                          </div>
                        </div>
                      ),
                    )}
                    <div className="common-button flex justify-center md:justify-start">
                      <OrderButtonThree handleClick={handleScrollToOrderForm} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      ) : (
        <section className="why-buy mt-14 sm:mt-16 md:mt-20 xl:mt-24">
          <div className="container mx-auto px-4 sm:px-0">
            <div className="rounded-lg bg-[#092143] py-2 lg:py-4">
              <h1 className="text-center text-xl font-bold text-white sm:text-4xl lg:text-4xl">
                উপকারিতা
              </h1>
            </div>
            <div className="mt-10 ">
              <div className="col-1 space-y-4 sm:space-y-7">
                {productDetails?.whyBest?.options.map((single: string) => (
                  <div
                    className="usefullnessCard flex items-center"
                    key={single}
                  >
                    <div className="icon rounded-full bg-[#092143] p-2">
                      <GiMoebiusStar className="text-3xl text-white" />
                    </div>
                    <div className="card-text ml-3">
                      <h3>{single}</h3>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="mt-8 flex justify-center">
              <OrderButton handleClick={handleScrollToOrderForm} />
            </div>
          </div>
        </section>
      )}

      <section className="why-buy mt-14 sm:mt-16 md:mt-20 xl:mt-24">
        <div className="container mx-auto px-4 sm:px-0">
          <div className="rounded-lg bg-[#092143] py-2 lg:py-4">
            <h1 className="text-center text-xl font-bold text-white sm:text-4xl lg:text-4xl">
              Ingredients/উপকরন
            </h1>
          </div>
          <div className="mt-2 flex flex-col gap-2">
            <div className="col-1 space-y-4 sm:space-y-7">
              <div className="usefullnessCard flex items-center">
                <div className="icon rounded-full bg-[#092143] p-2">
                  <TiTick className="text-sm text-white lg:text-3xl" />
                </div>
                <div className="card-text ml-3 ">
                  <span className="font-bold">Main Ingredients/উপকরন :</span>
                </div>
              </div>
              <div
                dangerouslySetInnerHTML={{
                  __html: productDetails?.productDetails?.ingredients
                    ? productDetails?.productDetails?.ingredients
                    : "<p>No content</p>",
                }}
                className="ml-10 font-semibold"
              />
            </div>
            <div className="col-1 space-y-4 sm:space-y-7">
              <div className="usefullnessCard flex items-center">
                <div className="icon rounded-full bg-[#092143] p-2">
                  <TiTick className="text-sm text-white lg:text-3xl" />
                </div>
                <div className="card-text ml-3 ">
                  <span className="font-bold">
                    Cosmetic Type :{" "}
                    {productDetails?.productDetails?.productCategory}
                  </span>
                </div>
              </div>
            </div>
            <div className="col-1 space-y-4 sm:space-y-7">
              <div className="usefullnessCard flex items-start">
                <div className="icon rounded-full bg-[#092143] p-2">
                  <TiTick className="text-sm text-white lg:text-3xl" />
                </div>
                <div className="card-text ml-3 flex flex-wrap items-center">
                  <span className="mr-2 font-bold">Skin Type:</span>
                  {productDetails?.productDetails?.skinType?.map((single) => (
                    <span key={single} className="mr-2 font-bold">
                      {single}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="how-to-use mt-14 sm:mt-16 md:mt-20 xl:mt-24">
        <div className="container mx-auto px-4 sm:px-0">
          <div className="rounded-tl-lg rounded-tr-lg bg-[#092143] py-2 lg:py-4">
            <h1 className="text-center text-xl font-bold text-white sm:text-4xl lg:text-4xl">
              ব্যবহার করার নিয়ম
            </h1>
          </div>
          <div className="bg-gradient-to-b from-[#b9a9e0] to-[#bcace6]">
            <div className="items-start gap-8">
              <div className="pb-5 xl:pb-0">
                <div className="space-y-3 pt-5 xl:space-y-5 xl:pt-10">
                  {productDetails?.usage?.options?.map(
                    (single: string, index: number) => (
                      <div className="step-card flex items-start" key={single}>
                        <div className="step flex rounded-br-full rounded-tr-full bg-white px-3 py-2 sm:px-4">
                          <p className="flex items-center text-lg font-semibold">
                            <FaRegHandPaper />
                            <span> -{convertToBanglaNumber(index + 1)}</span>:
                          </p>
                        </div>
                        <div className="point ml-3">
                          <p>{single}</p>
                        </div>
                      </div>
                    ),
                  )}
                </div>
                <div className="mt-3 flex justify-center xl:mt-8">
                  <OrderButton handleClick={handleScrollToOrderForm} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {productDetails?.awareness?.options?.length ? (
        <section className="why-buy mt-14 sm:mt-16 md:mt-20 xl:mt-24">
          <div className="container mx-auto px-4 sm:px-0">
            <div className="rounded-lg bg-[#092143] py-2 lg:py-4">
              <h1 className="text-center text-xl font-bold text-white sm:text-4xl lg:text-4xl">
                সতর্কতা
              </h1>
            </div>
            <div className="mt-2 flex flex-col gap-2">
              {productDetails?.awareness?.options?.map((aware: string) => (
                <div className="col-1 space-y-4 sm:space-y-7" key={aware}>
                  <div className="usefullnessCard flex items-center">
                    <FaRegHandPaper />
                    <div className="card-text ml-3 ">
                      <span className="font-bold">{aware}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      ) : (
        ""
      )}

      <section className="discount mt-14 sm:mt-16 md:mt-20 xl:mt-24">
        <div className="container mx-auto px-4 sm:px-0">
          <div className="rounded-xl bg-[#092143] py-10">
            <h3 className="mb-5 text-center text-3xl font-bold text-red-400 line-through sm:text-4xl">
              প্রোডাক্টের রেগুলার মূল্য:{" "}
              {convertToBanglaNumber(productDetails?.productDetails?.price)}{" "}
              টাকা
            </h3>
            <h3 className="text-center text-3xl font-bold text-green-400 sm:text-4xl">
              ডিস্কাউন্ট অফারে বর্তমান মূল্য:{" "}
              {convertToBanglaNumber(
                productDetails?.productDetails?.discountPrice,
              )}{" "}
              টাকা
            </h3>
            <div className="mt-5 flex justify-center">
              <OrderButtonTwo handleClick={handleScrollToOrderForm} />
            </div>
          </div>
        </div>
      </section>

      <div>
        <DirectOrderForm productDetails={productDetails?.productDetails} />
      </div>

      {/* <div className="px-2 py-6 text-start md:px-10 md:py-10 lg:px-20 xl:px-40">
        <div className="pb-4 text-center text-2xl font-bold">
          সাধারণ জিজ্ঞাসা
        </div>
        {productDetails?.generalQuestions?.map((single) => (
          <Accordion title={single?.question} key={single?.question}>
            <div
              dangerouslySetInnerHTML={{
                __html: single?.answer ? single?.answer : "<p>No answer</p>",
              }}
            />
          </Accordion>
        ))}
      </div> */}
    </div>
  );
};

export default LandingPageTwoOverview;
