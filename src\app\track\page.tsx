import TrackOrdersOverview from "@/Components/TrackOrderComponents/TrackOrdersOverview";
import ShopLayout from "@/Layouts/ShopLayout";
import { OrdersDetailsResponse } from "@/Types/orderTypes";
import { BASE_URL } from "@/environment/environment";
import { notFound } from "next/navigation";

interface SearchParams {
  orderId: string;
  phone: string;
}
const TrackOrderPage = async ({
  searchParams,
}: {
  searchParams: Promise<SearchParams>;
}) => {
  const { orderId, phone } = await searchParams;
  const data: OrdersDetailsResponse = await getOrderDetails({
    orderId: orderId ?? "",
    phone: phone ?? "",
  });

  return (
    <ShopLayout>
      <TrackOrdersOverview
        orderDetails={data.result}
        orderId={orderId ?? ""}
        phone={phone ?? ""}
      />
    </ShopLayout>
  );
};

export default TrackOrderPage;

async function getOrderDetails({ orderId, phone }: SearchParams) {
  const queryParams = new URLSearchParams();

  if (orderId) queryParams.append("orderId", orderId);
  if (phone) queryParams.append("phone", phone);

  const filter = queryParams.toString();
  const res = await fetch(`${BASE_URL}/orders/track?${filter}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}
