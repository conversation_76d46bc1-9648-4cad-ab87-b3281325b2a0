"use client";
import { SingleOrderDetails } from "@/Types/orderTypes";
import React, { useEffect, useState } from "react";
import CheckoutOrOrderedProducts from "../ReusableComponents/CheckoutOrOrderedProducts/CheckoutOrOrderedProducts";
import Tracking from "../OrderDetails/Tracking";
import TitleViewer from "../ReusableComponents/TitleViewer/TitleViewer";
import { useRouter } from "next/navigation";

const TrackOrdersOverview = ({
  orderDetails,
  orderId,
  phone,
}: {
  orderDetails: SingleOrderDetails;
  orderId: string;
  phone: string;
}) => {
  const router = useRouter();
  const [changedOrderId, setChangedOrderId] = useState<string>();
  const [changedPhoneNumber, setChangedPhoneNumber] = useState<string>();
  useEffect(() => {
    setChangedOrderId(orderId);
    setChangedPhoneNumber(phone);
  }, [phone, orderId]);

  return (
    <div>
      <div className="mt-4 flex items-center justify-center">
        <div className="w-400px flex flex-col gap-2 rounded-lg border-2 border-black p-2 md:flex-nowrap">
          <div className="w-full">
            <label htmlFor="name" className="block text-sm font-normal">
              Order ID:
            </label>
            <input
              type="text"
              id="orderId"
              name="orderId"
              value={changedOrderId}
              onChange={(e) => setChangedOrderId(e.target.value)}
              className="w-full rounded border p-2 text-sm font-normal"
            />
          </div>
          <div className="w-full">
            <label htmlFor="name" className="block text-sm font-normal">
              Phone Number:
            </label>
            <input
              type="text"
              id="phone"
              name="phone"
              value={changedPhoneNumber}
              onChange={(e) => setChangedPhoneNumber(e.target.value)}
              className="w-full rounded border p-2 text-sm font-normal"
            />
          </div>
          <div className="flex justify-center">
            <button
              type="button"
              className="bg-primary w-full rounded-lg px-4 py-2 text-sm font-normal text-white disabled:bg-gray-400"
              onClick={() =>
                router.push(
                  `/track?orderId=${changedOrderId}&phone=${changedPhoneNumber}`,
                )
              }
              // disabled={!formik.dirty}
            >
              Search
            </button>
          </div>
        </div>
      </div>
      {orderDetails?.orderId ? (
        <>
          <Tracking orderDetails={orderDetails!} />
          <TitleViewer
            titleTwo="Order Details"
            titleOne=""
            seeAllButton={false}
          />
          <table className="w-full table-auto">
            <tbody>
              <tr className="bg-gray-100">
                <td className="border px-4 py-2 text-sm font-normal">
                  Order No
                </td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  {orderDetails?.orderId}
                </td>
              </tr>
              <tr className="bg-white">
                <td className="border px-4 py-2 text-sm font-normal">Status</td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  {orderDetails?.orderStatus}
                </td>
              </tr>
              <tr className="bg-gray-100">
                <td className="border px-4 py-2 text-sm font-normal">Name</td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  {orderDetails?.name}
                </td>
              </tr>
              <tr className="bg-white">
                <td className="border px-4 py-2 text-sm font-normal">Phone </td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  {orderDetails?.phoneNumber}
                </td>
              </tr>
              <tr className="bg-gray-100">
                <td className="border px-4 py-2 text-sm font-normal">Email</td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  {orderDetails?.email}
                </td>
              </tr>
              <tr className="bg-white">
                <td className="border px-4 py-2 text-sm font-normal">
                  Delivery Address
                </td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  {orderDetails?.deliveryAddress}
                </td>
              </tr>
              <tr className="bg-gray-100">
                <td className="border px-4 py-2 text-sm font-normal">
                  Product Price
                </td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  ৳{orderDetails?.productPrice}
                </td>
              </tr>
              <tr className="bg-white">
                <td className="border px-4 py-2 text-sm font-normal">
                  Discount
                </td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  -৳{orderDetails?.discount}
                </td>
              </tr>
              <tr className="bg-gray-100">
                <td className="border px-4 py-2 text-sm font-normal">Coupon</td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  ৳{orderDetails?.couponDiscount}
                </td>
              </tr>
              <tr className="bg-white">
                <td className="border px-4 py-2 text-sm font-normal">
                  Delivery Charge
                </td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  +৳{orderDetails?.deliveryCharge}
                </td>
              </tr>
              <tr className="bg-gray-100">
                <td className="border px-4 py-2 text-sm font-normal">Total</td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  ৳{orderDetails?.totalAmount}
                </td>
              </tr>
              <tr className="bg-white">
                <td className="border px-4 py-2 text-sm font-normal">
                  Paid Amount
                </td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  -৳{orderDetails?.paidAmount}
                </td>
              </tr>
              <tr className="bg-gray-100">
                <td className="border px-4 py-2 text-sm font-normal">
                  Due Amount
                </td>
                <td className="border px-4 py-2 text-right text-sm font-normal">
                  ৳{orderDetails?.dueAmount}
                </td>
              </tr>
            </tbody>
          </table>
          <CheckoutOrOrderedProducts products={orderDetails?.products} />
        </>
      ) : (
        <p>No Order details found</p>
      )}
    </div>
  );
};

export default TrackOrdersOverview;
