"use client";
import { discountPercentage } from "@/Components/utils/PriceCalculator";
import { notLoggedInModal } from "@/Components/utils/commonModal";
import { ROUTES } from "@/Routes";
import { SingleProductOfList } from "@/Types/productTypes";
import { addToWishListApi } from "@/services/wishlistServices";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useContext, useState } from "react";
import { toast } from "react-hot-toast";
import { AiFillHeart, AiOutlineHeart } from "react-icons/ai";
import AddToCartModal from "../AddToCartModal/AddToCartModal";

import ProductsListSkeleton from "@/Components/ReusableComponents/Loaders/ProductListSkeleton";
import Modal from "@/Components/ReusableComponents/Modal/Modal";
import { NoProductsFound } from "@/Components/ReusableComponents/NoResultFound/NoResultFound";
import ScrollRevealWrapper from "@/Components/ReusableComponents/ScrollRevealWrapper/ScrollRevealWrapper";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { addToCartApi } from "@/services/cartServices";
import Link from "next/link";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Autoplay } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

type Props = {
  productList?: SingleProductOfList[] | undefined;
  loading: boolean;
  accessToken: string;
};
const ProductsListCarousel = ({ productList, loading, accessToken }: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { wishlist, handleAddToLocalCart }: ContextDataType =
    useContext(LocalDataContext);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [selectedProductInfo, setSelectedProductInfo] =
    useState<SingleProductOfList>();

  const { api: AddToWishlistApi } = addToWishListApi();

  const { mutateAsync: addToWishlistMutateAsync } = useMutation(
    AddToWishlistApi,
    {
      onSuccess: () => {
        // refetch();
        queryClient.invalidateQueries();
      },
    },
  );

  const handleAddToWishlist = async (product: SingleProductOfList) => {
    const productInfo = {
      productId: product._id,
      ...product,
    };

    if (accessToken) {
      toast.promise(
        addToWishlistMutateAsync(productInfo),
        {
          success: "Product Added to Wishlist Successful",
          error: "Error Adding Product to Wishlist",
          loading: "Adding Product to Wishlist",
        },
        {
          id: "add-to-wishlist-product",
        },
      );
    } else {
      notLoggedInModal(() => router.push(ROUTES.LOG_IN("")));
    }
  };

  const { api: AddToCartApi } = addToCartApi();

  const { mutateAsync: addToCartMutateAsync } = useMutation(AddToCartApi, {
    onSuccess: () => {
      queryClient.invalidateQueries();
    },
  });

  const handleAddToCart = async (product: SingleProductOfList) => {
    const productInfo = {
      productId: product?._id || "",
      quantity: 1,
      price: product?.discountPrice,
    };

    if (accessToken) {
      toast.promise(
        addToCartMutateAsync(productInfo),
        {
          success: "Product Added to cart Successful",
          error: "Error Adding Product to cart",
          loading: "Adding Product to cart",
        },
        {
          id: "add-to-cart-product",
        },
      );
    } else {
      handleAddToLocalCart({ ...product, quantity: 1 });
      toast.success("Product Added to cart Successful");
    }
  };

  return (
    <ScrollRevealWrapper>
      {!loading ? (
        <>
          {productList?.length ? (
            <Swiper
              effect="coverflow"
              grabCursor={true}
              centeredSlides={false}
              slidesPerView={2}
              spaceBetween={6}
              coverflowEffect={{
                rotate: 0,
                stretch: 10,
                depth: 100,
                modifier: 1,
                slideShadows: true,
              }}
              pagination={{ clickable: true }}
              className="mySwiper"
              autoplay={{
                delay: 3000,
                disableOnInteraction: false,
                reverseDirection: false,
              }}
              loop={true}
              breakpoints={{
                1024: {
                  slidesPerView: 4,
                },
                768: {
                  slidesPerView: 2,
                },
              }}
              modules={[Autoplay]}
            >
              {productList?.map((product: SingleProductOfList) => {
                const isProductInWishlist = wishlist?.some(
                  (wishlistProduct: any) =>
                    wishlistProduct.productId === product.id,
                );
                return (
                  <SwiperSlide key={product.id}>
                    <Link href={ROUTES.PRODUCTS.VIEW(product?.slug)}>
                      <div className="overflow-hidden rounded-lg border border-gray-300 pb-0 shadow-md hover:border-[#092143]">
                        <div className="relative  h-[167px] rounded-t-lg border-gray-200 bg-[#F4F4F4] sm:h-[272px] md:h-[206px] lg:h-[181px] xl:h-[190px] 2xl:h-[304px]">
                          {/* Image */}
                          <Image
                            src={generateProductImage(product.mainImageUrl)}
                            alt={product.name}
                            width={100}
                            height={100}
                            className="h-full w-full transform rounded-t-lg transition-all duration-700 ease-in-out hover:scale-110"
                          />

                          <div className="absolute right-0 top-0">
                            {discountPercentage(
                              product.price,
                              product.discountPrice,
                            ) > 0 ? (
                              <p className="rounded-bl-lg rounded-tr-lg bg-primary px-2 py-2  text-xs font-semibold text-white">
                                {discountPercentage(
                                  product.price,
                                  product.discountPrice,
                                )}
                                %
                                <br />
                                OFF
                              </p>
                            ) : (
                              ""
                            )}
                            {/* {product?.deal ? (
                          <p className="text-sm font-semibold  bg-red-500 text-white rounded-2xl  px-4 py-1 mt-1">
                            {product.deal}
                          </p>
                        ) : (
                          ""
                        )} */}
                          </div>
                        </div>
                        <div className="px-1 pb-0 pt-3 md:px-2">
                          <p className="mb-2 h-8 text-xs font-semibold text-black hover:cursor-pointer hover:underline md:h-10">
                            {product?.name}
                          </p>
                          <div className="mt-4 flex items-center justify-between md:mt-0">
                            <h2 className="text-lg font-semibold md:text-xl">
                              ৳ {product.discountPrice}{" "}
                              {product.price !== product.discountPrice ? (
                                <span className="text-xs text-slate-500 line-through md:text-sm">
                                  ৳{product.price}
                                </span>
                              ) : (
                                ""
                              )}
                            </h2>
                            <button
                              className="rounded-lg bg-white p-1"
                              onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                handleAddToWishlist(product);
                              }}
                              disabled={!product.isAvailable}
                            >
                              {isProductInWishlist ? (
                                <AiFillHeart
                                  className={`text-2xl ${
                                    isProductInWishlist
                                      ? "text-red-500"
                                      : "text-black"
                                  }`}
                                />
                              ) : (
                                <AiOutlineHeart
                                  className={`text-2xl ${
                                    isProductInWishlist
                                      ? "text-red-500"
                                      : "text-black"
                                  }`}
                                />
                              )}
                            </button>
                          </div>

                          {/* Add to Cart Button */}
                        </div>
                        <div className="flex gap-1">
                          <button
                            className="mt-4 w-full rounded-bl-md bg-primary py-2 text-xs font-semibold text-white hover:bg-orange-400 disabled:bg-gray-400"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              handleAddToCart(product);
                            }}
                            disabled={!product.isAvailable}
                          >
                            Add to Cart
                          </button>
                          <button
                            className="mt-4 w-full rounded-br-md bg-primary py-2 text-xs font-semibold text-white hover:bg-orange-400 disabled:bg-gray-400"
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              setSelectedProductInfo(product);
                              setShowModal(true);
                            }}
                            disabled={!product.isAvailable}
                          >
                            Buy Now
                          </button>
                        </div>
                      </div>
                    </Link>
                  </SwiperSlide>
                );
              })}
            </Swiper>
          ) : (
            <NoProductsFound />
          )}
        </>
      ) : (
        <ProductsListSkeleton />
      )}
      <Modal showModal={showModal} setShowModal={setShowModal}>
        <div className="flex w-full items-center justify-center">
          <AddToCartModal
            product={selectedProductInfo!}
            handleClose={() => setShowModal(false)}
            accessToken={accessToken}
          />
        </div>
      </Modal>
    </ScrollRevealWrapper>
  );
};

export default ProductsListCarousel;
