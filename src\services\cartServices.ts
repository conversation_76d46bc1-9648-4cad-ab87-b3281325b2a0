import { CartListType } from "@/Types/cartPageTypes";
import { BASE_URL } from "@/environment/environment";
import axios from "axios";
interface Cart {
  productId: string;
  quantity: number;
}

export const addToCartApi = () => {
  return {
    api(data: Cart) {
      return axios
        .post(`${BASE_URL}/cart/add-new`, data)
        .then(({ data }) => data);
    },
    getKey() {
      return ["addToCartApi"];
    },
  };
};

export const getCartApi = () => {
  return {
    api() {
      return axios
        .get<CartListType>(`${BASE_URL}/cart/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getCartApi"];
    },
  };
};

export const removeFromCart = () => {
  return {
    api(id: string) {
      return axios
        .delete(`${BASE_URL}/cart/delete/${id}`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["removeFromCart"];
    },
  };
};

export const removeAllFromCart = () => {
  return {
    api() {
      return axios
        .delete(`${BASE_URL}/cart/delete-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["removeAllFromCart"];
    },
  };
};
