"use client";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { SingleLocalCartItem } from "@/Types/localCartTypes";
import { useContext, useEffect, useState } from "react";
import { IoCartOutline } from "react-icons/io5";
import RightSideCartSlider from "../RightSideCartSlider/RightSideCartSlider";
import { BsHandbagFill } from "react-icons/bs";

const RightSideCartIcon = ({ accessToken }: { accessToken?: string }) => {
  const [openCartSlider, setOpenCartSlider] = useState<boolean>(false);
  const { localCartItems, userHeaderDetails }: ContextDataType =
    useContext(LocalDataContext);
  const [totalPrice, setTotalPrice] = useState<number>(0);

  useEffect(() => {
    let total = 0;
    if (localCartItems?.length) {
      localCartItems?.map((single: SingleLocalCartItem) => {
        total += single.discountPrice * single.quantity;
      });
    }
    setTotalPrice(total);
  }, [localCartItems]);

  const [count, setCount] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCount((prevCount) => {
        if (prevCount < totalPrice) {
          return prevCount + 55;
        } else {
          prevCount = totalPrice;
          clearInterval(interval); // Stop counting when reach 100
          return prevCount;
        }
      });
    }, 10);

    return () => clearInterval(interval); // Cleanup on unmount
    //need to change
  }, [totalPrice]);

  return (
    <div
      className="border-primary  flex cursor-pointer  flex-col items-center rounded-l-lg border-2 border-r-0 bg-black pt-1 text-white md:pt-2"
      onClick={() =>
        // router.push(accessToken ? ROUTES.CART : ROUTES.CART)
        setOpenCartSlider(true)
      }
    >
      <BsHandbagFill className="text-xl md:text-3xl" />
      <span className="pr-1 text-xs md:text-sm">
        {" "}
        {userHeaderDetails
          ? userHeaderDetails?.cartCount
          : localCartItems?.length || 0}{" "}
        Items
      </span>
      <span className="bg-primary w-full px-1 py-1 text-xs text-white md:rounded-bl-sm md:px-2 md:text-sm">
        ৳ {userHeaderDetails ? userHeaderDetails?.totalCartPrice : totalPrice}
        {/* <div>{count}</div> */}
      </span>
      <RightSideCartSlider
        openCartSlider={openCartSlider}
        setOpenCartSlider={setOpenCartSlider}
        accessToken={accessToken ? accessToken : ""}
      />
    </div>
  );
};

export default RightSideCartIcon;
