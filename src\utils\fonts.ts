import { <PERSON><PERSON>, <PERSON>_<PERSON>, <PERSON><PERSON>_<PERSON> } from "next/font/google";

export const lato = Lato({
  subsets: ["latin"],
  weight: ["100", "300", "400", "700", "900"],
  display: "swap",
});

export const tenor = Tenor_Sans({
  subsets: ["latin"],
  weight: ["400"],
  display: "swap",
});

export const saint = Mrs_<PERSON>_<PERSON>({
  subsets: ["latin"],
  weight: ["400"],
  display: "swap",
});
