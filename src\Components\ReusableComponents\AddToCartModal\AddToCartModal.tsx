import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import {
  maximumQuantityModal,
  minimumQuantityModal,
} from "@/Components/utils/commonModal";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import { SingleProductOfList } from "@/Types/productTypes";
import { addToCheckoutApi } from "@/services/checkoutServices";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useContext } from "react";
import { toast } from "react-hot-toast";
interface Props {
  product: SingleProductOfList;
  handleClose: () => void;
  accessToken: string;
}
const AddToCartModal = ({ product, handleClose, accessToken }: Props) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { handleAddToLocalCheckout }: ContextDataType =
    useContext(LocalDataContext);
  const [quantity, setQuantity] = React.useState<number>(1);
  const handleChangeQuantity = (type: string) => {
    if (type === "increase") {
      if (quantity === 5) {
        maximumQuantityModal();
      } else {
        setQuantity(quantity + 1);
      }
    } else {
      if (quantity > 1) {
        setQuantity(quantity - 1);
      } else {
        minimumQuantityModal();
      }
    }
  };

  const { api: AddToCheckoutApi } = addToCheckoutApi();

  const { mutateAsync: addToCartMutateAsync } = useMutation(AddToCheckoutApi, {
    onSuccess: () => {
      // refetch();
      handleClose();
      router.push(ROUTES.CHECKOUT);
      queryClient.invalidateQueries();
      setQuantity(1);
    },
  });

  const handleAddToCheckout = async () => {
    const data = {
      products: [
        {
          productDetails: product?._id,
          quantity: quantity,
        },
      ],
    };

    if (accessToken) {
      toast.promise(
        addToCartMutateAsync(data),
        {
          success: "Product Added for checkout Successful",
          error: "Error Adding Product to checkout",
          loading: "Adding Product to checkout",
        },
        {
          id: "add-to-checkout-product",
        },
      );
    } else {
      const products = [
        {
          id: product?.id,
          name: product?.name,
          brand: product?.brand,
          size: product?.size,
          image: product?.mainImageUrl,
          discountPrice: product?.discountPrice,
          price: product?.price,
          quantity: quantity,
          productCategory: product?.productCategory,
        },
      ];
      handleAddToLocalCheckout(products);
      toast.success("Added for checkout");
      router.push(ROUTES.CHECKOUT);
    }
  };
  return (
    <div className=" w-full rounded-2xl bg-slate-300 p-4 text-black md:max-w-[800px]">
      <div>
        <div className="flex flex-wrap items-start gap-4 md:flex-nowrap">
          <div className="flex w-full items-start justify-between md:block md:w-[160px]">
            <Image
              src={generateProductImage(product.mainImageUrl)}
              alt="product"
              height={100}
              width={100}
              style={{ width: "150px", height: "150px", borderRadius: "10px" }}
            />
            <button
              className="block h-[30px] rounded-lg bg-red-600 px-2 text-white md:hidden"
              onClick={() => handleClose()}
            >
              X
            </button>
          </div>
          <div>
            <p className="mb-2 text-lg font-semibold text-black md:text-xl">
              {product.name} - {product.size}
            </p>
            <div className="flex items-center gap-4 py-2 font-semibold">
              <p className="font-sm font-semibold">Brand : {product.brand}</p>
              <p className="font-sm font-semibold">
                Status : {product.isAvailable ? "In Stock" : "out of stock"}
              </p>
            </div>
            <div className="flex items-center gap-4">
              <h2 className="text-xl font-semibold md:text-2xl">
                ৳ {product.discountPrice}{" "}
                {product.price !== product.discountPrice ? (
                  <span className="text-sm text-slate-500 line-through">
                    ৳{product.price}
                  </span>
                ) : (
                  ""
                )}
              </h2>
              <div className="border-primary mb-4 mt-2 flex w-[150px] items-center justify-between rounded-lg border-2">
                <button
                  className="rounded-l-lg bg-gray-200 px-4 py-2"
                  onClick={() => handleChangeQuantity("decrease")}
                >
                  -
                </button>
                <span className="mx-2">{quantity}</span>
                <button
                  className="rounded-r-lg bg-gray-200 px-4 py-2"
                  onClick={() => handleChangeQuantity("increase")}
                >
                  +
                </button>
              </div>
            </div>
          </div>
          <button
            className="hidden rounded-lg bg-red-600 px-2 text-white md:block"
            onClick={() => handleClose()}
          >
            X
          </button>
        </div>
      </div>
      <div className="mt-2 flex items-center justify-center">
        <button
          className="bg-primary rounded-lg px-4 py-2 text-sm font-medium text-white disabled:bg-slate-500"
          onClick={() => handleAddToCheckout()}
          disabled={quantity < 1 || !product.isAvailable}
        >
          Proceed To Checkout
        </button>
      </div>
    </div>
  );
};

export default AddToCartModal;
