import { BASE_URL } from "@/environment/environment";
import axios from "axios";

export interface BlogsResponse {
  message: string;
  results: SingleBlogDetails[];
  status: boolean;
}

export interface SingleBlogDetails {
  _id: string;
  id: string;
  title: string;
  category: string;
  created_by: string;
  brief: string;
  image: string;
  descriptions: BlogDescription[];
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface BlogDescription {
  content: string;
  image: string;
  _id: string;
}

export interface SingleBlogDetailsResponse {
  message: string;
  result: SingleBlogDetails;
  relatedBlogs: SingleBlogDetails[];
  status: boolean;
}

export const getBlogsApi = () => {
  return {
    api() {
      return axios
        .get<BlogsResponse>(`${BASE_URL}/blogs/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getBlogsApi"];
    },
  };
};

export const getSingleBlogDetailsApi = (id: string) => {
  return {
    api() {
      return axios
        .get<any>(`${BASE_URL}/blogs/single/${id}`)
        .then(({ data }) => data);
    },
    getKey() {
      return [`getSingleBlogDetailsApi`, id];
    },
  };
};
