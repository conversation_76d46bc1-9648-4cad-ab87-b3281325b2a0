"use client";
import BrandList<PERSON>arousel from "@/Components/ReusableComponents/BrandListCarousel/BrandListCarousel";
import ProductsListSkeleton from "@/Components/ReusableComponents/Loaders/ProductListSkeleton";
import ViewMoreButton from "@/Components/ReusableComponents/MoreButtons/ViewMoreButton";
import OfferSection from "@/Components/ReusableComponents/OfferSection/OfferSection";
import ProductsListViewer from "@/Components/ReusableComponents/ProductsListViewer/ProductsListViewer";
import TitleViwerModern from "@/Components/ReusableComponents/TitleViewer/TitleViwerModern";
import TtileViewerMiddle from "@/Components/ReusableComponents/TitleViewer/TtileViewerMiddle";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import { ROUTES } from "@/Routes";
import {
  getHomePageContentListApi,
  SingleHomeContent,
} from "@/services/productsServices";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useContext, useEffect } from "react";
import BlogAndOVideoSection from "../BlogAndOVideoSection/BlogAndOVideoSection";
import FeaterCategoriesServer from "../FeaturedCategories/FeaterCategoriesServer";
import AddsBanner from "../LandingPageBanner/AddsBanner";
import LandingPageBannerTwo from "../LandingPageBanner/LandingPageBannerTwo";
import SkinCareOverview from "../SkinCareSection/SkinCareOverview";
import { HomePageContentResponse } from "./homePageContentTypes";
import HomepageSingleContentWithApi from "./HomepageSingleContentWithApi";

interface Props {
  accessToken: string;
  data: HomePageContentResponse;
}

const LandingPageUpdatedOverview = ({ accessToken, data }: Props) => {
  const router = useRouter();
  const { userHeaderDetails }: ContextDataType = useContext(LocalDataContext);

  const { api: homepageContentApi, getKey: homepageContentGetKey } =
    getHomePageContentListApi();
  const { data: homepageContent, isLoading: isHomepageContentLoading } =
    useQuery(homepageContentGetKey(), homepageContentApi, {
      refetchOnWindowFocus: false,
    });

  useEffect(() => {
    const data = {
      event: "page_view",
      url: window.location.href,
      value: 0,
      user_log_in_status: userHeaderDetails?.userDetails?.name
        ? "logged in"
        : "not logged in",
      user_id: userHeaderDetails?.userDetails?._id ?? "",
      user_name: userHeaderDetails?.userDetails?.name ?? "",
      user_phone_number: userHeaderDetails?.userDetails?.mobileNumber ?? "",
      user_email: userHeaderDetails?.userDetails?.email ?? "",
      user_zip: userHeaderDetails?.userDetails?.zipCode ?? "",
      user_district: userHeaderDetails?.userDetails?.district ?? "",
      items: null,
    };
    gtmDataLayerDataPass(data);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="bg-[#FAFAFA]">
      <div className="h-[22vh] sm:h-[40vh] md:h-[35vh] lg:h-[40vh] xl:h-[55vh] 2xl:h-[60vh]">
        <LandingPageBannerTwo banners={data.results.banners} />
      </div>
      <div className="px-4 md:px-6  lg:px-20 xl:px-28">
        <div className="mt-5 sm:mt-8 md:mt-16">
          <TitleViwerModern
            titleOne="Featured"
            titleTwo="Categories"
            seeAllButton={true}
            handleClick={() => router.push("/offers?page=1")}
          />
          {data.results.categories.length ? (
            <FeaterCategoriesServer categories={data.results.categories} />
          ) : (
            ""
          )}
        </div>
        <div className="mt-5 sm:mt-8 md:mt-16">
          <SkinCareOverview />
        </div>
        <div className="mt-8">
          <ProductsListViewer
            productList={data.results.homeProducts}
            loading={!data?.results?.homeProducts?.length}
            accessToken={accessToken}
            xxl={6}
            xl={5}
            lg={4}
            md={3}
            sm={2}
          />
          <ViewMoreButton handleClick={() => router.push("/products?page=1")} />
        </div>
        <OfferSection />
        <div className="mt-8">
          <TtileViewerMiddle
            titleOne="New Arrival"
            titleTwo="INNI"
          />
          <ProductsListViewer
            productList={data.results.newArrivalProducts}
            loading={!data?.results?.newArrivalProducts?.length}
            accessToken={accessToken}
            xxl={6}
            xl={5}
            lg={4}
            md={3}
            sm={2}
          />
          <ViewMoreButton
            handleClick={() => router.push("/new-arrival?page=1")}
          />
        </div>
        <div>
          {!isHomepageContentLoading ? (
            homepageContent?.results?.map((single: SingleHomeContent) => (
              <HomepageSingleContentWithApi
                productType={single?.selectedType}
                imageUrl={single?.imageUrl}
                key={single?._id}
                accessToken={accessToken}
                productTypeSlug={single?.selectedOption}
                name={single?.name}
              />
            ))
          ) : (
            <ProductsListSkeleton />
          )}
        </div>
        <div className="mt-8">
          <BlogAndOVideoSection />
        </div>
        <div className="mt-8">
          <AddsBanner />
        </div>
        <div className="mt-8">
          <TitleViwerModern
            titleOne="Shop From"
            titleTwo="Brand"
            seeAllButton={true}
            handleClick={() => router.push(ROUTES.BRANDS.HOME)}
          />
          <BrandListCarousel />
        </div>
      </div>
    </div>
  );
};

export default LandingPageUpdatedOverview;
