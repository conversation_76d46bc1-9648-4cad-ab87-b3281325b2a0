"use client";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import {
  LandingPageProductDetails,
  WhyBest,
} from "@/Types/landingPageProductType";
import { convertToBanglaNumber } from "@/utils/EnglishToBanglaConvert";
import Image from "next/image";
import { GiMoebiusStar, GiStarShuriken } from "react-icons/gi";
import logo from "../../../../src/dummyImages/logo.png";
import Accordion from "../Accordion";
import DirectOrderForm from "../DirectOrderForm";
import {
  OrderButtonVersionOne,
  OrderButtonVersionthree,
  OrderButtonVersionTwo,
} from "./CommonOrderNowButton";
import GetCustomerTrackUTMSource from "@/utils/CustomerPlatformSourceTrack";

interface Props {
  productDetails: LandingPageProductDetails;
}

const LandingPageThreeOverview = ({ productDetails }: Props) => {
  const handleScrollToOrderForm = () => {
    const orderFormDiv = document?.getElementById("order-form");
    if (orderFormDiv) {
      orderFormDiv?.scrollIntoView({ behavior: "smooth" });
    }
  };
  return (
    <div className="landing-page-two">
      <section className="hero-section bg-gradient-to-b from-[#F48F4B96] to-white">
        <div className="container mx-auto px-4 sm:px-0">
          <div className="flex justify-center">
            <div className="max-w-full sm:max-w-6xl">
              <div className="flex justify-center pb-5 pt-4">
                <Image src={logo} alt="logo" width={200} height={100} />
              </div>
              <h1 className="text-center text-3xl font-bold leading-[1.4em] sm:text-5xl sm:leading-[1.5em]">
                {productDetails?.title}
              </h1>
              <div className="mt-3 text-center text-xl sm:text-2xl">
                <div
                  className="dangerouslyHtml"
                  dangerouslySetInnerHTML={{
                    __html: productDetails?.punchLine
                      ? productDetails?.punchLine
                      : "<p>No answer</p>",
                  }}
                />{" "}
                মাএ{" "}
                <span className="text-red-500 line-through">
                  {convertToBanglaNumber(productDetails?.productDetails?.price)}
                </span>{" "}
                <span className="text-3xl font-semibold text-green-700">
                  {convertToBanglaNumber(
                    productDetails?.productDetails?.discountPrice,
                  )}
                </span>{" "}
                টাকায়
              </div>
              <div className="mb-6 mt-5 flex justify-center">
                <OrderButtonVersionOne handleClick={handleScrollToOrderForm} />
              </div>
              {productDetails?.youtubeUrl ? (
                <iframe
                  className="md:min-h-96 aspect-video w-full self-stretch"
                  src={productDetails?.youtubeUrl}
                  frameBorder="0"
                  title="Product Overview Video"
                  aria-hidden="true"
                />
              ) : (
                <div>
                  <div className="h-[450px] max-w-6xl rounded-xl border-2 border-white bg-white p-4 shadow-2xl md:h-[550px]">
                    <video
                      src={
                        productDetails?.video
                          ? generateProductImage(productDetails?.video)
                          : "https://beautysiaa.com/wp-content/uploads/2023/11/bb-cream-jahsbf.mp4"
                      }
                      className="h-full w-full"
                      controls
                      autoPlay
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {productDetails?.sections?.map((section: WhyBest, index: number) => {
        return index % 2 === 0 ? (
          <section className="why-buy mt-14 sm:mt-16 md:mt-20 xl:mt-24">
            <div className="container mx-auto px-4 sm:px-0">
              <div className="rounded-lg bg-secondary py-4">
                <h1 className="text-center text-3xl font-bold text-white sm:text-4xl">
                  {section?.title}
                </h1>
              </div>
              <div className="mt-10 grid grid-cols-1 items-center gap-8 sm:grid-cols-2 xl:grid-cols-3">
                <div className="col-1 space-y-4 sm:space-y-7">
                  {section?.options
                    ?.slice(0, Math.ceil(section?.options?.length / 2))
                    .map((single: string) => (
                      <div
                        className="usefullnessCard flex items-center"
                        key={single}
                      >
                        <div className="icon rounded-full bg-secondary p-2">
                          <GiStarShuriken className="text-3xl text-white" />
                        </div>
                        <div className="card-text ml-3">
                          <h3>{single}</h3>
                        </div>
                      </div>
                    ))}
                </div>
                <div className="col-2 hidden xl:block">
                  <Image
                    src={generateProductImage(section?.image)}
                    alt="logo"
                    width={100}
                    height={100}
                    className="h-auto w-full rounded-xl"
                  />
                </div>
                <div className="col-3 rtl-md space-y-4 sm:space-y-7">
                  {section?.options
                    ?.slice(
                      Math.ceil(section?.options?.length / 2),
                      Number(section?.options?.length),
                    )
                    .map((single: string) => (
                      <div
                        className="usefullnessCard flex items-center"
                        key={single}
                      >
                        <div className="icon rounded-full bg-secondary p-2">
                          <GiStarShuriken className="text-3xl text-white" />
                        </div>
                        <div className="card-text ml-3">
                          <h3>{single}</h3>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
              <div className="mt-8 flex justify-center">
                <OrderButtonVersionOne handleClick={handleScrollToOrderForm} />
              </div>
            </div>
          </section>
        ) : (
          <section className="how-to-use mt-14 sm:mt-16 md:mt-20 xl:mt-24">
            <div className="container mx-auto px-4 sm:px-0">
              <div className="rounded-tl-lg rounded-tr-lg bg-secondary py-4">
                <h1 className="text-center text-3xl font-bold text-white sm:text-4xl">
                  {section?.title}
                </h1>
              </div>
              <div className="bg-gradient-to-b from-[#F48F4B86] to-[#F48F4B27]">
                <div className="grid grid-cols-1 items-start gap-8 xl:grid-cols-2">
                  <div className="pb-5 xl:pb-0">
                    <div className="space-y-3 pt-5 xl:space-y-5 xl:pt-10">
                      {section?.options?.map(
                        (single: string, index: number) => (
                          <div
                            className="step-card flex items-center"
                            key={single}
                          >
                            <div className="point ml-3">
                              <p>{single}</p>
                            </div>
                          </div>
                        ),
                      )}
                    </div>
                    <div className="mt-3 flex justify-center xl:mt-8">
                      <OrderButtonVersionOne
                        handleClick={handleScrollToOrderForm}
                      />
                    </div>
                  </div>
                  <div className="hidden xl:block">
                    <Image
                      src={generateProductImage(section?.image)}
                      alt="logo"
                      width={100}
                      height={100}
                      className="h-auto max-h-[600px] w-full"
                    />
                  </div>
                </div>
              </div>
            </div>
          </section>
        );
      })}

      <section className="discount mt-14 sm:mt-16 md:mt-20 xl:mt-24">
        <div className="container mx-auto px-4 sm:px-0">
          <div className="rounded-xl bg-secondary py-10">
            <h3 className="mb-5 text-center text-3xl font-bold text-gray-800 line-through sm:text-4xl">
              প্রোডাক্টের রেগুলার মূল্য:{" "}
              {convertToBanglaNumber(productDetails?.productDetails?.price)}{" "}
              টাকা
            </h3>
            <h3 className="text-center text-3xl font-bold text-white sm:text-4xl">
              ডিস্কাউন্ট অফারে বর্তমান মূল্য:{" "}
              {convertToBanglaNumber(
                productDetails?.productDetails?.discountPrice,
              )}{" "}
              টাকা
            </h3>
            <div className="mt-5 flex justify-center">
              <OrderButtonVersionTwo handleClick={handleScrollToOrderForm} />
            </div>
          </div>
        </div>
      </section>

      <div>
        <DirectOrderForm productDetails={productDetails?.productDetails} />
      </div>

      {productDetails?.generalQuestions?.length ? (
        <div className="px-2 py-6 text-start md:px-10 md:py-10 lg:px-20 xl:px-40">
          <div className="pb-4 text-center text-2xl font-bold">
            সাধারণ জিজ্ঞাসা
          </div>
          {productDetails?.generalQuestions?.map((single) => (
            <Accordion title={single?.question} key={single?.question}>
              <div
                dangerouslySetInnerHTML={{
                  __html: single?.answer ? single?.answer : "<p>No answer</p>",
                }}
              />
            </Accordion>
          ))}
        </div>
      ) : (
        ""
      )}
      <GetCustomerTrackUTMSource />
    </div>
  );
};

export default LandingPageThreeOverview;
