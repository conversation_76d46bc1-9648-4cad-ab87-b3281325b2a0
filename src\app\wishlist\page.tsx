import WishlistPageOverview from "@/Components/WishlistComponents/WishlistPageOverview";
import ShopLayout from "@/Layouts/ShopLayout";
import { ROUTES } from "@/Routes";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Wishlist | INNI",
};

const WishlistPage = async () => {
  const accessToken = (await cookies()).get("GMK")?.value;

  if (!accessToken) {
    redirect(ROUTES.LOG_IN(ROUTES.WISHLIST));
    return null;
  }
  return (
    <ShopLayout>
      <WishlistPageOverview accessToken={accessToken ? accessToken : ""} />
    </ShopLayout>
  );
};

export default WishlistPage;
