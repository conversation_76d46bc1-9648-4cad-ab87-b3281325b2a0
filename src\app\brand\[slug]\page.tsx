import ProductsPage from "@/Components/ProductsPage/ProductsPage";
import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import ShopLayout from "@/Layouts/ShopLayout";
import { BASE_URL } from "@/environment/environment";
import { GetSingleBrandDetailsResponse } from "@/services/bannerBrandCategoryServices";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";

interface Props {
  params: Promise<{ slug: string }>;
  searchParams: Promise<SearchParams>;
}

interface SearchParams {
  page: string;
  brand: string;
}

export const generateMetadata = async ({
  params,
}: Props): Promise<Metadata> => {
  const { slug } = await params;
  const data: GetSingleBrandDetailsResponse = await getBrandDetails(slug);
  return {
    title: data?.result?.metaTitle ?? data?.result?.name,
    description: data?.result?.metaDescription,
    openGraph: {
      title: data?.result?.metaTitle ?? data?.result?.metaTitle,
      description: data?.result?.metaDescription,
      images: [generateProductImage(data?.result?.imageUrl)],
    },
  };
};

const BrandPage = async ({ params, searchParams }: Props) => {
  const accessToken = (await cookies()).get("GMK")?.value;
  const { page } = await searchParams;
  const { slug } = await params;
  const data = await getProductList({ page: page, brand: slug });
  const brandData: GetSingleBrandDetailsResponse = await getBrandDetails(slug);

  return (
    <ShopLayout>
      <ProductsPage
        accessToken={accessToken ? accessToken : ""}
        data={data}
        pageType="brand"
        brandName={slug}
        description={brandData?.result?.description}
      />
    </ShopLayout>
  );
};

export default BrandPage;

async function getProductList({ page, brand }: SearchParams) {
  const queryParams = new URLSearchParams();

  // if (size) queryParams.append("size", size.toString());
  if (page) queryParams.append("page", page.toString());
  // if (category) queryParams.append("productCategory", category);
  // if (productType) queryParams.append("productType", productType);
  // if (skinType) queryParams.append("skinType", skinType);
  if (brand) queryParams.append("brand", brand);
  // if (skinConcern) queryParams.append("skinConcern", skinConcern);

  const filter = queryParams.toString();
  const res = await fetch(`${BASE_URL}/products/get-all?${filter}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}

async function getBrandDetails(slug: string) {
  const res = await fetch(`${BASE_URL}/brands/single/${slug}`, {
    // cache: "no-cache",
    next: { revalidate: 0 },
  });

  if (!res.ok) {
    notFound();
    throw new Error("Failed to fetch data");
  }

  return res.json();
}
