"use client";
import BrandListCarousel from "@/Components/ReusableComponents/BrandListCarousel/BrandListCarousel";
import ViewMoreButton from "@/Components/ReusableComponents/MoreButtons/ViewMoreButton";
import OfferSection from "@/Components/ReusableComponents/OfferSection/OfferSection";
import ProductsListViewer from "@/Components/ReusableComponents/ProductsListViewer/ProductsListViewer";
import TitleViwerModern from "@/Components/ReusableComponents/TitleViewer/TitleViwerModern";
import TtileViewerMiddle from "@/Components/ReusableComponents/TitleViewer/TtileViewerMiddle";
import { ContextDataType, LocalDataContext } from "@/Context/LocalDataProvider";
import { ROUTES } from "@/Routes";
import bannerImage from "@/dummyImages/offer-banner/banner-common.jpg";
import { gtmDataLayerDataPass } from "@/lib/gtm";
import {
  getHomePageContentListApi,
  getHomePageProductListApi,
  getOfferProductListApi,
  SingleHomeContent,
} from "@/services/productsServices";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useContext, useEffect } from "react";
import BlogAndOVideoSection from "../BlogAndOVideoSection/BlogAndOVideoSection";
import CommonOfferSection from "../CommonOfferSection/CommonOfferSection";
import FeaturedCategoriesModern from "../FeaturedCategories/FeaturedCategoriesModern";
import LandingPageBanner from "../LandingPageBanner/LandingPageBanner";
import HomepageSingleContentWithApi from "./HomepageSingleContentWithApi";
import ProductsListSkeleton from "@/Components/ReusableComponents/Loaders/ProductListSkeleton";

interface Props {
  accessToken: string;
}

const LandingPageOverview = ({ accessToken }: Props) => {
  const router = useRouter();
  const { userHeaderDetails }: ContextDataType = useContext(LocalDataContext);

  const { api: offerApi, getKey: offerGetKey } = getHomePageProductListApi({
    productType: "skin care",
    size: "20",
  });
  const { data: offer, isLoading: isOfferLoading } = useQuery(
    offerGetKey(),
    offerApi,
    {
      refetchOnWindowFocus: false,
      onSuccess(data) { },
    },
  );

  const { api: homepageContentApi, getKey: homepageContentGetKey } =
    getHomePageContentListApi();
  const { data: homepageContent, isLoading: isHomepageContentLoading } =
    useQuery(homepageContentGetKey(), homepageContentApi, {
      refetchOnWindowFocus: false,
    });

  useEffect(() => {
    const data = {
      event: "page_view",
      url: window.location.href,
      value: 0,
      user_log_in_status: userHeaderDetails?.userDetails?.name
        ? "logged in"
        : "not logged in",
      user_id: userHeaderDetails?.userDetails?._id ?? "",
      user_name: userHeaderDetails?.userDetails?.name ?? "",
      user_phone_number: userHeaderDetails?.userDetails?.mobileNumber ?? "",
      user_email: userHeaderDetails?.userDetails?.email ?? "",
      user_zip: userHeaderDetails?.userDetails?.zipCode ?? "",
      user_district: userHeaderDetails?.userDetails?.district ?? "",
      items: null,
    };
    gtmDataLayerDataPass(data);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div>
      <div className="h-[22vh] sm:h-[40vh] md:h-[35vh] lg:h-[40vh] xl:h-[55vh] 2xl:h-[60vh]">
        <LandingPageBanner />
      </div>
      <div className="px-4 md:px-6  lg:px-20 xl:px-28">
        <div className="mt-2 md:mt-4">
          {/* <TitleViewer
            titleOne="Featured Categories"
            titleTwo=""
            seeAllButton={false}
            handleClick={() => router.push("/offers?page=1")}
          /> */}
          <TitleViwerModern
            titleOne="Featured"
            titleTwo="Categories"
            seeAllButton={true}
            handleClick={() => router.push("/offers?page=1")}
          />
          {/* <FeaturedCategories /> */}
          <FeaturedCategoriesModern />
        </div>
        <div className="mt-8">
          {/* <TitleViewer
            titleOne="Popular Product On"
            titleTwo="INNI"
            seeAllButton={true}
            handleClick={() => router.push("/products?page=1")}
          /> */}
          <TtileViewerMiddle
            titleOne="Popular Product"
            titleTwo="INNI"
          // seeAllButton={true}
          // handleClick={() => router.push("/products?page=1")}
          />
          <ProductsListViewer
            productList={offer?.results}
            loading={isOfferLoading}
            accessToken={accessToken}
            xxl={6}
            xl={5}
            lg={4}
            md={3}
            sm={2}
          />
          <ViewMoreButton handleClick={() => router.push("/products?page=1")} />
        </div>
        <OfferSection />
        <div>
          {!isHomepageContentLoading ? (
            homepageContent?.results?.map((single: SingleHomeContent) => (
              <HomepageSingleContentWithApi
                productType={single?.selectedType}
                imageUrl={single?.imageUrl}
                key={single?._id}
                accessToken={accessToken}
                productTypeSlug={single?.selectedOption}
                name={single?.name}
              />
            ))
          ) : (
            <ProductsListSkeleton />
          )}
        </div>
        {/* <div className="mt-8">
          <TitleViwerModern
            titleOne="Popular Product On"
            titleTwo="Offer"
            seeAllButton={true}
            handleClick={() => router.push("/offers?page=1")}
          />
          <ProductsListViewer
            productList={offer?.results}
            loading={isOfferLoading}
            accessToken={accessToken}
            xxl={6}
            xl={5}
            lg={4}
            md={3}
            sm={2}
          />
        </div> */}
        {/* <div className="mt-8">
          <CommonOfferSection image={bannerImage.src} />
        </div> */}
        <div className="mt-8">
          <BlogAndOVideoSection />
        </div>
        <div className="mt-8">
          <TitleViwerModern
            titleOne="Shop From"
            titleTwo="Brand"
            seeAllButton={true}
            handleClick={() => router.push(ROUTES.BRANDS.HOME)}
          />
          <BrandListCarousel />
        </div>
      </div>
    </div>
  );
};

export default LandingPageOverview;
