import Swal from "sweetalert2";

export const deleteModal = (type: string, handleDelete: () => void) => {
  Swal.fire({
    title: `Are you sure? you want to delete item from ${type}`,
    text: "You won't be able to revert this!",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Yes, delete it!",
  }).then((result) => {
    if (result.isConfirmed) {
      handleDelete();
    }
  });
};

export const notLoggedInModal = (goToLogin: () => void) => {
  Swal.fire({
    title: "Sorry You are not logged in",
    text: "Do you want to log in?",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Yes, Log in",
  }).then((result) => {
    if (result.isConfirmed) {
      goToLogin();
    }
  });
};

export const minimumQuantityModal = () => {
  Swal.fire({ icon: "error", title: "Minimum quantity should be one" });
};

export const maximumQuantityModal = () => {
  Swal.fire({
    icon: "error",
    title: "You can not add more than Five pcs at a time!!",
  });
};

export const addedOnCartModal = (
  buyMoreClick: () => void,
  goToCartClick: () => void,
) => {
  Swal.fire({
    title: "Product Added on cart successfully",
    showDenyButton: true,
    showCancelButton: true,
    confirmButtonText: "Buy More",
    denyButtonText: `Go To Cart`,
    icon: "success",
  }).then((result) => {
    if (result.isConfirmed) {
      buyMoreClick();
    } else if (result.isDenied) {
      goToCartClick();
    }
  });
};
