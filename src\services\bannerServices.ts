import { BASE_URL } from "@/environment/environment";
import axios from "axios";

export const getBannersApi = () => {
  return {
    api() {
      return axios
        .get<GetBannersResponse>(`${BASE_URL}/banners/get-all`)
        .then(({ data }) => data);
    },
    getKey() {
      return ["getBannersApi"];
    },
  };
};

export interface GetBannersResponse {
  message: string;
  results: SingleBanner[];
  status: boolean;
}

export interface SingleBanner {
  _id: string;
  imageUrl: string;
  redirectSlug: string;
  redirectType: string;
  bannerType: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}
