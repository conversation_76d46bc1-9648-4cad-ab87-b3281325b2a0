import { generateProductImage } from "@/Components/utils/GenerateProductImage";
import {
  maximumQuantityModal,
  minimumQuantityModal,
} from "@/Components/utils/commonModal";
import { LocalDataContext } from "@/Context/LocalDataProvider";
import { SingleLocalCartItem } from "@/Types/localCartTypes";
import Image from "next/image";
import { useContext, useState } from "react";
import { AiFillDelete } from "react-icons/ai";

interface Props {
  product: SingleLocalCartItem;
  handleSelectOrUnselectSingle: (product: SingleLocalCartItem) => void;
  selectedForCheckout: SingleLocalCartItem[];
  handleDeleteFromCart: (id: string) => void;
  isLastItem: boolean;
}

export const SingleItemOfCartTable = ({
  product,
  handleSelectOrUnselectSingle,
  selectedForCheckout,
  handleDeleteFromCart,
  isLastItem,
}: Props) => {
  const { handleUpdateSingleCartItem } = useContext(LocalDataContext);
  const isSelected = selectedForCheckout.some(
    (item: SingleLocalCartItem) => item.id === product.id,
  );
  const [quantity, setQuantity] = useState(product.quantity ?? 0);
  return (
    <>
      <tr key={product.id} className={`${!isLastItem && "border-b"} mb-4 pb-4`}>
        <td className=" py-2">
          <input
            type="checkbox"
            checked={isSelected}
            onClick={() => handleSelectOrUnselectSingle(product)}
          />
        </td>
        <td className="flex items-center px-4 py-2">
          <Image
            height={100}
            width={100}
            src={generateProductImage(product?.mainImageUrl)}
            alt={product.name}
            className="w-20"
          />
          <h3 className="ml-4 text-sm font-normal">{product.name}</h3>
        </td>
        <td className="px-4 py-2 text-lg font-semibold">
          ৳ {product.discountPrice}{" "}
          {product.price !== product.discountPrice ? (
            <span className="text-sm font-normal text-slate-500  line-through">
              ৳{product.price}
            </span>
          ) : (
            ""
          )}
        </td>
        <td className="px-4 py-2">
          <div className="border-primary mb-4 mt-2 flex w-[150px] items-center justify-between rounded-lg border-2">
            <button
              className="rounded-l-lg bg-gray-200 px-4 py-2"
              onClick={() => {
                if (quantity > 1) {
                  handleUpdateSingleCartItem(product.id, quantity - 1);
                  setQuantity(quantity - 1);
                } else {
                  minimumQuantityModal();
                }
              }}
            >
              -
            </button>
            <span className="mx-1">{quantity}</span>
            <button
              className="rounded-r-lg bg-gray-200 px-4 py-2"
              onClick={() => {
                if (quantity < 5) {
                  handleUpdateSingleCartItem(product.id, quantity + 1);
                  setQuantity(quantity + 1);
                } else {
                  maximumQuantityModal();
                }
              }}
            >
              +
            </button>
          </div>
        </td>
        <td className="px-4 py-2">
          <span className="text-center font-semibold">
            ৳{product.discountPrice * quantity}
          </span>
        </td>
        <td className="px-4 py-2 text-2xl text-red-500">
          <button onClick={() => handleDeleteFromCart(product.id)}>
            <AiFillDelete />
          </button>
        </td>
      </tr>
      {/* for small screen */}
    </>
  );
};
export const SingleItemOfCartCard = ({
  product,
  handleSelectOrUnselectSingle,
  selectedForCheckout,
  handleDeleteFromCart,
  isLastItem,
}: Props) => {
  const { handleUpdateSingleCartItem } = useContext(LocalDataContext);
  const isSelected = selectedForCheckout.some(
    (item: SingleLocalCartItem) => item.id === product.id,
  );
  const [quantity, setQuantity] = useState(product.quantity ?? 0);
  return (
    <div className={`mb-4  flex ${!isLastItem && "border-b"} pb-4  md:hidden`}>
      {/* Left side - Image */}
      <div className="mr-4 w-20 ">
        <input
          type="checkbox"
          checked={isSelected}
          onClick={() => handleSelectOrUnselectSingle(product)}
        />
        <Image
          height={100}
          width={100}
          src={generateProductImage(product?.mainImageUrl)}
          alt={product.name}
          className="h-auto w-full rounded-md"
        />
      </div>

      {/* Right side - Product Details */}
      <div className="flex-1 ">
        <div className="flex items-start justify-between gap-1">
          <h3 className="text-sm font-normal">{product.name}</h3>
          <div className="text-2xl text-red-500">
            <button onClick={() => handleDeleteFromCart(product.id)}>
              <AiFillDelete />
            </button>
          </div>
        </div>
        <div className="my-2 flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-normal text-gray-600">Price :</span>
            <span className="text-lg font-semibold">
              ৳ {product.discountPrice}{" "}
              {product.price !== product.discountPrice ? (
                <span className="text-sm text-slate-500 line-through">
                  ৳{product.price}
                </span>
              ) : (
                ""
              )}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-normal text-gray-600">
              Quantity :
            </span>
            <div className="border-primary mb-4 mt-2 flex w-[150px] items-center justify-between rounded-lg border-2">
              <button
                className="rounded-l-lg bg-gray-200 px-4 py-2"
                onClick={() => {
                  if (quantity > 1) {
                    handleUpdateSingleCartItem(product.id, quantity - 1);
                    setQuantity(quantity - 1);
                  } else {
                    minimumQuantityModal();
                  }
                }}
              >
                -
              </button>
              <span className="mx-1">{quantity}</span>
              <button
                className="rounded-r-lg bg-gray-200 px-4 py-2"
                onClick={() => {
                  if (quantity < 5) {
                    handleUpdateSingleCartItem(product.id, quantity + 1);
                    setQuantity(quantity + 1);
                  } else {
                    maximumQuantityModal();
                  }
                }}
              >
                +
              </button>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-normal text-gray-600">
              Subtotal :
            </span>
            <span className="text-lg font-semibold">
              ৳ {product.discountPrice * quantity}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
